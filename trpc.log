2025-05-21 11:33:56.239	INFO	trpc-naming-polaris@v0.5.24/naming.go:225	naming-polaris starts to set polaris-go logs config: &{DirPath:$HOME/polaris/log Level:default MaxBackups:5 MaxSize:50}
2025-05-21 11:33:56.239	INFO	trpc-naming-polaris@v0.5.24/naming.go:231	naming-polaris starts to set polaris-go logs level: false
2025-05-21 11:33:56.254	INFO	server/service.go:207	process: 3462019, http service: trpc.zy_socail.qq_photos.QzonePhotoTool launch success, tcp: 127.0.0.1:6610, serving ...
2025-05-21 11:34:38.737	ERROR	debuglog@v0.1.13/log.go:233	client request:, cost:1.53339267s, to:30.176.224.185:5743, err:type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-21 11:34:38.737	ERROR	repo/qzone_photo_tool_repo.go:57	qzone proxy AlbumList failed,  err=type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-21 11:34:38.737	ERROR	debuglog@v0.1.13/log.go:196	server request:/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool, cost:1.533637862s, from:127.0.0.1:55870, err:type:business, code:-380550, msg:, caused by type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid, total timeout:999.793622ms
2025-05-21 17:03:44.948	INFO	trpc-naming-polaris@v0.5.24/naming.go:225	naming-polaris starts to set polaris-go logs config: &{DirPath:$HOME/polaris/log Level:default MaxBackups:5 MaxSize:50}
2025-05-21 17:03:44.948	INFO	trpc-naming-polaris@v0.5.24/naming.go:231	naming-polaris starts to set polaris-go logs level: false
2025-05-21 17:03:44.963	INFO	server/service.go:207	process: 3634237, http service: trpc.zy_socail.qq_photos.QzonePhotoTool launch success, tcp: 127.0.0.1:6610, serving ...
2025-05-21 17:03:53.613	ERROR	debuglog@v0.1.13/log.go:233	client request:, cost:1.530797635s, to:30.176.224.185:5743, err:type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-21 17:03:53.613	ERROR	repo/qzone_photo_tool_repo.go:57	qzone proxy AlbumList failed,  err=type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-21 17:03:53.613	ERROR	debuglog@v0.1.13/log.go:196	server request:/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool, cost:1.530985928s, from:127.0.0.1:47270, err:type:business, code:-380550, msg:, caused by type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid, total timeout:999.84065ms
2025-05-23 15:04:29.653	INFO	trpc-naming-polaris@v0.5.24/naming.go:225	naming-polaris starts to set polaris-go logs config: &{DirPath:$HOME/polaris/log Level:default MaxBackups:5 MaxSize:50}
2025-05-23 15:04:29.653	INFO	trpc-naming-polaris@v0.5.24/naming.go:231	naming-polaris starts to set polaris-go logs level: false
2025-05-23 15:04:29.669	INFO	server/service.go:207	process: 912615, http service: trpc.zy_socail.qq_photos.QzonePhotoTool launch success, tcp: 127.0.0.1:6610, serving ...
2025-05-23 15:24:09.080	ERROR	debuglog@v0.1.13/log.go:233	client request:, cost:1.825787952s, to:30.176.225.108:5743, err:type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 15:24:43.661	ERROR	repo/qzone_photo_tool_repo.go:57	qzone proxy AlbumList failed,  err=type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 15:24:43.661	ERROR	debuglog@v0.1.13/log.go:196	server request:/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool, cost:19m20.663142277s, from:127.0.0.1:56462, err:type:business, code:-380550, msg:, caused by type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid, total timeout:999.836914ms
2025-05-23 16:10:14.275	INFO	trpc-naming-polaris@v0.5.24/naming.go:225	naming-polaris starts to set polaris-go logs config: &{DirPath:$HOME/polaris/log Level:default MaxBackups:5 MaxSize:50}
2025-05-23 16:10:14.275	INFO	trpc-naming-polaris@v0.5.24/naming.go:231	naming-polaris starts to set polaris-go logs level: false
2025-05-23 16:10:14.290	INFO	server/service.go:207	process: 953475, http service: trpc.zy_socail.qq_photos.QzonePhotoTool launch success, tcp: 127.0.0.1:6610, serving ...
2025-05-23 16:10:48.892	ERROR	debuglog@v0.1.13/log.go:233	client request:, cost:1.523125155s, to:11.142.18.88:5743, err:type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:10:48.892	ERROR	repo/qzone_photo_tool_repo.go:57	qzone proxy AlbumList failed,  err=type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:10:48.892	ERROR	debuglog@v0.1.13/log.go:196	server request:/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool, cost:1.523293291s, from:127.0.0.1:52340, err:type:business, code:-380550, msg:, caused by type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid, total timeout:999.839819ms
2025-05-23 16:11:26.889	ERROR	debuglog@v0.1.13/log.go:233	client request:, cost:1.509589484s, to:11.142.41.77:5743, err:type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:11:26.889	ERROR	repo/qzone_photo_tool_repo.go:57	qzone proxy AlbumList failed,  err=type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:11:26.889	ERROR	debuglog@v0.1.13/log.go:196	server request:/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool, cost:1.509762248s, from:127.0.0.1:42888, err:type:business, code:-380550, msg:, caused by type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid, total timeout:999.912164ms
2025-05-23 16:23:08.213	INFO	trpc-naming-polaris@v0.5.24/naming.go:225	naming-polaris starts to set polaris-go logs config: &{DirPath:$HOME/polaris/log Level:default MaxBackups:5 MaxSize:50}
2025-05-23 16:23:08.214	INFO	trpc-naming-polaris@v0.5.24/naming.go:231	naming-polaris starts to set polaris-go logs level: false
2025-05-23 16:23:08.231	INFO	server/service.go:207	process: 961545, http service: trpc.zy_socail.qq_photos.QzonePhotoTool launch success, tcp: 127.0.0.1:6610, serving ...
2025-05-23 16:25:14.567	ERROR	debuglog@v0.1.13/log.go:233	client request:, cost:1.525237176s, to:11.142.18.88:5743, err:type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:25:14.567	ERROR	repo/qzone_photo_tool_repo.go:57	qzone proxy AlbumList failed,  err=type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:25:14.567	ERROR	debuglog@v0.1.13/log.go:196	server request:/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool, cost:1.525434597s, from:127.0.0.1:48884, err:type:business, code:-380550, msg:, caused by type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid, total timeout:999.85689ms
2025-05-23 16:28:34.720	ERROR	debuglog@v0.1.13/log.go:233	client request:, cost:1.507219169s, to:30.182.124.156:5743, err:type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:28:34.720	ERROR	repo/qzone_photo_tool_repo.go:57	qzone proxy AlbumList failed,  err=type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid
2025-05-23 16:28:34.721	ERROR	debuglog@v0.1.13/log.go:196	server request:/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool, cost:1.50743148s, from:127.0.0.1:42264, err:type:business, code:-380550, msg:, caused by type:framework, code:171, msg:tcp client transport ReadFrame, caused by read msg header stx invalid, total timeout:999.908108ms
2025-05-26 20:42:02.703	INFO	trpc-naming-polaris@v0.5.24/naming.go:225	naming-polaris starts to set polaris-go logs config: &{DirPath:$HOME/polaris/log Level:default MaxBackups:5 MaxSize:50}
2025-05-26 20:42:02.703	INFO	trpc-naming-polaris@v0.5.24/naming.go:231	naming-polaris starts to set polaris-go logs level: false
2025-05-26 20:42:02.710	INFO	server/service.go:207	process: 3504057, http service: trpc.zy_socail.qq_photos.QzonePhotoTool launch success, tcp: 127.0.0.1:6610, serving ...
