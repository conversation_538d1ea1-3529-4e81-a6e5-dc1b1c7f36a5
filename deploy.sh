#!/bin/bash

# 服务器端部署脚本 - OpenCV 优化版本

set -e

# 配置变量
IMAGE_NAME="output_error_qq_photos"
CONTAINER_NAME="output_error_qq_photos"
TAG="${1:-latest}"
TAR_FILE="${IMAGE_NAME}_${TAG}.tar"
PORT="${2:-8080}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未启动，请启动 Docker 服务"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 检查镜像文件
check_image_file() {
    # 检查压缩文件或原始文件
    if [ -f "${TAR_FILE}.gz" ]; then
        DEPLOY_FILE="${TAR_FILE}.gz"
        LOAD_CMD="gunzip -c ${DEPLOY_FILE} | docker load"
        log_success "找到压缩镜像文件: ${DEPLOY_FILE}"
    elif [ -f "$TAR_FILE" ]; then
        DEPLOY_FILE="$TAR_FILE"
        LOAD_CMD="docker load -i ${DEPLOY_FILE}"
        log_success "找到镜像文件: ${DEPLOY_FILE}"
    else
        log_error "镜像文件不存在: $TAR_FILE 或 ${TAR_FILE}.gz"
        log_info "请确保已将镜像文件传输到当前目录"
        log_info "支持的文件格式: .tar 或 .tar.gz"
        exit 1
    fi
}

# 停止并删除旧容器
cleanup_old_container() {
    log_info "清理旧容器..."
    
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "停止旧容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" || true
        
        log_info "删除旧容器: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME" || true
    fi
    
    log_success "旧容器清理完成"
}

# 加载镜像
load_image() {
    log_info "加载 Docker 镜像..."
    log_info "执行命令: ${LOAD_CMD}"
    
    if eval "${LOAD_CMD}"; then
        log_success "镜像加载成功"
        
        # 验证镜像是否正确加载
        if docker images | grep -q "$IMAGE_NAME"; then
            log_success "镜像验证通过"
        else
            log_error "镜像加载后未找到，请检查"
            exit 1
        fi
    else
        log_error "镜像加载失败"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p config
    
    # 如果 application.yaml 不存在，创建一个示例
    if [ ! -f "application.yaml" ]; then
        log_warning "application.yaml 不存在，创建示例配置文件"
        cat > application.yaml << EOF
util:
  phash:
    distance_threshold: 10
EOF
    fi
    
    log_success "目录创建完成"
}

# 启动容器
start_container() {
    log_info "启动新容器..."
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$PORT:8080" \
        -v "$(pwd)/application.yaml:/app/application.yaml:ro" \
        -v "$(pwd)/logs:/app/logs" \
        -e LD_LIBRARY_PATH=/usr/local/lib \
        -e PKG_CONFIG_PATH=/usr/local/lib/pkgconfig \
        --restart unless-stopped \
        --log-driver json-file \
        --log-opt max-size=100m \
        --log-opt max-file=3 \
        "$IMAGE_NAME:$TAG"
    
    if [ $? -eq 0 ]; then
        log_success "容器启动成功"
    else
        log_error "容器启动失败"
        exit 1
    fi
}

# 检查容器状态和 OpenCV 依赖
check_container_status() {
    log_info "检查容器状态..."
    
    sleep 10  # 等待容器完全启动
    
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_success "容器运行正常"
        
        # 显示容器信息
        echo ""
        log_info "容器信息:"
        docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        # 检查 OpenCV 依赖
        log_info "检查 OpenCV 依赖..."
        docker exec "$CONTAINER_NAME" /bin/bash -c "
            echo '=== OpenCV 库文件检查 ==='
            find /usr/local/lib -name '*opencv*' | head -5
            echo ''
            echo '=== 环境变量检查 ==='
            echo 'LD_LIBRARY_PATH: '\$LD_LIBRARY_PATH
            echo ''
            echo '=== 程序依赖检查 ==='
            ldd /app/output_error_qq_photos | grep -E '(opencv|not found)' | head -5 || echo '依赖检查完成'
        " 2>/dev/null || log_warning "OpenCV 依赖检查失败，但容器正在运行"
        
        echo ""
        log_info "最近的日志:"
        docker logs --tail 20 "$CONTAINER_NAME"
        
    else
        log_error "容器启动失败，查看日志:"
        docker logs "$CONTAINER_NAME"
        
        # 尝试诊断问题
        log_info "尝试诊断启动问题..."
        docker run --rm "$IMAGE_NAME:$TAG" /bin/bash -c "
            echo '=== 检查程序文件 ==='
            ls -la /app/
            echo ''
            echo '=== 检查库依赖 ==='
            ldd /app/output_error_qq_photos | head -10
            echo ''
            echo '=== 尝试运行程序 ==='
            timeout 5s /app/output_error_qq_photos --help 2>&1 || echo '程序测试完成'
        " 2>/dev/null || log_error "诊断失败"
        
        exit 1
    fi
}

# 显示管理命令
show_management_commands() {
    echo ""
    log_success "部署完成！管理命令:"
    echo ""
    echo "🔍 查看日志:"
    echo "   docker logs -f $CONTAINER_NAME"
    echo ""
    echo "📊 查看容器状态:"
    echo "   docker ps"
    echo "   docker stats $CONTAINER_NAME"
    echo ""
    echo "⚙️  进入容器:"
    echo "   docker exec -it $CONTAINER_NAME /bin/bash"
    echo ""
    echo "🔄 重启容器:"
    echo "   docker restart $CONTAINER_NAME"
    echo ""
    echo "🛑 停止容器:"
    echo "   docker stop $CONTAINER_NAME"
    echo ""
    echo "🗑️  删除容器:"
    echo "   docker rm -f $CONTAINER_NAME"
    echo ""
    echo "🌐 访问应用:"
    echo "   http://localhost:$PORT"
    echo ""
    echo "🔧 OpenCV 故障排查:"
    echo "   # 检查 OpenCV 库"
    echo "   docker exec $CONTAINER_NAME find /usr/local/lib -name '*opencv*'"
    echo "   # 检查程序依赖"
    echo "   docker exec $CONTAINER_NAME ldd /app/output_error_qq_photos"
    echo "   # 检查环境变量"
    echo "   docker exec $CONTAINER_NAME env | grep -E '(LD_LIBRARY_PATH|PKG_CONFIG_PATH)'"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "  Docker 部署脚本 - OpenCV 优化版本"
    echo "========================================"
    echo ""
    
    check_docker
    check_image_file
    cleanup_old_container
    load_image
    create_directories
    start_container
    check_container_status
    show_management_commands
    
    log_success "部署完成！"
}

# 脚本使用说明
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "使用方法: $0 [TAG] [PORT]"
    echo ""
    echo "参数:"
    echo "  TAG     镜像标签 (默认: latest)"
    echo "  PORT    服务端口 (默认: 8080)"
    echo ""
    echo "示例:"
    echo "  $0                # 部署 latest 标签，端口 8080"
    echo "  $0 v1.0.0         # 部署 v1.0.0 标签，端口 8080"
    echo "  $0 latest 9090    # 部署 latest 标签，端口 9090"
    echo ""
    echo "前置条件:"
    echo "  1. 确保 Docker 已安装并运行"
    echo "  2. 确保镜像文件在当前目录:"
    echo "     - ${IMAGE_NAME}_[TAG].tar 或"
    echo "     - ${IMAGE_NAME}_[TAG].tar.gz"
    echo ""
    echo "特性:"
    echo "  ✅ 支持压缩镜像文件"
    echo "  ✅ 自动配置 OpenCV 环境变量"
    echo "  ✅ 容器健康检查"
    echo "  ✅ 详细的故障排查指导"
    exit 0
fi

# 执行主函数
main
