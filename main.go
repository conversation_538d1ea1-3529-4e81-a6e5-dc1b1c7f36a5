// Package main 是由 trpc-go-cmdline v2.8.31 生成的服务端示例代码
// 注意：本文件并非必须存在，而仅为示例，用户应按需进行修改使用，如不需要，可直接删去

package main

import (
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.woa.com/QzonePlatform/intimate-space/backend/common/codec/psa"
	"git.woa.com/fanniliu/output_error_qq_photos/internal/app_config"
	"git.woa.com/fanniliu/output_error_qq_photos/internal/service"
	pb "git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

func main() {
	// 加载应用配置
	if err := app_config.LoadConfig("./application.yaml"); err != nil {
		log.Fatal("加载配置文件失败:", err)
	}

	s := trpc.NewServer()

	impl := service.NewQzonePhotoToolImpl()
	pb.RegisterQzonePhotoToolService(s, impl)
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
