package tools

import (
	"context"
	"flag"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.woa.com/trpc-go/tnet/log"
	"git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

type paramType struct {
	target       string // 目标TRPC服务名，例如 "aigc.access.AigcAccess"
	namespace    string // TRPC服务命名空间，例如 "Production"
	timeout      int    // 请求超时时间（毫秒）
	logFile      string // 日志文件名，如果为空则输出到标准输出
	logLevel     string // 日志级别，例如 "debug", "info", "error"
	taskListFile string // 包含任务列表的文件路径，每行一个任务参数
	taskLine     string // 单个任务行内容，如果只执行一个任务则使用此参数
	concurrency  int    // 并发协程数量，控制同时处理的任务数
	startGap     int    // 任务启动间隔（毫秒），用于平滑启动，避免瞬时大量请求
	startAt      int    // 从任务文件中的第几行开始处理（用于断点续传）
}

var param = paramType{}

// 初始化命令行参数
func initArgs() {
	flag.StringVar(&param.target, "target", "", "access service target")
	flag.StringVar(&param.namespace, "namespace", "Production", "access service namespace")
	flag.IntVar(&param.timeout, "timeout", 30000, "req access timeout time, ms")
	flag.StringVar(&param.logFile, "logFile", "", "log filename")
	flag.StringVar(&param.logLevel, "logLevel", "debug", "log level")
	flag.StringVar(&param.taskListFile, "taskListFile", "", "a file with task line")
	flag.StringVar(&param.taskLine, "taskLine", "", "a line of op task")
	flag.IntVar(&param.concurrency, "concurrency", 1, "task concurrency")
	flag.IntVar(&param.startGap, "startGap", 1000, "task start gap, ms")
	flag.IntVar(&param.startAt, "startAt", 0, "start at witch line")
}

// makeClientOptions 构造client选项
func makeClientOptions(node *registry.Node) []client.Option {
	options := []client.Option{
		client.WithSelectorNode(node),                                       // 指定选择器节点，如果node为空，TRPC会使用服务发现机制
		client.WithProtocol("http"),                                         // 使用HTTP协议进行通信
		client.WithTarget(param.target),                                     // 目标服务名
		client.WithNamespace(param.namespace),                               // 服务命名空间
		client.WithTimeout(time.Duration(param.timeout) * time.Millisecond), // 请求超时时间
	}
	return options
}

func rawFixTool(uin int) (int, *qq_photos.RawFixToolReply) {
	rawFixToolRequest := &qq_photos.RawFixToolRequest{
		Id: uint64(uin),
	}
	proxy := qq_photos.NewQzonePhotoToolClientProxy()
	node := &registry.Node{}
	rawFixToolReply, err := proxy.RawFixTool(context.Background(), rawFixToolRequest, makeClientOptions(node)...)
	if err != nil {
		log.Errorf("rawFixTool failed, uin=%s, node=%v, err=%v", uin, node, err)
		return -1, nil
	}

	return 0, rawFixToolReply
}

func main() {
	initArgs()
	flag.Parse()
}
