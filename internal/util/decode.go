package util

import (
	"encoding/base64"
	"encoding/binary"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

// ParseQzoneCloudLLOC 解析qzone lloc
func ParseQzoneCloudLLOC(lloccode string) (photoID, bucketID string, err error) {
	// 解url safe的base64
	lloc, err := base64.StdEncoding.DecodeString(unescape(lloccode))
	if err != nil {
		return "", "", err
	}
	const encodeTypeCloud byte = '5'
	// lloc规则：type(uint8) + len(photoID)(uint16) + photoID + len(bucketID)(uint16) + bucketID
	if len(lloc) < 8 {
		return "", "", errors.New("lloc too short")
	}
	typeByte := lloc[0]
	if typeByte != encodeTypeCloud {
		return "", "", errors.New("lloc type not match")
	}
	photoIDLen := int(binary.LittleEndian.Uint16([]byte(lloc[1:3])))
	if len(lloc) < photoIDLen+5 {
		return "", "", errors.New("photoIDLen too short")
	}
	photoID = string(lloc[3 : photoIDLen+3])
	bucketIDLen := int(binary.LittleEndian.Uint16([]byte(lloc[photoIDLen+3:])))
	if len(lloc) < photoIDLen+bucketIDLen+5 {
		return "", "", errors.New("bucketIDLen too short")
	}
	bucketID = string(lloc[photoIDLen+5:])
	if bucketIDLen != len(bucketID) {
		return "", "", errors.New("bucketIDLen not match")
	}
	return photoID, bucketID, nil
}

func unescape(s string) string {
	s = strings.ReplaceAll(s, "*", "/")
	s = strings.ReplaceAll(s, "!", "=")
	s = strings.ReplaceAll(s, ".", "+")
	return s
}

// ParsePhotoID 解析自定义的 photoid 字符串。
// 它返回 uin, uploadtime, clocktime, openid，以及解析过程中可能遇到的错误。
func ParsePhotoID(photoid string) (uin uint32, uploadtime uint32, clocktime uint32, openid string, err error) {
	uin = 0
	uploadtime = 0
	clocktime = 0
	openid = ""

	if len(photoid) < 3 {
		return 0, 0, 0, "", errors.New("photoid string too short")
	}

	version := photoid[1] // photoid[1] 返回 byte 类型

	// Base64解码 photoid 中从第4个字符开始的部分 (photoid[3:] in Go)
	decodedBytes, err := base64.StdEncoding.DecodeString(unescape(photoid[3:]))
	if err != nil {
		return 0, 0, 0, "", fmt.Errorf("base64 decode failed: %w", err)
	}

	// 如果原始系统是大端序，需要改为 binary.BigEndian。
	byteOrder := binary.LittleEndian

	if version < '5' {
		// 版本 < '5' 的数据结构: uin(4 bytes) + uploadtime(4 bytes) + clocktime(4 bytes) = 12 bytes
		if len(decodedBytes) < 12 {
			return 0, 0, 0, "", errors.New("decoded data too short for version < '5'")
		}

		uin = byteOrder.Uint32(decodedBytes[0:4])
		uploadtime = byteOrder.Uint32(decodedBytes[4:8])
		clocktime = byteOrder.Uint32(decodedBytes[8:12])
		openid = ""
	} else {
		// 版本 >= '5' 的数据结构: openid_len(2 bytes) + openid(variable) + uploadtime(4 bytes) + clocktime(4 bytes)

		// 检查是否有足够的字节读取 openid_len
		if len(decodedBytes) < 2 {
			return 0, 0, 0, "", errors.New("decoded data too short to read openid_len for version >= '5'")
		}

		openidLen := byteOrder.Uint16(decodedBytes[0:2])

		// 计算所需总长度
		// 2 (openid_len) + openidLen (openid数据) + 4 (uploadtime) + 4 (clocktime)
		requiredLen := int(openidLen) + 2 + 4 + 4

		if len(decodedBytes) < requiredLen {
			return 0, 0, 0, "", fmt.Errorf("decoded data too short for version >= '5'. Expected at least %d bytes, got %d", requiredLen, len(decodedBytes))
		}

		// 解析 openid
		openid = string(decodedBytes[2 : 2+openidLen])

		// 解析 uploadtime
		uploadtimeOffset := 2 + openidLen
		uploadtime = byteOrder.Uint32(decodedBytes[uploadtimeOffset : uploadtimeOffset+4])

		// 解析 clocktime
		clocktimeOffset := uploadtimeOffset + 4
		clocktime = byteOrder.Uint32(decodedBytes[clocktimeOffset : clocktimeOffset+4])

		// 20191028业务要求尝试用openid填充uin字段
		// 在解析失败时将 uin 设为0，与 strtoul 行为类似，并打印一个警告。
		parsedUIN, parseErr := strconv.ParseUint(openid, 10, 32)
		if parseErr != nil {
			// 如果openid不是有效的数字，uin会是0
			fmt.Printf("Warning: openid '%s' could not be converted to uin: %v\n", openid, parseErr)
			uin = 0
		} else {
			uin = uint32(parsedUIN)
		}
	}

	return uin, uploadtime, clocktime, openid, nil // 成功解析
}
