package util

import (
	"fmt"
	"image"
	"strings"

	"gocv.io/x/gocv"
)

// phash 计算图像的感知哈希 (pHash)。
// 它返回哈希字符串，并在任何步骤失败时返回错误。
func phash(img gocv.Mat) (string, error) {
	// 第一步：缩放至32x32，转换为灰度图，然后转为float32类型
	resized := gocv.NewMat()
	// 通常，对于图像缩小，使用InterpolationArea插值方法效果较好
	gocv.Resize(img, &resized, image.Point{32, 32}, 0, 0, gocv.InterpolationArea)
	defer resized.Close() // 重要：释放GoCV Mat资源，防止内存泄漏

	gray := gocv.NewMat()
	gocv.CvtColor(resized, &gray, gocv.ColorBGRToGray)
	defer gray.Close()

	// 转换为float32类型以便进行DCT
	floatMat := gocv.NewMat()
	gray.ConvertTo(&floatMat, gocv.MatTypeCV32F)
	defer floatMat.Close()

	// 第二步：执行离散余弦变换 (DCT) 并裁剪至8x8
	dctMat := gocv.NewMat()
	// gocv.DCT在flags为0时默认执行2D DCT
	gocv.DCT(floatMat, &dctMat, 0)
	defer dctMat.Close()

	// 裁剪左上角8x8的块。
	// 这会创建一个“区域”或“子矩阵”视图，不复制数据。
	// 只要父级dctMat在使用cropped之后再关闭，就不会有问题。
	cropped := dctMat.Region(image.Rect(0, 0, 8, 8))
	// cropped 是 dctMat 的一个视图，所以不需要单独 defer Close()。

	// 第三步：计算8x8 DCT块的平均值
	var sum float32
	for r := 0; r < 8; r++ {
		for c := 0; c < 8; c++ {
			// GetFloatAt 获取 (行, 列) 位置的float32值
			sum += cropped.GetFloatAt(r, c)
		}
	}
	avg := sum / 64.0

	// 第四步：根据与平均值的比较生成哈希字符串
	var hashBuilder strings.Builder // 使用 strings.Builder 进行高效的字符串拼接
	for r := 0; r < 8; r++ {
		for c := 0; c < 8; c++ {
			if cropped.GetFloatAt(r, c) > avg {
				hashBuilder.WriteString("1")
			} else {
				hashBuilder.WriteString("0")
			}
		}
	}

	return hashBuilder.String(), nil
}

// hmdistance 计算两个哈希字符串之间的汉明距离。
func hmdistance(hash1, hash2 string) (int, error) {
	if len(hash1) != len(hash2) {
		return 0, fmt.Errorf("哈希字符串长度必须相等: %d vs %d", len(hash1), len(hash2))
	}

	num := 0
	for i := 0; i < len(hash1); i++ {
		if hash1[i] != hash2[i] {
			num++
		}
	}
	return num, nil
}

func CompareImagesByPhash(bigPic, rawPic gocv.Mat, threshold int) (distance int, similar bool, err error) {
	defer bigPic.Close()
	defer rawPic.Close()

	// 将图像转换为灰度图
	grayBigPic := gocv.NewMat()
	gocv.CvtColor(bigPic, &grayBigPic, gocv.ColorBGRToGray)
	defer grayBigPic.Close()

	grayRawPic := gocv.NewMat()
	gocv.CvtColor(rawPic, &grayRawPic, gocv.ColorBGRToGray)
	defer grayRawPic.Close()

	// 计算两个图像的哈希值
	hashBigPic, err := phash(grayBigPic)
	if err != nil {
		return 0, false, err
	}
	hashRawPic, err := phash(grayRawPic)
	if err != nil {
		return 0, false, err
	}

	// 计算汉明距离
	distance, err = hmdistance(hashBigPic, hashRawPic)
	if err != nil {
		return 0, false, err
	}

	// 根据阈值判断是否相似
	if distance <= threshold {
		return distance, true, nil
	}
	return distance, false, nil
}
