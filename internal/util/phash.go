package util

import (
	"image"

	"github.com/corona10/goimagehash"
)

// todo 要自己定threshold
func CompareImagesFromImage(img1, img2 image.Image, threshold int) (distance int, similar bool, err error) {
	// 生成 pHash
	hash1, err := goimagehash.PerceptionHash(img1)
	if err != nil {
		return 0, false, err
	}

	hash2, err := goimagehash.PerceptionHash(img2)
	if err != nil {
		return 0, false, err
	}

	// 计算汉明距离
	distance, _ = hash1.Distance(hash2)
	// 判断差异度（汉明距离 ≤ threshold 为相似）
	similar = distance <= threshold

	return distance, similar, nil
}
