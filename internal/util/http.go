package util

import (
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"gocv.io/x/gocv"
)

// downloadImage 从URL下载图片并解码为image.Image
func DownloadImage(url string) (gocv.Mat, error) {
	start := time.Now()
	//todo 最大重试次数、最少重试间隔、随机时间间隔 后面要配置
	var downloadImageMaxRetry int = 3
	baseSleep := time.Millisecond * 800
	randSleepTime := 1500

	for i := 0; i < downloadImageMaxRetry; i++ {
		// 创建HTTP客户端，设置超时
		client := &http.Client{
			Timeout: 30 * time.Second,
		}
		// 发送GET请求
		resp, err := client.Get(url)
		if err != nil {
			log.Errorf("下载图片失败，重试次数: %d, URL: %s, err: %v", i, url, err)
			continue
		}

		func() {
			defer resp.Body.Close()

			//检查Retcode字段是否存在，如果存在就是qq服务器过于繁忙，返回了默认图片
			if retcode := resp.Header.Get("Retcode"); retcode != "" {
				log.Errorf("请求过于频繁，返回了默认图片，重试次数: %d, URL: %s, err: %v", i, url, err)
				sleepTime := baseSleep + time.Duration(rand.Intn(randSleepTime))*time.Millisecond
				time.Sleep(sleepTime)
				continue
			}

			// 读取响应体内容
			bodyBytes, err := io.ReadAll(resp.Body)
			if err != nil {
				return gocv.Mat{}, fmt.Errorf("读取响应体失败: %w", err)
			}

			// 尝试解码图片
			mat, err := gocv.IMDecode(bodyBytes, gocv.IMReadColor)

			if err != nil {
				return gocv.Mat{}, fmt.Errorf("gocv.IMDecode失败: %w", err)
			}

			timeCost := time.Since(start)
			log.Debugf("下载图片完成，耗时: %v, URL: %s", timeCost, url)

			return mat, nil
		}()
	}

	return gocv.Mat{}, fmt.Errorf("下载图片失败，重试次数超过最大限制")
}
