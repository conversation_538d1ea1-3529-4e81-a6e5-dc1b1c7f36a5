package util

import (
	"bytes"
	"fmt"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"time"
)

// downloadImage 从URL下载图片并解码为image.Image
func DownloadImage(url string) (image.Image, error) {
	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 发送GET请求
	resp, err := client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 检查Content-Type
	// contentType := resp.Header.Get("Content-Type")
	// log.Infof("下载图片 Content-Type: %s, URL: %s", contentType, url)

	// 读取响应体内容
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 如果内容为空或太小，可能不是有效图片
	// if len(bodyBytes) < 100 {
	// 	return nil, fmt.Errorf("响应内容太小(大小:%d)，可能不是有效图片", len(bodyBytes))
	// }

	// 尝试检测图片格式
	// contentFormat := http.DetectContentType(bodyBytes)
	// log.Infof("检测到的内容格式: %s, 大小: %d bytes", contentFormat, len(bodyBytes))

	// 创建一个新的Reader
	reader := bytes.NewReader(bodyBytes)

	// 注册所有标准图片格式
	image.RegisterFormat("jpeg", "jpeg", jpeg.Decode, jpeg.DecodeConfig)
	image.RegisterFormat("png", "png", png.Decode, png.DecodeConfig)
	image.RegisterFormat("gif", "gif", gif.Decode, gif.DecodeConfig)

	// 尝试解码图片
	img, format, err := image.Decode(reader)
	if err != nil {
		// 保存前几个字节用于调试
		prefix := bodyBytes
		if len(bodyBytes) > 50 {
			prefix = bodyBytes[:50]
		}
		return nil, fmt.Errorf("解码图片失败: %w, 格式: %s, 前缀: %x", err, format, prefix)
	}

	// log.Infof("成功解码图片，格式: %s", format)
	return img, nil
}
