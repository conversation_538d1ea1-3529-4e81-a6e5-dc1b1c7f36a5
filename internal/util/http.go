package util

import (
	"bytes"
	"fmt"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// downloadImage 从URL下载图片并解码为image.Image
func DownloadImage(url string) (image.Image, error) {
	start := time.Now()
	//todo 最大重试次数、最少重试间隔、随机时间间隔 后面要配置
	var downloadImageMaxRetry int = 3
	baseSleep := time.Millisecond * 800
	randSleepTime := 1500

	for i := 0; i < downloadImageMaxRetry; i++ {
		// 创建HTTP客户端，设置超时
		client := &http.Client{
			Timeout: 30 * time.Second,
		}
		// 发送GET请求
		resp, err := client.Get(url)
		if err != nil {
			log.Errorf("下载图片失败，重试次数: %d, URL: %s, err: %v", i, url, err)
			return nil, err
		}
		defer resp.Body.Close()

		//检查Retcode字段是否存在，如果存在就是qq服务器过于繁忙，返回了默认图片  todo可以搞一个休息后重试
		if retcode := resp.Header.Get("Retcode"); retcode != "" {
			return nil, fmt.Errorf("Retcode exist,Retcode: %s, URL: %s", retcode, url)
		}

		// 读取响应体内容
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("读取响应体失败: %w", err)
		}

		// 创建一个新的Reader
		reader := bytes.NewReader(bodyBytes)

		// 注册所有标准图片格式
		image.RegisterFormat("jpeg", "jpeg", jpeg.Decode, jpeg.DecodeConfig)
		image.RegisterFormat("png", "png", png.Decode, png.DecodeConfig)
		image.RegisterFormat("gif", "gif", gif.Decode, gif.DecodeConfig)

		// 尝试解码图片
		img, format, err := image.Decode(reader)
		if err != nil {
			// 保存前几个字节用于调试
			prefix := bodyBytes
			if len(bodyBytes) > 50 {
				prefix = bodyBytes[:50]
			}
			return nil, fmt.Errorf("解码图片失败: %w, 格式: %s, 前缀: %x", err, format, prefix)
		}

		timeCost := time.Since(start)
		log.Debugf("下载图片完成，耗时: %v, URL: %s", timeCost, url)

		return img, nil
	}

	return nil, fmt.Errorf("下载图片失败，重试次数超过最大限制")
}
