package util

import (
	"image"

	"github.com/corona10/goimagehash"
	"github.com/nfnt/resize"
)

// todo 要自己定threshold
func CompareImagesFromImage(img1, img2 image.Image, threshold int) (distance int, similar bool, err error) {
	//todo 很多图片尺寸不一样可能导致 差异度不一样，尝试缩放
	// 获取两张图片的尺寸
	bounds1 := img1.Bounds()
	bounds2 := img2.Bounds()

	// 统一尺寸：将较大的图片resize到较小图片的尺寸
	width := uint(bounds1.Dx())
	height := uint(bounds1.Dy())
	if bounds2.Dx()*bounds2.Dy() < bounds1.Dx()*bounds1.Dy() {
		width = uint(bounds2.Dx())
		height = uint(bounds2.Dy())
	}

	// resize
	if bounds1.Dx() != int(width) || bounds1.Dy() != int(height) {
		img1 = resize.Resize(width, height, img1, resize.Lanczos3)
	}
	if bounds2.Dx() != int(width) || bounds2.Dy() != int(height) {
		img2 = resize.Resize(width, height, img2, resize.Lanczos3)
	}

	// 生成 pHash
	hash1, err := goimagehash.PerceptionHash(img1)
	if err != nil {
		return 0, false, err
	}

	hash2, err := goimagehash.PerceptionHash(img2)
	if err != nil {
		return 0, false, err
	}

	// 计算汉明距离
	distance, _ = hash1.Distance(hash2)
	// 判断差异度（汉明距离 ≤ threshold 为相似）
	similar = distance <= threshold

	return distance, similar, nil
}
