package dao

// import (
// 	"context"
// 	"errors"

// 	"git.woa.com/fanniliu/output_error_qq_photos/internal/config"
// 	"git.woa.com/fanniliu/output_error_qq_photos/internal/model"
// 	"gorm.io/gorm"
// )

// type AlbumOrPhotoUinDao struct {
// 	db *gorm.DB
// }

// func NewAlbumOrPhotoUinDao() (*AlbumOrPhotoUinDao, error) {
// 	db, err := config.SQLManager.GetDB("album_or_photo_uin")

// 	if err != nil {
// 		return nil, err
// 	}

// 	return &AlbumOrPhotoUinDao{
// 		db: db,
// 	}, nil
// }

// // 大概有1.8亿个数据行，占用4.32GB
// func (d *AlbumOrPhotoUinDao) GetByUin(ctx context.Context, uin uint64) (*model.AlbumOrPhotoUin, error) {

// 	var result model.AlbumOrPhotoUin
// 	if err := d.db.WithContext(ctx).Where("uint = ?", uin).First(&result).Error; err != nil {
// 		if errors.Is(err, gorm.ErrRecordNotFound) {
// 			return nil, nil
// 		}
// 		return nil, err
// 	}

// 	return &result, nil
// }

// func (d *AlbumOrPhotoUinDao) GetAll(ctx context.Context) ([]*model.AlbumOrPhotoUin, error) {
// 	var results []*model.AlbumOrPhotoUin
// 	if err := d.db.WithContext(ctx).Find(&results).Error; err != nil {
// 		return nil, err
// 	}
// 	return results, nil
// }

// // 深度分页，游标法
// func (this *AlbumOrPhotoUinDao) GetRangeOrderByUin(ctx context.Context, startIndex int, limit int) ([]*model.AlbumOrPhotoUin, error) {
// 	if startIndex < 0 || limit <= 0 {
// 		return nil, errors.New("album_or_photo_uint_dao.GetRangeOrderByUin invalid params")
// 	}

// 	var results []*model.AlbumOrPhotoUin
// 	if err := this.db.WithContext(ctx).
// 		Where("uin > ?", startIndex).
// 		Order("uin ASC").
// 		Offset(startIndex).
// 		Limit(limit).
// 		Find(&results).Error; err != nil {
// 		return nil, err
// 	}

// 	return results, nil
// }
