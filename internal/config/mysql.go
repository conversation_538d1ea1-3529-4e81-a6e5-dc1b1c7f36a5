// /internal/config/mysql.go
package config

// import (
// 	"fmt"
// 	"os"
// 	"sync"
// 	"time"

// 	"gopkg.in/yaml.v3"
// 	"gorm.io/driver/mysql"
// 	"gorm.io/gorm"
// )

// // MySQLConfig represents a single MySQL database configuration
// type MySQLConfig struct {
// 	Host            string        `yaml:"host"`
// 	Port            int           `yaml:"port"`
// 	Username        string        `yaml:"username"`
// 	Password        string        `yaml:"password"`
// 	Database        string        `yaml:"database"`
// 	MaxIdleConns    int           `yaml:"max_idle_conns"`
// 	MaxOpenConns    int           `yaml:"max_open_conns"`
// 	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
// }

// // MySQLConfigs holds multiple MySQL configurations
// type MySQLConfigs struct {
// 	MySQL map[string]MySQLConfig `yaml:"mysql"`
// }

// type MySQLManager struct {
// 	configs *MySQLConfigs
// 	dbs     map[string]*gorm.DB
// 	mu      sync.RWMutex
// }

// var (
// 	SQLManager *MySQLManager
// 	managerMu  sync.Mutex
// )

// func InitManager(configPath string) error {
// 	managerMu.Lock()
// 	defer managerMu.Unlock()

// 	if SQLManager != nil {
// 		return nil // Already initialized
// 	}

// 	manager, err := NewMySQLManager(configPath)
// 	if err != nil {
// 		return err
// 	}

// 	SQLManager = manager
// 	return nil
// }

// func (c *MySQLConfig) GetDSN() string {
// 	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
// 		c.Username, c.Password, c.Host, c.Port, c.Database)
// }

// func loadMySQLConfig(configPath string) (*MySQLConfigs, error) {
// 	data, err := os.ReadFile(configPath)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to read MySQL config file: %w", err)
// 	}

// 	var configs MySQLConfigs
// 	if err := yaml.Unmarshal(data, &configs); err != nil {
// 		return nil, fmt.Errorf("failed to unmarshal MySQL config: %w", err)
// 	}

// 	return &configs, nil
// }

// func NewMySQLManager(configPath string) (*MySQLManager, error) {
// 	configs, err := loadMySQLConfig(configPath)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return &MySQLManager{
// 		configs: configs,
// 		dbs:     make(map[string]*gorm.DB),
// 	}, nil
// }

// func (m *MySQLManager) GetDB(name string) (*gorm.DB, error) {
// 	m.mu.RLock()
// 	db, exists := m.dbs[name]
// 	m.mu.RUnlock()

// 	if exists {
// 		return db, nil
// 	}

// 	return m.initDB(name)
// }

// // initDB initializes a database connection
// func (m *MySQLManager) initDB(name string) (*gorm.DB, error) {
// 	m.mu.Lock()
// 	defer m.mu.Unlock()

// 	// Check again in case another goroutine initialized it
// 	if db, exists := m.dbs[name]; exists {
// 		return db, nil
// 	}

// 	config, exists := m.configs.MySQL[name]
// 	if !exists {
// 		return nil, fmt.Errorf("MySQL configuration for '%s' not found", name)
// 	}

// 	db, err := gorm.Open(mysql.Open(config.GetDSN()), &gorm.Config{})
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to connect to MySQL database '%s': %w", name, err)
// 	}

// 	sqlDB, err := db.DB()
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to get database connection for '%s': %w", name, err)
// 	}

// 	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
// 	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
// 	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)

// 	m.dbs[name] = db
// 	return db, nil
// }
