package app_config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构体
type Config struct {
	Util UtilConfig `yaml:"util"`
}

// UtilConfig 工具配置
type UtilConfig struct {
	Phash PhashConfig `yaml:"phash"`
}

// PhashConfig 感知哈希配置
type PhashConfig struct {
	DistanceThreshold int `yaml:"distance_threshold"`
}

// AppConfig 全局配置变量
var appConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) error {
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML
	config := &Config{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 存储到全局变量
	appConfig = config
	return nil
}

// GetConfig 获取配置
func GetConfig() *Config {
	return appConfig
}
