package service

import (
	"context"

	"git.woa.com/fanniliu/output_error_qq_photos/internal/handler"
	"git.woa.com/trpc-go/tnet/log"
	pb "git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

type QzonePhotoToolImpl struct {
	pb.UnimplementedQzonePhotoTool
	//todo 注入Handler
	QzonePhotoToolHandler *handler.QzonePhotoToolHandlerImpl
}

func (s *QzonePhotoToolImpl) RawFixTool(
	ctx context.Context,
	req *pb.RawFixToolRequest,
) (*pb.RawFixToolReply, error) {
	// rsp := &pb.RawFixToolReply{}
	// rsp.Photoids = []uint64{1, 2, 3, 4, 5, 1432143}
	uin := req.Id
	errPhotoIds, err := s.QzonePhotoToolHandler.FindErrorPhotosByUin(uin)

	if err != nil {
		log.Errorf("findErrorPhotosByUin err:%v", err)
		return nil, err
	}

	rsp := &pb.RawFixToolReply{
		Photoids: errPhotoIds,
	}
	return rsp, nil
}

func NewQzonePhotoToolImpl() *QzonePhotoToolImpl {
	result := &QzonePhotoToolImpl{
		QzonePhotoToolHandler: handler.NewQzonePhotoToolHandlerImpl(),
	}
	return result
}
