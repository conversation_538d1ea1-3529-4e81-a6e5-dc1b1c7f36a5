#!/bin/bash

# Docker 构建和部署脚本

set -e

IMAGE_NAME="output_error_qq_photos"
TAG="latest"
TAR_FILE="${IMAGE_NAME}_${TAG}.tar"

echo "开始构建 Docker 镜像..."

# 构建镜像
docker build -f Dockerfile.simple -t ${IMAGE_NAME}:${TAG} .

echo "镜像构建完成！"

# 保存镜像为 tar 文件
echo "正在导出镜像到 ${TAR_FILE}..."
docker save -o ${TAR_FILE} ${IMAGE_NAME}:${TAG}

echo "镜像已导出到: ${TAR_FILE}"
echo "文件大小: $(du -h ${TAR_FILE} | cut -f1)"

echo ""
echo "部署说明："
echo "1. 将 ${TAR_FILE} 传输到目标服务器"
echo "2. 在服务器上运行: docker load -i ${TAR_FILE}"
echo "3. 运行容器: docker run -d --name ${IMAGE_NAME} -p 8080:8080 ${IMAGE_NAME}:${TAG}"
echo ""
echo "或者使用 docker-compose:"
echo "docker-compose up -d"
