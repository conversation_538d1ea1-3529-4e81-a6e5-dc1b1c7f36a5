2025-05-20 10:32:50.083461Z	info	network	network/impl.go:195	long connection {ID:1238261356,Address:************:8081,Service:{ServiceKey: {namespace: "Polaris", service: "polaris-default"}, ClusterType: builtin}} create
2025-05-20 10:32:50.083700Z	info	network	network/conn.go:89	connection {ID:1238261356,Address:************:8081,Service:{ServiceKey: {namespace: "Polaris", service: "polaris-default"}, ClusterType: builtin}}: acquired, opKey System curRef is 1
2025-05-20 10:39:06.423981Z	error	network	network/impl.go:420	fail to get connection, opKey is System, cluster builtin, error is fail to connect to ***********:8081, timeout is 3.468220792s, service is {ServiceKey: {namespace: "Polaris", service: "polaris-default"}, ClusterType: builtin}, because context deadline exceeded
2025-05-20 10:39:06.424047Z	info	network	common/system.go:161	fail to get connection of builtin, err fail to connect to ***********:8081, timeout is 3.468220792s, service is {ServiceKey: {namespace: "Polaris", service: "polaris-default"}, ClusterType: builtin}, because context deadline exceeded
