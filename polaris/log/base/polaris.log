2025-05-20 10:31:51.784549Z	info	base	api/config.go:293	global clock started
2025-05-20 10:31:51.784926Z	warn	base	config/default.go:401	no IP or interface name configured
2025-05-20 10:31:51.784990Z	info	base	api/config.go:321	
-------Start to init SDKContext of version 0.12.12, IP: , PID: 2641399, UID: 3E6848BC-F95C-4092-8B9E-BD23B0B14404, CONTAINER: , HOSTNAME:VM-9-17-tencentos-------
2025-05-20 10:31:51.785160Z	info	base	grpc/operation_async.go:88	set grpc plugin as connectionCreator
2025-05-20 10:31:51.785205Z	info	base	plugin/manage.go:236	Initialized plugin type serverConnector, name grpc, id 30
2025-05-20 10:31:51.785227Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name filterOn<PERSON><PERSON><PERSON>er, id 14
2025-05-20 10:31:51.785387Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name nearbyBasedRouter, id 15
2025-05-20 10:31:51.785550Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name ruleBasedRouter, id 16
2025-05-20 10:31:51.785560Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name setDivisionRouter, id 17
2025-05-20 10:31:51.785569Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name canaryRouter, id 12
2025-05-20 10:31:51.785579Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name dstMetaRouter, id 13
2025-05-20 10:31:51.785589Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name maglev, id 3
2025-05-20 10:31:51.785607Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name cMurmurHash, id 4
2025-05-20 10:31:51.785615Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name l5cst, id 5
2025-05-20 10:31:51.785624Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name ringHash, id 6
2025-05-20 10:31:51.785806Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name weightedRandom, id 7
2025-05-20 10:31:51.786028Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name dynamicWeightedRandom, id 22
2025-05-20 10:31:51.786044Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name hash, id 2
2025-05-20 10:31:51.786055Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name http, id 8
2025-05-20 10:31:51.786063Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name tcp, id 9
2025-05-20 10:31:51.786071Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name udp, id 10
2025-05-20 10:31:51.786081Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCheck, id 19
2025-05-20 10:31:51.786092Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCount, id 20
2025-05-20 10:31:51.786108Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorRate, id 21
2025-05-20 10:31:51.786116Z	info	base	plugin/manage.go:236	Initialized plugin type weightAdjuster, name rateDelayAdjuster, id 18
2025-05-20 10:31:51.786128Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceRoute, id 29
2025-05-20 10:31:51.792300Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name stat2Monitor, id 26
2025-05-20 10:31:51.792348Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name rateLimitRecord, id 27
2025-05-20 10:31:51.792368Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceCache, id 28
2025-05-20 10:31:51.792377Z	info	base	plugin/manage.go:236	Initialized plugin type alarmReporter, name alarm2file, id 1
2025-05-20 10:31:51.792391Z	info	base	inmemory/inmemory.go:166	LocalCache Real persistDir:/data/home/<USER>/polaris/backup
2025-05-20 10:31:51.792457Z	info	base	plugin/manage.go:236	Initialized plugin type localRegistry, name inmemory, id 23
2025-05-20 10:31:51.792472Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name unirate, id 24
2025-05-20 10:31:51.792484Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name reject, id 11
2025-05-20 10:31:51.792506Z	info	base	plugin/manage.go:236	Initialized plugin type subScribe, name subscribeLocalChannel, id 25
2025-05-20 10:31:51.797282Z	info	base	api/config.go:348	
3E6848BC-F95C-4092-8B9E-BD23B0B14404, -------Configuration with default value-------
global:
  system:
    mode: 0
    discoverCluster:
      namespace: Polaris
      service: polaris.discover
      refreshInterval: 10m0s
    healthCheckCluster:
      namespace: Polaris
      service: polaris.healthcheck
      refreshInterval: 10m0s
    monitorCluster:
      namespace: Polaris
      service: polaris.monitor
      refreshInterval: 10m0s
    rateLimitCluster: null
    metricCluster: null
    dynamicWeightCluster:
      namespace: Polaris
      service: polaris.dynamic.weight
      refreshInterval: 10m0s
    variables: {}
    apiVersion: v2
  api:
    timeout: 1s
    bindIf: ""
    bindIP: ""
    reportInterval: 10m0s
    maxRetryTimes: 1
    retryInterval: 1s
    location:
      region: ""
      zone: ""
      campus: ""
      enableUpdate: true
  serverConnector:
    addresses: []
    backupAddresses: []
    protocol: grpc
    connectTimeout: 1s
    messageTimeout: 1.5s
    watchTimeout: 1m0s
    connectionIdleTimeout: 3s
    requestQueueSize: 1000
    serverSwitchInterval: 10m0s
    reconnectInterval: 500ms
    apiVersion: v2
    joinPoint: ""
    plugin:
      grpc:
        maxCallRecvMsgSize: 52428800
  statReporter:
    enable: true
    chain:
    - stat2Monitor
    - serviceCache
    - rateLimitRecord
    - serviceRoute
    plugin:
      rateLimitRecord:
        reportInterval: 1m0s
      serviceCache:
        reportInterval: 3m0s
      serviceRoute:
        reportInterval: 5m0s
      stat2Monitor:
        metricsReportWindow: 1m0s
        metricsNumBuckets: 12
consumer:
  localCache:
    serviceExpireTime: 24h0m0s
    serviceRefreshInterval: 2s
    persistDir: $HOME/polaris/backup
    enablePersistence: null
    type: inmemory
    persistMaxWriteRetry: 5
    persistMaxReadRetry: 1
    persistRetryInterval: 1s
    persistAvailableInterval: 1m0s
    startUseFileCache: true
    pushEmptyProtection: false
    plugin: {}
  serviceRouter:
    chain:
    - dstMetaRouter
    - ruleBasedRouter
    - nearbyBasedRouter
    plugin:
      nearbyBasedRouter:
        matchLevel: zone
        maxMatchLevel: ""
        strictNearby: false
        enableDegradeByUnhealthyPercent: true
        unhealthyPercentToDegrade: 100
    percentOfMinInstances: 0
    disableRouteSourceContinue: false
    enableRecoverAll: true
  loadbalancer:
    type: weightedRandom
    plugin:
      hash:
        hashFunction: murmur3
      maglev:
        hashFunction: murmur3
        tableSize: 65537
      ringHash:
        hashFunction: murmur3
        vnodeCount: 10
  circuitBreaker:
    enable: true
    checkPeriod: 30s
    chain:
    - errorCount
    - errorRate
    - errorCheck
    sleepWindow: 30s
    requestCountAfterHalfOpen: 10
    successCountAfterHalfOpen: 8
    recoverWindow: 1m0s
    recoverNumBuckets: 10
    plugin:
      errorCount:
        continuousErrorThreshold: 10
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 10
      errorRate:
        requestVolumeThreshold: 10
        errorRatePercent: 50
        errorRateThreshold: 0
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 5
  healthCheck:
    interval: 10s
    chain: []
    plugin:
      http:
        timeout: 100ms
        path: ""
      tcp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
      udp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
    when: never
    concurrency: 0
  subscribe:
    type: subscribeLocalChannel
    plugin:
      subscribeLocalChannel:
        channelBufferSize: 30
  servicesSpecific:
  - namespace: Polaris
    service: polaris.discover
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.healthcheck
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.monitor
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.dynamic.weight
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
provider:
  rateLimit:
    enable: true
    plugin:
      unirate:
        maxQueuingTime: 1s
    mode: "\x01"
    rateLimitCluster:
      namespace: ""
      service: ""
      refreshInterval: 10m0s
    maxWindowSize: 20000
    purgeInterval: 1m0s

2025-05-20 10:31:51.797339Z	info	base	api/config.go:353	
-------3E6848BC-F95C-4092-8B9E-BD23B0B14404, All plugins and engine initialized successfully-------
2025-05-20 10:31:51.797402Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/client_info.json
2025-05-20 10:31:51.797448Z	info	base	network/impl.go:370	connectionManager start updateDiscover
2025-05-20 10:31:51.797563Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:31:51.797584Z	info	base	inmemory/inmemory.go:550	3E6848BC-F95C-4092-8B9E-BD23B0B14404, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:31:51.797602Z	info	base	common/register.go:52	3E6848BC-F95C-4092-8B9E-BD23B0B14404, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:31:51.797622Z	info	base	common/register.go:65	3E6848BC-F95C-4092-8B9E-BD23B0B14404, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:31:51.797633Z	info	base	inmemory/inmemory.go:552	3E6848BC-F95C-4092-8B9E-BD23B0B14404, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}, err <nil>
2025-05-20 10:31:51.797649Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:31:51.797661Z	info	base	inmemory/inmemory.go:550	3E6848BC-F95C-4092-8B9E-BD23B0B14404, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:31:51.797672Z	info	base	common/register.go:52	3E6848BC-F95C-4092-8B9E-BD23B0B14404, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:31:51.797682Z	info	base	common/register.go:65	3E6848BC-F95C-4092-8B9E-BD23B0B14404, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:31:51.797691Z	info	base	inmemory/inmemory.go:552	3E6848BC-F95C-4092-8B9E-BD23B0B14404, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}, err <nil>
2025-05-20 10:31:51.797737Z	info	base	network/impl.go:410	get connection of builtin for System
2025-05-20 10:31:51.799598Z	info	base	startup/client_report.go:138	current client area info is Host:**********-{Region:华南, Zone:广州, Campus:广州三区}
2025-05-20 10:31:51.799622Z	info	base	startup/client_report.go:144	client area info is ready
2025-05-20 10:31:51.799637Z	info	base	schedule/routines.go:228	item clientReportTask in task clientReportTask has added
2025-05-20 10:31:51.799649Z	info	base	schedule/routines.go:115	task clientReportTask started period 5m0s
2025-05-20 10:31:51.799668Z	info	base	schedule/routines.go:277	task clientReportTask has been started
2025-05-20 10:31:51.799677Z	info	base	schedule/routines.go:228	item sdkConfigReportTask in task sdkConfigReportTask has added
2025-05-20 10:31:51.799686Z	info	base	schedule/routines.go:115	task sdkConfigReportTask started period 2m30s
2025-05-20 10:31:51.799695Z	info	base	schedule/routines.go:277	task sdkConfigReportTask has been started
2025-05-20 10:31:51.799703Z	info	base	api/config.go:363	
-------3E6848BC-F95C-4092-8B9E-BD23B0B14404, All plugins and engine started successfully-------
2025-05-20 10:31:51.799714Z	info	base	api/config.go:371	
-------3E6848BC-F95C-4092-8B9E-BD23B0B14404, SDKContext init successfully-------
2025-05-20 10:31:51.801490Z	info	base	grpc/creator.go:88	localAddress from connection is **********:43400, IP is **********, hashValue is 8140490996141982352
2025-05-20 10:32:07.916114Z	error	base	flow/sync_flow.go:182	fail to get resource of {namespace: "Polaris", service: "polaris.discover"} for timeout, retryTimes: 0, total consumed time: 0s, total sleep time: 0s
2025-05-20 10:32:07.916118Z	error	base	common/cache_persist.go:130	fail to load cache from file svcV2#Polaris#polaris.discover#instance.json, svcKey: {namespace: "Polaris", service: "polaris.discover", event: instance}, error is Fail to Stat the cache file name /data/home/<USER>/polaris/backup/svcV2#Polaris#polaris.discover#instance.json:  stat /data/home/<USER>/polaris/backup/svcV2#Polaris#polaris.discover#instance.json: no such file or directory
2025-05-20 10:32:07.916154Z	error	base	flow/sync_flow.go:186	retry times exceed 0 in SyncGetResources, serviceKey: {namespace: "Polaris", service: "polaris.discover"}, timeout is 300ms
2025-05-20 10:32:07.916176Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/svc#Polaris#polaris.discover#instance.json
2025-05-20 10:32:07.916258Z	error	base	network/impl.go:374	connectionManager get discover service err: polaris-go version: 0.12.12, Polaris-1004(ErrCodeAPITimeoutError): retry times exceed 0 in SyncGetResources, serviceKey: {namespace: "Polaris", service: "polaris.discover"}, timeout is 300ms
n type serviceRouter, name filterOnlyRouter, id 14
2025-05-20 10:31:55.140382Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name ringHash, id 6
2025-05-20 10:31:55.140600Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name weightedRandom, id 7
2025-05-20 10:31:55.140881Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name dynamicWeightedRandom, id 22
2025-05-20 10:31:55.140905Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name hash, id 2
2025-05-20 10:31:55.140919Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name maglev, id 3
2025-05-20 10:31:55.140930Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name cMurmurHash, id 4
2025-05-20 10:31:55.140942Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name l5cst, id 5
2025-05-20 10:31:55.140958Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name tcp, id 9
2025-05-20 10:31:55.140971Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name udp, id 10
2025-05-20 10:31:55.140984Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name http, id 8
2025-05-20 10:31:55.140999Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCheck, id 19
2025-05-20 10:31:55.141014Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCount, id 20
2025-05-20 10:31:55.141041Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorRate, id 21
2025-05-20 10:31:55.141054Z	info	base	plugin/manage.go:236	Initialized plugin type weightAdjuster, name rateDelayAdjuster, id 18
2025-05-20 10:31:55.141069Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name rateLimitRecord, id 27
2025-05-20 10:31:55.141090Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceCache, id 28
2025-05-20 10:31:55.141108Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceRoute, id 29
2025-05-20 10:31:55.149339Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name stat2Monitor, id 26
2025-05-20 10:31:55.149370Z	info	base	plugin/manage.go:236	Initialized plugin type alarmReporter, name alarm2file, id 1
2025-05-20 10:31:55.149385Z	info	base	inmemory/inmemory.go:166	LocalCache Real persistDir:/data/home/<USER>/polaris/backup
2025-05-20 10:31:55.149438Z	info	base	plugin/manage.go:236	Initialized plugin type localRegistry, name inmemory, id 23
2025-05-20 10:31:55.149448Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name reject, id 11
2025-05-20 10:31:55.149461Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name unirate, id 24
2025-05-20 10:31:55.149471Z	info	base	plugin/manage.go:236	Initialized plugin type subScribe, name subscribeLocalChannel, id 25
2025-05-20 10:31:55.152436Z	info	base	api/config.go:348	
DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, -------Configuration with default value-------
global:
  system:
    mode: 0
    discoverCluster:
      namespace: Polaris
      service: polaris.discover
      refreshInterval: 10m0s
    healthCheckCluster:
      namespace: Polaris
      service: polaris.healthcheck
      refreshInterval: 10m0s
    monitorCluster:
      namespace: Polaris
      service: polaris.monitor
      refreshInterval: 10m0s
    rateLimitCluster: null
    metricCluster: null
    dynamicWeightCluster:
      namespace: Polaris
      service: polaris.dynamic.weight
      refreshInterval: 10m0s
    variables: {}
    apiVersion: v2
  api:
    timeout: 1s
    bindIf: ""
    bindIP: ""
    reportInterval: 10m0s
    maxRetryTimes: 1
    retryInterval: 1s
    location:
      region: ""
      zone: ""
      campus: ""
      enableUpdate: true
  serverConnector:
    addresses: []
    backupAddresses: []
    protocol: grpc
    connectTimeout: 1s
    messageTimeout: 1.5s
    watchTimeout: 1m0s
    connectionIdleTimeout: 3s
    requestQueueSize: 1000
    serverSwitchInterval: 10m0s
    reconnectInterval: 500ms
    apiVersion: v2
    joinPoint: ""
    plugin:
      grpc:
        maxCallRecvMsgSize: 52428800
  statReporter:
    enable: true
    chain:
    - stat2Monitor
    - serviceCache
    - rateLimitRecord
    - serviceRoute
    plugin:
      rateLimitRecord:
        reportInterval: 1m0s
      serviceCache:
        reportInterval: 3m0s
      serviceRoute:
        reportInterval: 5m0s
      stat2Monitor:
        metricsReportWindow: 1m0s
        metricsNumBuckets: 12
consumer:
  localCache:
    serviceExpireTime: 24h0m0s
    serviceRefreshInterval: 2s
    persistDir: $HOME/polaris/backup
    enablePersistence: null
    type: inmemory
    persistMaxWriteRetry: 5
    persistMaxReadRetry: 1
    persistRetryInterval: 1s
    persistAvailableInterval: 1m0s
    startUseFileCache: true
    pushEmptyProtection: false
    plugin: {}
  serviceRouter:
    chain:
    - dstMetaRouter
    - ruleBasedRouter
    - nearbyBasedRouter
    plugin:
      nearbyBasedRouter:
        matchLevel: zone
        maxMatchLevel: ""
        strictNearby: false
        enableDegradeByUnhealthyPercent: true
        unhealthyPercentToDegrade: 100
    percentOfMinInstances: 0
    disableRouteSourceContinue: false
    enableRecoverAll: true
  loadbalancer:
    type: weightedRandom
    plugin:
      hash:
        hashFunction: murmur3
      maglev:
        hashFunction: murmur3
        tableSize: 65537
      ringHash:
        hashFunction: murmur3
        vnodeCount: 10
  circuitBreaker:
    enable: true
    checkPeriod: 30s
    chain:
    - errorCount
    - errorRate
    - errorCheck
    sleepWindow: 30s
    requestCountAfterHalfOpen: 10
    successCountAfterHalfOpen: 8
    recoverWindow: 1m0s
    recoverNumBuckets: 10
    plugin:
      errorCount:
        continuousErrorThreshold: 10
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 10
      errorRate:
        requestVolumeThreshold: 10
        errorRatePercent: 50
        errorRateThreshold: 0
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 5
  healthCheck:
    interval: 10s
    chain: []
    plugin:
      http:
        timeout: 100ms
        path: ""
      tcp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
      udp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
    when: never
    concurrency: 0
  subscribe:
    type: subscribeLocalChannel
    plugin:
      subscribeLocalChannel:
        channelBufferSize: 30
  servicesSpecific:
  - namespace: Polaris
    service: polaris.discover
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.healthcheck
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.monitor
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.dynamic.weight
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
provider:
  rateLimit:
    enable: true
    plugin:
      unirate:
        maxQueuingTime: 1s
    mode: "\x01"
    rateLimitCluster:
      namespace: ""
      service: ""
      refreshInterval: 10m0s
    maxWindowSize: 20000
    purgeInterval: 1m0s

2025-05-20 10:31:55.152474Z	info	base	api/config.go:353	
-------DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, All plugins and engine initialized successfully-------
2025-05-20 10:31:55.152529Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/client_info.json
2025-05-20 10:31:55.152567Z	info	base	network/impl.go:370	connectionManager start updateDiscover
2025-05-20 10:31:55.152713Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:31:55.152746Z	info	base	inmemory/inmemory.go:550	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:31:55.152769Z	info	base	common/register.go:52	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:31:55.152788Z	info	base	common/register.go:65	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:31:55.152807Z	info	base	inmemory/inmemory.go:552	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}, err <nil>
2025-05-20 10:31:55.152828Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:31:55.152841Z	info	base	inmemory/inmemory.go:550	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:31:55.152853Z	info	base	common/register.go:52	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:31:55.152885Z	info	base	common/register.go:65	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:31:55.152906Z	info	base	inmemory/inmemory.go:552	DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}, err <nil>
2025-05-20 10:31:55.153015Z	info	base	network/impl.go:410	get connection of builtin for System
2025-05-20 10:31:55.154088Z	info	base	startup/client_report.go:138	current client area info is Host:**********-{Region:华南, Zone:广州, Campus:广州三区}
2025-05-20 10:31:55.154115Z	info	base	startup/client_report.go:144	client area info is ready
2025-05-20 10:31:55.154134Z	info	base	schedule/routines.go:228	item clientReportTask in task clientReportTask has added
2025-05-20 10:31:55.154151Z	info	base	schedule/routines.go:115	task clientReportTask started period 5m0s
2025-05-20 10:31:55.154170Z	info	base	schedule/routines.go:277	task clientReportTask has been started
2025-05-20 10:31:55.154190Z	info	base	schedule/routines.go:228	item sdkConfigReportTask in task sdkConfigReportTask has added
2025-05-20 10:31:55.154204Z	info	base	schedule/routines.go:115	task sdkConfigReportTask started period 2m30s
2025-05-20 10:31:55.154215Z	info	base	schedule/routines.go:277	task sdkConfigReportTask has been started
2025-05-20 10:31:55.154223Z	info	base	api/config.go:363	
-------DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, All plugins and engine started successfully-------
2025-05-20 10:31:55.154235Z	info	base	api/config.go:371	
-------DFB2D4D8-86AC-423E-B5B0-16A1D9B9CC45, SDKContext init successfully-------
2025-05-20 10:31:55.154611Z	info	base	grpc/creator.go:88	localAddress from connection is **********:60606, IP is **********, hashValue is 8140490996141982352
2025-05-20 10:32:45.786525Z	info	base	api/config.go:293	global clock started
2025-05-20 10:32:45.786703Z	warn	base	config/default.go:401	no IP or interface name configured
2025-05-20 10:32:45.786769Z	info	base	api/config.go:321	
-------Start to init SDKContext of version 0.12.12, IP: , PID: 2642139, UID: 00929BB5-4C5E-457A-A6D4-AB622401295C, CONTAINER: , HOSTNAME:VM-9-17-tencentos-------
2025-05-20 10:32:45.786986Z	info	base	grpc/operation_async.go:88	set grpc plugin as connectionCreator
2025-05-20 10:32:45.787029Z	info	base	plugin/manage.go:236	Initialized plugin type serverConnector, name grpc, id 30
2025-05-20 10:32:45.787053Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name dstMetaRouter, id 13
2025-05-20 10:32:45.787066Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name filterOnlyRouter, id 14
2025-05-20 10:32:45.787267Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name nearbyBasedRouter, id 15
2025-05-20 10:32:45.787461Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name ruleBasedRouter, id 16
2025-05-20 10:32:45.787475Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name setDivisionRouter, id 17
2025-05-20 10:32:45.787486Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name canaryRouter, id 12
2025-05-20 10:32:45.787739Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name dynamicWeightedRandom, id 22
2025-05-20 10:32:45.787765Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name hash, id 2
2025-05-20 10:32:45.787778Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name maglev, id 3
2025-05-20 10:32:45.787788Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name cMurmurHash, id 4
2025-05-20 10:32:45.787798Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name l5cst, id 5
2025-05-20 10:32:45.787808Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name ringHash, id 6
2025-05-20 10:32:45.788007Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name weightedRandom, id 7
2025-05-20 10:32:45.788024Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name http, id 8
2025-05-20 10:32:45.788043Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name tcp, id 9
2025-05-20 10:32:45.788051Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name udp, id 10
2025-05-20 10:32:45.788061Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCheck, id 19
2025-05-20 10:32:45.788071Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCount, id 20
2025-05-20 10:32:45.788087Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorRate, id 21
2025-05-20 10:32:45.788096Z	info	base	plugin/manage.go:236	Initialized plugin type weightAdjuster, name rateDelayAdjuster, id 18
2025-05-20 10:32:45.794146Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name stat2Monitor, id 26
2025-05-20 10:32:45.794172Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name rateLimitRecord, id 27
2025-05-20 10:32:45.794200Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceCache, id 28
2025-05-20 10:32:45.794213Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceRoute, id 29
2025-05-20 10:32:45.794222Z	info	base	plugin/manage.go:236	Initialized plugin type alarmReporter, name alarm2file, id 1
2025-05-20 10:32:45.794233Z	info	base	inmemory/inmemory.go:166	LocalCache Real persistDir:/data/home/<USER>/polaris/backup
2025-05-20 10:32:45.794280Z	info	base	plugin/manage.go:236	Initialized plugin type localRegistry, name inmemory, id 23
2025-05-20 10:32:45.794290Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name reject, id 11
2025-05-20 10:32:45.794301Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name unirate, id 24
2025-05-20 10:32:45.794310Z	info	base	plugin/manage.go:236	Initialized plugin type subScribe, name subscribeLocalChannel, id 25
2025-05-20 10:32:45.798995Z	info	base	api/config.go:348	
00929BB5-4C5E-457A-A6D4-AB622401295C, -------Configuration with default value-------
global:
  system:
    mode: 0
    discoverCluster:
      namespace: Polaris
      service: polaris.discover
      refreshInterval: 10m0s
    healthCheckCluster:
      namespace: Polaris
      service: polaris.healthcheck
      refreshInterval: 10m0s
    monitorCluster:
      namespace: Polaris
      service: polaris.monitor
      refreshInterval: 10m0s
    rateLimitCluster: null
    metricCluster: null
    dynamicWeightCluster:
      namespace: Polaris
      service: polaris.dynamic.weight
      refreshInterval: 10m0s
    variables: {}
    apiVersion: v2
  api:
    timeout: 1s
    bindIf: ""
    bindIP: ""
    reportInterval: 10m0s
    maxRetryTimes: 1
    retryInterval: 1s
    location:
      region: ""
      zone: ""
      campus: ""
      enableUpdate: true
  serverConnector:
    addresses: []
    backupAddresses: []
    protocol: grpc
    connectTimeout: 1s
    messageTimeout: 1.5s
    watchTimeout: 1m0s
    connectionIdleTimeout: 3s
    requestQueueSize: 1000
    serverSwitchInterval: 10m0s
    reconnectInterval: 500ms
    apiVersion: v2
    joinPoint: ""
    plugin:
      grpc:
        maxCallRecvMsgSize: 52428800
  statReporter:
    enable: true
    chain:
    - stat2Monitor
    - serviceCache
    - rateLimitRecord
    - serviceRoute
    plugin:
      rateLimitRecord:
        reportInterval: 1m0s
      serviceCache:
        reportInterval: 3m0s
      serviceRoute:
        reportInterval: 5m0s
      stat2Monitor:
        metricsReportWindow: 1m0s
        metricsNumBuckets: 12
consumer:
  localCache:
    serviceExpireTime: 24h0m0s
    serviceRefreshInterval: 2s
    persistDir: $HOME/polaris/backup
    enablePersistence: null
    type: inmemory
    persistMaxWriteRetry: 5
    persistMaxReadRetry: 1
    persistRetryInterval: 1s
    persistAvailableInterval: 1m0s
    startUseFileCache: true
    pushEmptyProtection: false
    plugin: {}
  serviceRouter:
    chain:
    - dstMetaRouter
    - ruleBasedRouter
    - nearbyBasedRouter
    plugin:
      nearbyBasedRouter:
        matchLevel: zone
        maxMatchLevel: ""
        strictNearby: false
        enableDegradeByUnhealthyPercent: true
        unhealthyPercentToDegrade: 100
    percentOfMinInstances: 0
    disableRouteSourceContinue: false
    enableRecoverAll: true
  loadbalancer:
    type: weightedRandom
    plugin:
      hash:
        hashFunction: murmur3
      maglev:
        hashFunction: murmur3
        tableSize: 65537
      ringHash:
        hashFunction: murmur3
        vnodeCount: 10
  circuitBreaker:
    enable: true
    checkPeriod: 30s
    chain:
    - errorCount
    - errorRate
    - errorCheck
    sleepWindow: 30s
    requestCountAfterHalfOpen: 10
    successCountAfterHalfOpen: 8
    recoverWindow: 1m0s
    recoverNumBuckets: 10
    plugin:
      errorCount:
        continuousErrorThreshold: 10
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 10
      errorRate:
        requestVolumeThreshold: 10
        errorRatePercent: 50
        errorRateThreshold: 0
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 5
  healthCheck:
    interval: 10s
    chain: []
    plugin:
      http:
        timeout: 100ms
        path: ""
      tcp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
      udp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
    when: never
    concurrency: 0
  subscribe:
    type: subscribeLocalChannel
    plugin:
      subscribeLocalChannel:
        channelBufferSize: 30
  servicesSpecific:
  - namespace: Polaris
    service: polaris.discover
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.healthcheck
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.monitor
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.dynamic.weight
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
provider:
  rateLimit:
    enable: true
    plugin:
      unirate:
        maxQueuingTime: 1s
    mode: "\x01"
    rateLimitCluster:
      namespace: ""
      service: ""
      refreshInterval: 10m0s
    maxWindowSize: 20000
    purgeInterval: 1m0s

2025-05-20 10:32:45.799050Z	info	base	api/config.go:353	
-------00929BB5-4C5E-457A-A6D4-AB622401295C, All plugins and engine initialized successfully-------
2025-05-20 10:32:45.799111Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/client_info.json
2025-05-20 10:32:45.799158Z	info	base	network/impl.go:370	connectionManager start updateDiscover
2025-05-20 10:32:45.799277Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:32:45.799297Z	info	base	inmemory/inmemory.go:550	00929BB5-4C5E-457A-A6D4-AB622401295C, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:32:45.799314Z	info	base	common/register.go:52	00929BB5-4C5E-457A-A6D4-AB622401295C, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:32:45.799333Z	info	base	common/register.go:65	00929BB5-4C5E-457A-A6D4-AB622401295C, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:32:45.799344Z	info	base	inmemory/inmemory.go:552	00929BB5-4C5E-457A-A6D4-AB622401295C, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}, err <nil>
2025-05-20 10:32:45.799362Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:32:45.799374Z	info	base	inmemory/inmemory.go:550	00929BB5-4C5E-457A-A6D4-AB622401295C, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:32:45.799385Z	info	base	common/register.go:52	00929BB5-4C5E-457A-A6D4-AB622401295C, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:32:45.799395Z	info	base	common/register.go:65	00929BB5-4C5E-457A-A6D4-AB622401295C, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:32:45.799405Z	info	base	inmemory/inmemory.go:552	00929BB5-4C5E-457A-A6D4-AB622401295C, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}, err <nil>
2025-05-20 10:32:45.799466Z	info	base	network/impl.go:410	get connection of builtin for System
2025-05-20 10:32:45.800594Z	info	base	startup/client_report.go:138	current client area info is Host:**********-{Region:华南, Zone:广州, Campus:广州三区}
2025-05-20 10:32:45.800623Z	info	base	startup/client_report.go:144	client area info is ready
2025-05-20 10:32:45.800637Z	info	base	schedule/routines.go:228	item clientReportTask in task clientReportTask has added
2025-05-20 10:32:45.800650Z	info	base	schedule/routines.go:115	task clientReportTask started period 5m0s
2025-05-20 10:32:45.800668Z	info	base	schedule/routines.go:277	task clientReportTask has been started
2025-05-20 10:32:45.800678Z	info	base	schedule/routines.go:228	item sdkConfigReportTask in task sdkConfigReportTask has added
2025-05-20 10:32:45.800688Z	info	base	schedule/routines.go:115	task sdkConfigReportTask started period 2m30s
2025-05-20 10:32:45.800696Z	info	base	schedule/routines.go:277	task sdkConfigReportTask has been started
2025-05-20 10:32:45.800704Z	info	base	api/config.go:363	
-------00929BB5-4C5E-457A-A6D4-AB622401295C, All plugins and engine started successfully-------
2025-05-20 10:32:45.800716Z	info	base	api/config.go:371	
-------00929BB5-4C5E-457A-A6D4-AB622401295C, SDKContext init successfully-------
2025-05-20 10:32:45.800979Z	info	base	grpc/creator.go:88	localAddress from connection is **********:45484, IP is **********, hashValue is 8140490996141982352
2025-05-20 10:37:20.500408Z	info	base	api/config.go:293	global clock started
2025-05-20 10:37:20.500563Z	warn	base	config/default.go:401	no IP or interface name configured
2025-05-20 10:37:20.500623Z	info	base	api/config.go:321	
-------Start to init SDKContext of version 0.12.12, IP: , PID: 2644637, UID: 1B6FD704-82C7-4CE2-980F-E7D55A482ACB, CONTAINER: , HOSTNAME:VM-9-17-tencentos-------
2025-05-20 10:37:20.500796Z	info	base	grpc/operation_async.go:88	set grpc plugin as connectionCreator
2025-05-20 10:37:20.500826Z	info	base	plugin/manage.go:236	Initialized plugin type serverConnector, name grpc, id 30
2025-05-20 10:37:20.500845Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name canaryRouter, id 12
2025-05-20 10:37:20.500855Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name dstMetaRouter, id 13
2025-05-20 10:37:20.500865Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name filterOnlyRouter, id 14
2025-05-20 10:37:20.501134Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name nearbyBasedRouter, id 15
2025-05-20 10:37:20.501347Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name ruleBasedRouter, id 16
2025-05-20 10:37:20.501360Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name setDivisionRouter, id 17
2025-05-20 10:37:20.501371Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name maglev, id 3
2025-05-20 10:37:20.501387Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name cMurmurHash, id 4
2025-05-20 10:37:20.501397Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name l5cst, id 5
2025-05-20 10:37:20.501407Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name ringHash, id 6
2025-05-20 10:37:20.501591Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name weightedRandom, id 7
2025-05-20 10:37:20.501788Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name dynamicWeightedRandom, id 22
2025-05-20 10:37:20.501799Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name hash, id 2
2025-05-20 10:37:20.501811Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name http, id 8
2025-05-20 10:37:20.501821Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name tcp, id 9
2025-05-20 10:37:20.501831Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name udp, id 10
2025-05-20 10:37:20.501841Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCheck, id 19
2025-05-20 10:37:20.501852Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCount, id 20
2025-05-20 10:37:20.501865Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorRate, id 21
2025-05-20 10:37:20.501898Z	info	base	plugin/manage.go:236	Initialized plugin type weightAdjuster, name rateDelayAdjuster, id 18
2025-05-20 10:37:20.508023Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name stat2Monitor, id 26
2025-05-20 10:37:20.508051Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name rateLimitRecord, id 27
2025-05-20 10:37:20.508069Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceCache, id 28
2025-05-20 10:37:20.508083Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceRoute, id 29
2025-05-20 10:37:20.508093Z	info	base	plugin/manage.go:236	Initialized plugin type alarmReporter, name alarm2file, id 1
2025-05-20 10:37:20.508106Z	info	base	inmemory/inmemory.go:166	LocalCache Real persistDir:/data/home/<USER>/polaris/backup
2025-05-20 10:37:20.508168Z	info	base	plugin/manage.go:236	Initialized plugin type localRegistry, name inmemory, id 23
2025-05-20 10:37:20.508182Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name reject, id 11
2025-05-20 10:37:20.508195Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name unirate, id 24
2025-05-20 10:37:20.508215Z	info	base	plugin/manage.go:236	Initialized plugin type subScribe, name subscribeLocalChannel, id 25
2025-05-20 10:37:20.512838Z	info	base	api/config.go:348	
1B6FD704-82C7-4CE2-980F-E7D55A482ACB, -------Configuration with default value-------
global:
  system:
    mode: 0
    discoverCluster:
      namespace: Polaris
      service: polaris.discover
      refreshInterval: 10m0s
    healthCheckCluster:
      namespace: Polaris
      service: polaris.healthcheck
      refreshInterval: 10m0s
    monitorCluster:
      namespace: Polaris
      service: polaris.monitor
      refreshInterval: 10m0s
    rateLimitCluster: null
    metricCluster: null
    dynamicWeightCluster:
      namespace: Polaris
      service: polaris.dynamic.weight
      refreshInterval: 10m0s
    variables: {}
    apiVersion: v2
  api:
    timeout: 1s
    bindIf: ""
    bindIP: ""
    reportInterval: 10m0s
    maxRetryTimes: 1
    retryInterval: 1s
    location:
      region: ""
      zone: ""
      campus: ""
      enableUpdate: true
  serverConnector:
    addresses: []
    backupAddresses: []
    protocol: grpc
    connectTimeout: 1s
    messageTimeout: 1.5s
    watchTimeout: 1m0s
    connectionIdleTimeout: 3s
    requestQueueSize: 1000
    serverSwitchInterval: 10m0s
    reconnectInterval: 500ms
    apiVersion: v2
    joinPoint: ""
    plugin:
      grpc:
        maxCallRecvMsgSize: 52428800
  statReporter:
    enable: true
    chain:
    - stat2Monitor
    - serviceCache
    - rateLimitRecord
    - serviceRoute
    plugin:
      rateLimitRecord:
        reportInterval: 1m0s
      serviceCache:
        reportInterval: 3m0s
      serviceRoute:
        reportInterval: 5m0s
      stat2Monitor:
        metricsReportWindow: 1m0s
        metricsNumBuckets: 12
consumer:
  localCache:
    serviceExpireTime: 24h0m0s
    serviceRefreshInterval: 2s
    persistDir: $HOME/polaris/backup
    enablePersistence: null
    type: inmemory
    persistMaxWriteRetry: 5
    persistMaxReadRetry: 1
    persistRetryInterval: 1s
    persistAvailableInterval: 1m0s
    startUseFileCache: true
    pushEmptyProtection: false
    plugin: {}
  serviceRouter:
    chain:
    - dstMetaRouter
    - ruleBasedRouter
    - nearbyBasedRouter
    plugin:
      nearbyBasedRouter:
        matchLevel: zone
        maxMatchLevel: ""
        strictNearby: false
        enableDegradeByUnhealthyPercent: true
        unhealthyPercentToDegrade: 100
    percentOfMinInstances: 0
    disableRouteSourceContinue: false
    enableRecoverAll: true
  loadbalancer:
    type: weightedRandom
    plugin:
      hash:
        hashFunction: murmur3
      maglev:
        hashFunction: murmur3
        tableSize: 65537
      ringHash:
        hashFunction: murmur3
        vnodeCount: 10
  circuitBreaker:
    enable: true
    checkPeriod: 30s
    chain:
    - errorCount
    - errorRate
    - errorCheck
    sleepWindow: 30s
    requestCountAfterHalfOpen: 10
    successCountAfterHalfOpen: 8
    recoverWindow: 1m0s
    recoverNumBuckets: 10
    plugin:
      errorCount:
        continuousErrorThreshold: 10
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 10
      errorRate:
        requestVolumeThreshold: 10
        errorRatePercent: 50
        errorRateThreshold: 0
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 5
  healthCheck:
    interval: 10s
    chain: []
    plugin:
      http:
        timeout: 100ms
        path: ""
      tcp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
      udp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
    when: never
    concurrency: 0
  subscribe:
    type: subscribeLocalChannel
    plugin:
      subscribeLocalChannel:
        channelBufferSize: 30
  servicesSpecific:
  - namespace: Polaris
    service: polaris.discover
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.healthcheck
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.monitor
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.dynamic.weight
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
provider:
  rateLimit:
    enable: true
    plugin:
      unirate:
        maxQueuingTime: 1s
    mode: "\x01"
    rateLimitCluster:
      namespace: ""
      service: ""
      refreshInterval: 10m0s
    maxWindowSize: 20000
    purgeInterval: 1m0s

2025-05-20 10:37:20.512903Z	info	base	api/config.go:353	
-------1B6FD704-82C7-4CE2-980F-E7D55A482ACB, All plugins and engine initialized successfully-------
2025-05-20 10:37:20.512963Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/client_info.json
2025-05-20 10:37:20.513033Z	info	base	network/impl.go:370	connectionManager start updateDiscover
2025-05-20 10:37:20.513174Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:37:20.513197Z	info	base	inmemory/inmemory.go:550	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:37:20.513235Z	info	base	common/register.go:52	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:37:20.513256Z	info	base	common/register.go:65	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:37:20.513267Z	info	base	inmemory/inmemory.go:552	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}, err <nil>
2025-05-20 10:37:20.513283Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:37:20.513295Z	info	base	inmemory/inmemory.go:550	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:37:20.513306Z	info	base	common/register.go:52	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:37:20.513316Z	info	base	common/register.go:65	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:37:20.513325Z	info	base	inmemory/inmemory.go:552	1B6FD704-82C7-4CE2-980F-E7D55A482ACB, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}, err <nil>
2025-05-20 10:37:20.513378Z	info	base	network/impl.go:410	get connection of builtin for System
2025-05-20 10:37:20.514553Z	info	base	startup/client_report.go:138	current client area info is Host:**********-{Region:华南, Zone:广州, Campus:广州三区}
2025-05-20 10:37:20.514583Z	info	base	startup/client_report.go:144	client area info is ready
2025-05-20 10:37:20.514597Z	info	base	schedule/routines.go:228	item clientReportTask in task clientReportTask has added
2025-05-20 10:37:20.514610Z	info	base	schedule/routines.go:115	task clientReportTask started period 5m0s
2025-05-20 10:37:20.514628Z	info	base	schedule/routines.go:277	task clientReportTask has been started
2025-05-20 10:37:20.514638Z	info	base	schedule/routines.go:228	item sdkConfigReportTask in task sdkConfigReportTask has added
2025-05-20 10:37:20.514647Z	info	base	schedule/routines.go:115	task sdkConfigReportTask started period 2m30s
2025-05-20 10:37:20.514656Z	info	base	schedule/routines.go:277	task sdkConfigReportTask has been started
2025-05-20 10:37:20.514663Z	info	base	api/config.go:363	
-------1B6FD704-82C7-4CE2-980F-E7D55A482ACB, All plugins and engine started successfully-------
2025-05-20 10:37:20.514678Z	info	base	api/config.go:371	
-------1B6FD704-82C7-4CE2-980F-E7D55A482ACB, SDKContext init successfully-------
2025-05-20 10:37:20.514762Z	info	base	grpc/creator.go:88	localAddress from connection is **********:34942, IP is **********, hashValue is 8140490996141982352
2025-05-20 10:37:20.516613Z	warn	base	config/default.go:401	no IP or interface name configured
2025-05-20 10:37:20.516671Z	info	base	api/config.go:321	
-------Start to init SDKContext of version 0.12.12, IP: , PID: 2644637, UID: FACA2E42-B74B-414E-B845-84B4B82FC3F5, CONTAINER: , HOSTNAME:VM-9-17-tencentos-------
2025-05-20 10:37:20.516815Z	info	base	grpc/operation_async.go:88	set grpc plugin as connectionCreator
2025-05-20 10:37:20.516847Z	info	base	plugin/manage.go:236	Initialized plugin type serverConnector, name grpc, id 30
2025-05-20 10:37:20.516858Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name filterOnlyRouter, id 14
2025-05-20 10:37:20.517065Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name nearbyBasedRouter, id 15
2025-05-20 10:37:20.517262Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name ruleBasedRouter, id 16
2025-05-20 10:37:20.517272Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name setDivisionRouter, id 17
2025-05-20 10:37:20.517280Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name canaryRouter, id 12
2025-05-20 10:37:20.517289Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name dstMetaRouter, id 13
2025-05-20 10:37:20.517298Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name hash, id 2
2025-05-20 10:37:20.517307Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name maglev, id 3
2025-05-20 10:37:20.517316Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name cMurmurHash, id 4
2025-05-20 10:37:20.517324Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name l5cst, id 5
2025-05-20 10:37:20.517332Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name ringHash, id 6
2025-05-20 10:37:20.517520Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name weightedRandom, id 7
2025-05-20 10:37:20.517703Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name dynamicWeightedRandom, id 22
2025-05-20 10:37:20.517715Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name http, id 8
2025-05-20 10:37:20.517723Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name tcp, id 9
2025-05-20 10:37:20.517732Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name udp, id 10
2025-05-20 10:37:20.517741Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCheck, id 19
2025-05-20 10:37:20.517751Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCount, id 20
2025-05-20 10:37:20.517766Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorRate, id 21
2025-05-20 10:37:20.517774Z	info	base	plugin/manage.go:236	Initialized plugin type weightAdjuster, name rateDelayAdjuster, id 18
2025-05-20 10:37:20.523575Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name stat2Monitor, id 26
2025-05-20 10:37:20.523607Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name rateLimitRecord, id 27
2025-05-20 10:37:20.523629Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceCache, id 28
2025-05-20 10:37:20.523643Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceRoute, id 29
2025-05-20 10:37:20.523652Z	info	base	plugin/manage.go:236	Initialized plugin type alarmReporter, name alarm2file, id 1
2025-05-20 10:37:20.523671Z	info	base	inmemory/inmemory.go:166	LocalCache Real persistDir:/data/home/<USER>/polaris/backup
2025-05-20 10:37:20.523710Z	info	base	plugin/manage.go:236	Initialized plugin type localRegistry, name inmemory, id 23
2025-05-20 10:37:20.523720Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name reject, id 11
2025-05-20 10:37:20.523731Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name unirate, id 24
2025-05-20 10:37:20.523742Z	info	base	plugin/manage.go:236	Initialized plugin type subScribe, name subscribeLocalChannel, id 25
2025-05-20 10:37:20.526822Z	info	base	api/config.go:348	
FACA2E42-B74B-414E-B845-84B4B82FC3F5, -------Configuration with default value-------
global:
  system:
    mode: 0
    discoverCluster:
      namespace: Polaris
      service: polaris.discover
      refreshInterval: 10m0s
    healthCheckCluster:
      namespace: Polaris
      service: polaris.healthcheck
      refreshInterval: 10m0s
    monitorCluster:
      namespace: Polaris
      service: polaris.monitor
      refreshInterval: 10m0s
    rateLimitCluster: null
    metricCluster: null
    dynamicWeightCluster:
      namespace: Polaris
      service: polaris.dynamic.weight
      refreshInterval: 10m0s
    variables: {}
    apiVersion: v2
  api:
    timeout: 1s
    bindIf: ""
    bindIP: ""
    reportInterval: 10m0s
    maxRetryTimes: 1
    retryInterval: 1s
    location:
      region: ""
      zone: ""
      campus: ""
      enableUpdate: true
  serverConnector:
    addresses: []
    backupAddresses: []
    protocol: grpc
    connectTimeout: 1s
    messageTimeout: 1s
    watchTimeout: 1m0s
    connectionIdleTimeout: 3s
    requestQueueSize: 1000
    serverSwitchInterval: 10m0s
    reconnectInterval: 500ms
    apiVersion: v2
    joinPoint: ""
    plugin:
      grpc:
        maxCallRecvMsgSize: 52428800
  statReporter:
    enable: true
    chain:
    - stat2Monitor
    - serviceCache
    - rateLimitRecord
    - serviceRoute
    plugin:
      rateLimitRecord:
        reportInterval: 1m0s
      serviceCache:
        reportInterval: 3m0s
      serviceRoute:
        reportInterval: 5m0s
      stat2Monitor:
        metricsReportWindow: 1m0s
        metricsNumBuckets: 12
consumer:
  localCache:
    serviceExpireTime: 24h0m0s
    serviceRefreshInterval: 2s
    persistDir: $HOME/polaris/backup
    enablePersistence: null
    type: inmemory
    persistMaxWriteRetry: 5
    persistMaxReadRetry: 1
    persistRetryInterval: 1s
    persistAvailableInterval: 1m0s
    startUseFileCache: true
    pushEmptyProtection: false
    plugin: {}
  serviceRouter:
    chain:
    - dstMetaRouter
    - ruleBasedRouter
    - nearbyBasedRouter
    plugin:
      nearbyBasedRouter:
        matchLevel: zone
        maxMatchLevel: ""
        strictNearby: false
        enableDegradeByUnhealthyPercent: true
        unhealthyPercentToDegrade: 100
    percentOfMinInstances: 0
    disableRouteSourceContinue: false
    enableRecoverAll: true
  loadbalancer:
    type: weightedRandom
    plugin:
      hash:
        hashFunction: murmur3
      maglev:
        hashFunction: murmur3
        tableSize: 65537
      ringHash:
        hashFunction: murmur3
        vnodeCount: 10
  circuitBreaker:
    enable: true
    checkPeriod: 30s
    chain:
    - errorCount
    - errorRate
    - errorCheck
    sleepWindow: 30s
    requestCountAfterHalfOpen: 10
    successCountAfterHalfOpen: 8
    recoverWindow: 1m0s
    recoverNumBuckets: 10
    plugin:
      errorCount:
        continuousErrorThreshold: 10
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 10
      errorRate:
        requestVolumeThreshold: 10
        errorRatePercent: 50
        errorRateThreshold: 0
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 5
  healthCheck:
    interval: 10s
    chain:
    - tcp
    plugin:
      http:
        timeout: 100ms
        path: ""
      tcp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
      udp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
    when: never
    concurrency: 0
  subscribe:
    type: subscribeLocalChannel
    plugin:
      subscribeLocalChannel:
        channelBufferSize: 30
  servicesSpecific:
  - namespace: Polaris
    service: polaris.discover
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.healthcheck
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.monitor
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.dynamic.weight
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
provider:
  rateLimit:
    enable: true
    plugin:
      unirate:
        maxQueuingTime: 1s
    mode: "\x01"
    rateLimitCluster:
      namespace: ""
      service: ""
      refreshInterval: 10m0s
    maxWindowSize: 20000
    purgeInterval: 1m0s

2025-05-20 10:37:20.526852Z	info	base	api/config.go:353	
-------FACA2E42-B74B-414E-B845-84B4B82FC3F5, All plugins and engine initialized successfully-------
2025-05-20 10:37:20.526920Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/client_info.json
2025-05-20 10:37:20.526940Z	info	base	network/impl.go:370	connectionManager start updateDiscover
2025-05-20 10:37:20.528029Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:37:20.528118Z	info	base	inmemory/inmemory.go:550	FACA2E42-B74B-414E-B845-84B4B82FC3F5, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:37:20.528151Z	info	base	common/register.go:52	FACA2E42-B74B-414E-B845-84B4B82FC3F5, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:37:20.527993Z	info	base	startup/client_report.go:138	current client area info is Host:**********-{Region:华南, Zone:广州, Campus:广州三区}
2025-05-20 10:37:20.528187Z	info	base	common/register.go:65	FACA2E42-B74B-414E-B845-84B4B82FC3F5, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:37:20.528193Z	info	base	startup/client_report.go:144	client area info is ready
2025-05-20 10:37:20.528209Z	info	base	inmemory/inmemory.go:552	FACA2E42-B74B-414E-B845-84B4B82FC3F5, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}, err <nil>
2025-05-20 10:37:20.528234Z	info	base	schedule/routines.go:228	item clientReportTask in task clientReportTask has added
2025-05-20 10:37:20.528272Z	info	base	schedule/routines.go:115	task clientReportTask started period 5m0s
2025-05-20 10:37:20.528312Z	info	base	schedule/routines.go:277	task clientReportTask has been started
2025-05-20 10:37:20.528244Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:37:20.528341Z	info	base	schedule/routines.go:228	item sdkConfigReportTask in task sdkConfigReportTask has added
2025-05-20 10:37:20.528368Z	info	base	schedule/routines.go:115	task sdkConfigReportTask started period 2m30s
2025-05-20 10:37:20.528355Z	info	base	inmemory/inmemory.go:550	FACA2E42-B74B-414E-B845-84B4B82FC3F5, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:37:20.528381Z	info	base	schedule/routines.go:277	task sdkConfigReportTask has been started
2025-05-20 10:37:20.528406Z	info	base	api/config.go:363	
-------FACA2E42-B74B-414E-B845-84B4B82FC3F5, All plugins and engine started successfully-------
2025-05-20 10:37:20.528388Z	info	base	common/register.go:52	FACA2E42-B74B-414E-B845-84B4B82FC3F5, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:37:20.528426Z	info	base	api/config.go:371	
-------FACA2E42-B74B-414E-B845-84B4B82FC3F5, SDKContext init successfully-------
2025-05-20 10:37:20.528445Z	info	base	common/register.go:65	FACA2E42-B74B-414E-B845-84B4B82FC3F5, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:37:20.528515Z	info	base	inmemory/inmemory.go:552	FACA2E42-B74B-414E-B845-84B4B82FC3F5, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}, err <nil>
2025-05-20 10:37:24.254525Z	info	base	network/impl.go:410	get connection of builtin for System
2025-05-20 10:37:24.254615Z	error	base	flow/sync_flow.go:182	fail to get resource of {namespace: "Polaris", service: "polaris.discover"} for timeout, retryTimes: 0, total consumed time: 10.48934ms, total sleep time: 0s
2025-05-20 10:37:24.254655Z	error	base	flow/sync_flow.go:186	retry times exceed 0 in SyncGetResources, serviceKey: {namespace: "Polaris", service: "polaris.discover"}, timeout is 300ms
2025-05-20 10:37:24.254717Z	error	base	flow/sync_flow.go:182	fail to get resource of {namespace: "Polaris", service: "polaris.discover"} for timeout, retryTimes: 0, total consumed time: 0s, total sleep time: 0s
2025-05-20 10:37:24.254738Z	error	base	network/impl.go:374	connectionManager get discover service err: polaris-go version: 0.12.12, Polaris-1004(ErrCodeAPITimeoutError): retry times exceed 0 in SyncGetResources, serviceKey: {namespace: "Polaris", service: "polaris.discover"}, timeout is 300ms
2025-05-20 10:37:24.254763Z	error	base	flow/sync_flow.go:186	retry times exceed 0 in SyncGetResources, serviceKey: {namespace: "Polaris", service: "polaris.discover"}, timeout is 300ms
2025-05-20 10:37:24.254737Z	error	base	common/cache_persist.go:130	fail to load cache from file svcV2#Polaris#polaris.discover#routing.json, svcKey: {namespace: "Polaris", service: "polaris.discover", event: routing}, error is Fail to Stat the cache file name /data/home/<USER>/polaris/backup/svcV2#Polaris#polaris.discover#routing.json:  stat /data/home/<USER>/polaris/backup/svcV2#Polaris#polaris.discover#routing.json: no such file or directory
2025-05-20 10:39:02.942827Z	info	base	api/config.go:293	global clock started
2025-05-20 10:39:02.943006Z	warn	base	config/default.go:401	no IP or interface name configured
2025-05-20 10:39:02.943067Z	info	base	api/config.go:321	
-------Start to init SDKContext of version 0.12.12, IP: , PID: 2646012, UID: EA168753-AD9B-4BEA-8CB4-4D4B1F747099, CONTAINER: , HOSTNAME:VM-9-17-tencentos-------
2025-05-20 10:39:02.943273Z	info	base	grpc/operation_async.go:88	set grpc plugin as connectionCreator
2025-05-20 10:39:02.943302Z	info	base	plugin/manage.go:236	Initialized plugin type serverConnector, name grpc, id 30
2025-05-20 10:39:02.943483Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name nearbyBasedRouter, id 15
2025-05-20 10:39:02.943666Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name ruleBasedRouter, id 16
2025-05-20 10:39:02.943677Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name setDivisionRouter, id 17
2025-05-20 10:39:02.943687Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name canaryRouter, id 12
2025-05-20 10:39:02.943697Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name dstMetaRouter, id 13
2025-05-20 10:39:02.943706Z	info	base	plugin/manage.go:236	Initialized plugin type serviceRouter, name filterOnlyRouter, id 14
2025-05-20 10:39:02.943912Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name weightedRandom, id 7
2025-05-20 10:39:02.944135Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name dynamicWeightedRandom, id 22
2025-05-20 10:39:02.944149Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name hash, id 2
2025-05-20 10:39:02.944160Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name maglev, id 3
2025-05-20 10:39:02.944169Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name cMurmurHash, id 4
2025-05-20 10:39:02.944179Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name l5cst, id 5
2025-05-20 10:39:02.944201Z	info	base	plugin/manage.go:236	Initialized plugin type loadBalancer, name ringHash, id 6
2025-05-20 10:39:02.944213Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name http, id 8
2025-05-20 10:39:02.944223Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name tcp, id 9
2025-05-20 10:39:02.944233Z	info	base	plugin/manage.go:236	Initialized plugin type outlierDetector, name udp, id 10
2025-05-20 10:39:02.944244Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCheck, id 19
2025-05-20 10:39:02.944255Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorCount, id 20
2025-05-20 10:39:02.944268Z	info	base	plugin/manage.go:236	Initialized plugin type circuitBreaker, name errorRate, id 21
2025-05-20 10:39:02.944278Z	info	base	plugin/manage.go:236	Initialized plugin type weightAdjuster, name rateDelayAdjuster, id 18
2025-05-20 10:39:02.944290Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name rateLimitRecord, id 27
2025-05-20 10:39:02.944308Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceCache, id 28
2025-05-20 10:39:02.944322Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name serviceRoute, id 29
2025-05-20 10:39:02.952131Z	info	base	plugin/manage.go:236	Initialized plugin type statReporter, name stat2Monitor, id 26
2025-05-20 10:39:02.952184Z	info	base	plugin/manage.go:236	Initialized plugin type alarmReporter, name alarm2file, id 1
2025-05-20 10:39:02.952211Z	info	base	inmemory/inmemory.go:166	LocalCache Real persistDir:/data/home/<USER>/polaris/backup
2025-05-20 10:39:02.952271Z	info	base	plugin/manage.go:236	Initialized plugin type localRegistry, name inmemory, id 23
2025-05-20 10:39:02.952284Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name reject, id 11
2025-05-20 10:39:02.952297Z	info	base	plugin/manage.go:236	Initialized plugin type rateLimiter, name unirate, id 24
2025-05-20 10:39:02.952317Z	info	base	plugin/manage.go:236	Initialized plugin type subScribe, name subscribeLocalChannel, id 25
2025-05-20 10:39:02.955239Z	info	base	api/config.go:348	
EA168753-AD9B-4BEA-8CB4-4D4B1F747099, -------Configuration with default value-------
global:
  system:
    mode: 0
    discoverCluster:
      namespace: Polaris
      service: polaris.discover
      refreshInterval: 10m0s
    healthCheckCluster:
      namespace: Polaris
      service: polaris.healthcheck
      refreshInterval: 10m0s
    monitorCluster:
      namespace: Polaris
      service: polaris.monitor
      refreshInterval: 10m0s
    rateLimitCluster: null
    metricCluster: null
    dynamicWeightCluster:
      namespace: Polaris
      service: polaris.dynamic.weight
      refreshInterval: 10m0s
    variables: {}
    apiVersion: v2
  api:
    timeout: 1s
    bindIf: ""
    bindIP: ""
    reportInterval: 10m0s
    maxRetryTimes: 1
    retryInterval: 1s
    location:
      region: ""
      zone: ""
      campus: ""
      enableUpdate: true
  serverConnector:
    addresses: []
    backupAddresses: []
    protocol: grpc
    connectTimeout: 1s
    messageTimeout: 1s
    watchTimeout: 1m0s
    connectionIdleTimeout: 3s
    requestQueueSize: 1000
    serverSwitchInterval: 10m0s
    reconnectInterval: 500ms
    apiVersion: v2
    joinPoint: ""
    plugin:
      grpc:
        maxCallRecvMsgSize: 52428800
  statReporter:
    enable: true
    chain:
    - stat2Monitor
    - serviceCache
    - rateLimitRecord
    - serviceRoute
    plugin:
      rateLimitRecord:
        reportInterval: 1m0s
      serviceCache:
        reportInterval: 3m0s
      serviceRoute:
        reportInterval: 5m0s
      stat2Monitor:
        metricsReportWindow: 1m0s
        metricsNumBuckets: 12
consumer:
  localCache:
    serviceExpireTime: 24h0m0s
    serviceRefreshInterval: 2s
    persistDir: $HOME/polaris/backup
    enablePersistence: null
    type: inmemory
    persistMaxWriteRetry: 5
    persistMaxReadRetry: 1
    persistRetryInterval: 1s
    persistAvailableInterval: 1m0s
    startUseFileCache: true
    pushEmptyProtection: false
    plugin: {}
  serviceRouter:
    chain:
    - dstMetaRouter
    - ruleBasedRouter
    - nearbyBasedRouter
    plugin:
      nearbyBasedRouter:
        matchLevel: zone
        maxMatchLevel: ""
        strictNearby: false
        enableDegradeByUnhealthyPercent: true
        unhealthyPercentToDegrade: 100
    percentOfMinInstances: 0
    disableRouteSourceContinue: false
    enableRecoverAll: true
  loadbalancer:
    type: weightedRandom
    plugin:
      hash:
        hashFunction: murmur3
      maglev:
        hashFunction: murmur3
        tableSize: 65537
      ringHash:
        hashFunction: murmur3
        vnodeCount: 10
  circuitBreaker:
    enable: true
    checkPeriod: 30s
    chain:
    - errorCount
    - errorRate
    - errorCheck
    sleepWindow: 30s
    requestCountAfterHalfOpen: 10
    successCountAfterHalfOpen: 8
    recoverWindow: 1m0s
    recoverNumBuckets: 10
    plugin:
      errorCount:
        continuousErrorThreshold: 10
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 10
      errorRate:
        requestVolumeThreshold: 10
        errorRatePercent: 50
        errorRateThreshold: 0
        metricStatTimeWindow: 1m0s
        metricNumBuckets: 5
  healthCheck:
    interval: 10s
    chain:
    - tcp
    plugin:
      http:
        timeout: 100ms
        path: ""
      tcp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
      udp:
        timeout: 100ms
        retry: 0
        send: ""
        receive: ""
    when: never
    concurrency: 0
  subscribe:
    type: subscribeLocalChannel
    plugin:
      subscribeLocalChannel:
        channelBufferSize: 30
  servicesSpecific:
  - namespace: Polaris
    service: polaris.discover
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.healthcheck
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.monitor
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
  - namespace: Polaris
    service: polaris.dynamic.weight
    serviceRouter:
      chain:
      - dstMetaRouter
      - nearbyBasedRouter
      plugin:
        nearbyBasedRouter:
          matchLevel: region
          maxMatchLevel: ""
          strictNearby: false
          enableDegradeByUnhealthyPercent: true
          unhealthyPercentToDegrade: 100
      percentOfMinInstances: 0
      disableRouteSourceContinue: false
      enableRecoverAll: true
    circuitBreaker:
      enable: true
      checkPeriod: 30s
      chain:
      - errorCount
      - errorRate
      - errorCheck
      sleepWindow: 30s
      requestCountAfterHalfOpen: 10
      successCountAfterHalfOpen: 8
      recoverWindow: 1m0s
      recoverNumBuckets: 10
      plugin:
        errorCount:
          continuousErrorThreshold: 1
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 10
        errorRate:
          requestVolumeThreshold: 10
          errorRatePercent: 50
          errorRateThreshold: 0
          metricStatTimeWindow: 1m0s
          metricNumBuckets: 5
    healthCheck:
      interval: 10s
      chain: []
      plugin:
        http:
          timeout: 100ms
          path: ""
        tcp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
        udp:
          timeout: 100ms
          retry: 0
          send: ""
          receive: ""
      when: never
      concurrency: 0
provider:
  rateLimit:
    enable: true
    plugin:
      unirate:
        maxQueuingTime: 1s
    mode: "\x01"
    rateLimitCluster:
      namespace: ""
      service: ""
      refreshInterval: 10m0s
    maxWindowSize: 20000
    purgeInterval: 1m0s

2025-05-20 10:39:02.955269Z	info	base	api/config.go:353	
-------EA168753-AD9B-4BEA-8CB4-4D4B1F747099, All plugins and engine initialized successfully-------
2025-05-20 10:39:02.955326Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/client_info.json
2025-05-20 10:39:02.955374Z	info	base	network/impl.go:370	connectionManager start updateDiscover
2025-05-20 10:39:02.955484Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:39:02.955504Z	info	base	inmemory/inmemory.go:550	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}
2025-05-20 10:39:02.955523Z	info	base	common/register.go:52	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:39:02.955541Z	info	base	common/register.go:65	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: instance, initialized: firstTask}
2025-05-20 10:39:02.955559Z	info	base	inmemory/inmemory.go:552	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: instance}, err <nil>
2025-05-20 10:39:02.955580Z	info	base	inmemory/inmemory.go:1201	start to add load cache task for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:39:02.955600Z	info	base	inmemory/inmemory.go:550	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, start to register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}
2025-05-20 10:39:02.955611Z	info	base	common/register.go:52	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, addTask: start to add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:39:02.955621Z	info	base	common/register.go:65	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, addTask: finish add first task for {namespace: "Polaris", service: "polaris.discover", event: routing, initialized: firstTask}
2025-05-20 10:39:02.955633Z	info	base	inmemory/inmemory.go:552	EA168753-AD9B-4BEA-8CB4-4D4B1F747099, finish register event handler for {namespace: "Polaris", service: "polaris.discover", event: routing}, err <nil>
2025-05-20 10:39:02.955690Z	info	base	network/impl.go:410	get connection of builtin for System
2025-05-20 10:39:02.956799Z	info	base	startup/client_report.go:138	current client area info is Host:**********-{Region:华南, Zone:广州, Campus:广州三区}
2025-05-20 10:39:02.956822Z	info	base	startup/client_report.go:144	client area info is ready
2025-05-20 10:39:02.956835Z	info	base	schedule/routines.go:228	item clientReportTask in task clientReportTask has added
2025-05-20 10:39:02.956848Z	info	base	schedule/routines.go:115	task clientReportTask started period 5m0s
2025-05-20 10:39:02.956879Z	info	base	schedule/routines.go:277	task clientReportTask has been started
2025-05-20 10:39:02.956900Z	info	base	schedule/routines.go:228	item sdkConfigReportTask in task sdkConfigReportTask has added
2025-05-20 10:39:02.956911Z	info	base	schedule/routines.go:115	task sdkConfigReportTask started period 2m30s
2025-05-20 10:39:02.956919Z	info	base	schedule/routines.go:277	task sdkConfigReportTask has been started
2025-05-20 10:39:02.956927Z	info	base	api/config.go:363	
-------EA168753-AD9B-4BEA-8CB4-4D4B1F747099, All plugins and engine started successfully-------
2025-05-20 10:39:02.956941Z	info	base	api/config.go:371	
-------EA168753-AD9B-4BEA-8CB4-4D4B1F747099, SDKContext init successfully-------
2025-05-20 10:39:06.423777Z	error	base	common/cache_persist.go:130	fail to load cache from file svcV2#Polaris#polaris.discover#instance.json, svcKey: {namespace: "Polaris", service: "polaris.discover", event: instance}, error is Fail to Stat the cache file name /data/home/<USER>/polaris/backup/svcV2#Polaris#polaris.discover#instance.json:  stat /data/home/<USER>/polaris/backup/svcV2#Polaris#polaris.discover#instance.json: no such file or directory
2025-05-20 10:39:06.423896Z	info	base	common/cache_persist.go:182	Start to load cache from /data/home/<USER>/polaris/backup/svc#Polaris#polaris.discover#instance.json
2025-05-20 10:39:06.423894Z	info	base	grpc/creator.go:88	localAddress from connection is **********:49076, IP is **********, hashValue is 8140490996141982352
2025-05-20 10:39:06.424015Z	error	base	flow/sync_flow.go:182	fail to get resource of {namespace: "Polaris", service: "polaris.discover"} for timeout, retryTimes: 0, total consumed time: 3.470626043s, total sleep time: 0s
2025-05-20 10:39:06.424048Z	error	base	flow/sync_flow.go:186	retry times exceed 0 in SyncGetResources, serviceKey: {namespace: "Polaris", service: "polaris.discover"}, timeout is 300ms
2025-05-20 10:39:06.424072Z	info	base	network/impl.go:410	get connection of builtin for System
2025-05-20 10:39:06.424106Z	error	base	network/impl.go:374	connectionManager get discover service err: polaris-go version: 0.12.12, Polaris-1004(ErrCodeAPITimeoutError): retry times exceed 0 in SyncGetResources, serviceKey: {namespace: "Polaris", service: "polaris.discover"}, timeout is 300ms
