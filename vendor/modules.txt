# git.code.oa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos v0.1.13
## explicit; go 1.17
git.code.oa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos
# git.code.oa.com/QzonePlatform/QzoneProtocol v0.0.0-20250416023433-22bbdb0c1134
## explicit; go 1.13
git.code.oa.com/QzonePlatform/QzoneProtocol/tarsgowoa/PHOTO
git.code.oa.com/QzonePlatform/QzoneProtocol/tarsgowoa/SLICE_UPLOAD
git.code.oa.com/QzonePlatform/QzoneProtocol/tarsgowoa/photo_struct_wrap
# git.code.oa.com/polaris/polaris-go v0.12.12
## explicit; go 1.13
git.code.oa.com/polaris/polaris-go/api
git.code.oa.com/polaris/polaris-go/pkg/algorithm/hash
git.code.oa.com/polaris/polaris-go/pkg/algorithm/rand
git.code.oa.com/polaris/polaris-go/pkg/algorithm/search
git.code.oa.com/polaris/polaris-go/pkg/clock
git.code.oa.com/polaris/polaris-go/pkg/config
git.code.oa.com/polaris/polaris-go/pkg/flow
git.code.oa.com/polaris/polaris-go/pkg/flow/cbcheck
git.code.oa.com/polaris/polaris-go/pkg/flow/data
git.code.oa.com/polaris/polaris-go/pkg/flow/detect
git.code.oa.com/polaris/polaris-go/pkg/flow/quota
git.code.oa.com/polaris/polaris-go/pkg/flow/schedule
git.code.oa.com/polaris/polaris-go/pkg/flow/startup
git.code.oa.com/polaris/polaris-go/pkg/log
git.code.oa.com/polaris/polaris-go/pkg/metric
git.code.oa.com/polaris/polaris-go/pkg/model
git.code.oa.com/polaris/polaris-go/pkg/model/local
git.code.oa.com/polaris/polaris-go/pkg/model/pb
git.code.oa.com/polaris/polaris-go/pkg/network
git.code.oa.com/polaris/polaris-go/pkg/plugin
git.code.oa.com/polaris/polaris-go/pkg/plugin/alarmreporter
git.code.oa.com/polaris/polaris-go/pkg/plugin/circuitbreaker
git.code.oa.com/polaris/polaris-go/pkg/plugin/common
git.code.oa.com/polaris/polaris-go/pkg/plugin/loadbalancer
git.code.oa.com/polaris/polaris-go/pkg/plugin/localregistry
git.code.oa.com/polaris/polaris-go/pkg/plugin/outlierdetection
git.code.oa.com/polaris/polaris-go/pkg/plugin/ratelimiter
git.code.oa.com/polaris/polaris-go/pkg/plugin/register
git.code.oa.com/polaris/polaris-go/pkg/plugin/serverconnector
git.code.oa.com/polaris/polaris-go/pkg/plugin/servicerouter
git.code.oa.com/polaris/polaris-go/pkg/plugin/statreporter
git.code.oa.com/polaris/polaris-go/pkg/plugin/subscribe
git.code.oa.com/polaris/polaris-go/pkg/plugin/weightadjuster
git.code.oa.com/polaris/polaris-go/pkg/trie
git.code.oa.com/polaris/polaris-go/pkg/version
git.code.oa.com/polaris/polaris-go/plugin/alarmreporter/file
git.code.oa.com/polaris/polaris-go/plugin/circuitbreaker/common
git.code.oa.com/polaris/polaris-go/plugin/circuitbreaker/errorcheck
git.code.oa.com/polaris/polaris-go/plugin/circuitbreaker/errorcount
git.code.oa.com/polaris/polaris-go/plugin/circuitbreaker/errorrate
git.code.oa.com/polaris/polaris-go/plugin/loadbalancer/common
git.code.oa.com/polaris/polaris-go/plugin/loadbalancer/dynamicweightrandom
git.code.oa.com/polaris/polaris-go/plugin/loadbalancer/hash
git.code.oa.com/polaris/polaris-go/plugin/loadbalancer/maglev
git.code.oa.com/polaris/polaris-go/plugin/loadbalancer/ringhash
git.code.oa.com/polaris/polaris-go/plugin/loadbalancer/weightedrandom
git.code.oa.com/polaris/polaris-go/plugin/localregistry/common
git.code.oa.com/polaris/polaris-go/plugin/localregistry/inmemory
git.code.oa.com/polaris/polaris-go/plugin/logger/zaplog
git.code.oa.com/polaris/polaris-go/plugin/outlierdetection/http
git.code.oa.com/polaris/polaris-go/plugin/outlierdetection/tcp
git.code.oa.com/polaris/polaris-go/plugin/outlierdetection/udp
git.code.oa.com/polaris/polaris-go/plugin/outlierdetection/utils
git.code.oa.com/polaris/polaris-go/plugin/ratelimiter/reject
git.code.oa.com/polaris/polaris-go/plugin/ratelimiter/unirate
git.code.oa.com/polaris/polaris-go/plugin/serverconnector/common
git.code.oa.com/polaris/polaris-go/plugin/serverconnector/grpc
git.code.oa.com/polaris/polaris-go/plugin/servicerouter/canary
git.code.oa.com/polaris/polaris-go/plugin/servicerouter/common
git.code.oa.com/polaris/polaris-go/plugin/servicerouter/dstmeta
git.code.oa.com/polaris/polaris-go/plugin/servicerouter/filteronly
git.code.oa.com/polaris/polaris-go/plugin/servicerouter/nearbybase
git.code.oa.com/polaris/polaris-go/plugin/servicerouter/rulebase
git.code.oa.com/polaris/polaris-go/plugin/servicerouter/setdivision
git.code.oa.com/polaris/polaris-go/plugin/statreporter/basereporter
git.code.oa.com/polaris/polaris-go/plugin/statreporter/monitor
git.code.oa.com/polaris/polaris-go/plugin/statreporter/pb/util
git.code.oa.com/polaris/polaris-go/plugin/statreporter/ratelimit
git.code.oa.com/polaris/polaris-go/plugin/statreporter/serviceinfo
git.code.oa.com/polaris/polaris-go/plugin/statreporter/serviceroute
git.code.oa.com/polaris/polaris-go/plugin/subscribe/localchannel
git.code.oa.com/polaris/polaris-go/plugin/subscribe/utils
git.code.oa.com/polaris/polaris-go/plugin/weightadjuster/ratedelay
# git.code.oa.com/trpc-go/trpc-codec/oidb v0.3.3
## explicit; go 1.12
git.code.oa.com/trpc-go/trpc-codec/oidb
git.code.oa.com/trpc-go/trpc-codec/oidb/entity
# git.code.oa.com/trpc-go/trpc-codec/qzh v0.1.6
## explicit; go 1.14
git.code.oa.com/trpc-go/trpc-codec/qzh
git.code.oa.com/trpc-go/trpc-codec/qzh/qza
# git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.13
## explicit; go 1.12
git.code.oa.com/trpc-go/trpc-filter/debuglog
git.code.oa.com/trpc-go/trpc-filter/debuglog/internal/matcher
# git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.5
## explicit; go 1.13
git.code.oa.com/trpc-go/trpc-filter/recovery
# git.code.oa.com/trpc-go/trpc-go v0.20.0
## explicit; go 1.18
git.code.oa.com/trpc-go/trpc-go
git.code.oa.com/trpc-go/trpc-go/admin
git.code.oa.com/trpc-go/trpc-go/client
git.code.oa.com/trpc-go/trpc-go/codec
git.code.oa.com/trpc-go/trpc-go/config
git.code.oa.com/trpc-go/trpc-go/errs
git.code.oa.com/trpc-go/trpc-go/filter
git.code.oa.com/trpc-go/trpc-go/healthcheck
git.code.oa.com/trpc-go/trpc-go/http
git.code.oa.com/trpc-go/trpc-go/internal/addrutil
git.code.oa.com/trpc-go/trpc-go/internal/atomic
git.code.oa.com/trpc-go/trpc-go/internal/attachment
git.code.oa.com/trpc-go/trpc-go/internal/bytes
git.code.oa.com/trpc-go/trpc-go/internal/codec
git.code.oa.com/trpc-go/trpc-go/internal/context
git.code.oa.com/trpc-go/trpc-go/internal/env
git.code.oa.com/trpc-go/trpc-go/internal/error
git.code.oa.com/trpc-go/trpc-go/internal/expandenv
git.code.oa.com/trpc-go/trpc-go/internal/graceful
git.code.oa.com/trpc-go/trpc-go/internal/graceful/internal
git.code.oa.com/trpc-go/trpc-go/internal/http/config
git.code.oa.com/trpc-go/trpc-go/internal/http/fastop
git.code.oa.com/trpc-go/trpc-go/internal/httprule
git.code.oa.com/trpc-go/trpc-go/internal/keeporder
git.code.oa.com/trpc-go/trpc-go/internal/keeporder/actor
git.code.oa.com/trpc-go/trpc-go/internal/local/inprocess
git.code.oa.com/trpc-go/trpc-go/internal/local/server
git.code.oa.com/trpc-go/trpc-go/internal/lru
git.code.oa.com/trpc-go/trpc-go/internal/naming
git.code.oa.com/trpc-go/trpc-go/internal/net
git.code.oa.com/trpc-go/trpc-go/internal/packetbuffer
git.code.oa.com/trpc-go/trpc-go/internal/protocol
git.code.oa.com/trpc-go/trpc-go/internal/queue
git.code.oa.com/trpc-go/trpc-go/internal/random
git.code.oa.com/trpc-go/trpc-go/internal/reflect
git.code.oa.com/trpc-go/trpc-go/internal/reflection
git.code.oa.com/trpc-go/trpc-go/internal/report
git.code.oa.com/trpc-go/trpc-go/internal/ring
git.code.oa.com/trpc-go/trpc-go/internal/rpcz
git.code.oa.com/trpc-go/trpc-go/internal/rpczenable
git.code.oa.com/trpc-go/trpc-go/internal/scope
git.code.oa.com/trpc-go/trpc-go/internal/stack
git.code.oa.com/trpc-go/trpc-go/internal/stream
git.code.oa.com/trpc-go/trpc-go/internal/tls
git.code.oa.com/trpc-go/trpc-go/internal/writev
git.code.oa.com/trpc-go/trpc-go/log
git.code.oa.com/trpc-go/trpc-go/log/internal/timeunit
git.code.oa.com/trpc-go/trpc-go/log/rollwriter
git.code.oa.com/trpc-go/trpc-go/metrics
git.code.oa.com/trpc-go/trpc-go/naming/bannednodes
git.code.oa.com/trpc-go/trpc-go/naming/circuitbreaker
git.code.oa.com/trpc-go/trpc-go/naming/discovery
git.code.oa.com/trpc-go/trpc-go/naming/loadbalance
git.code.oa.com/trpc-go/trpc-go/naming/registry
git.code.oa.com/trpc-go/trpc-go/naming/selector
git.code.oa.com/trpc-go/trpc-go/naming/servicerouter
git.code.oa.com/trpc-go/trpc-go/overloadctrl
git.code.oa.com/trpc-go/trpc-go/plugin
git.code.oa.com/trpc-go/trpc-go/pool/connpool
git.code.oa.com/trpc-go/trpc-go/pool/httppool
git.code.oa.com/trpc-go/trpc-go/pool/multiplexed
git.code.oa.com/trpc-go/trpc-go/pool/objectpool
git.code.oa.com/trpc-go/trpc-go/restful
git.code.oa.com/trpc-go/trpc-go/restful/dat
git.code.oa.com/trpc-go/trpc-go/restful/errors
git.code.oa.com/trpc-go/trpc-go/rpcz
git.code.oa.com/trpc-go/trpc-go/server
git.code.oa.com/trpc-go/trpc-go/transport
git.code.oa.com/trpc-go/trpc-go/transport/internal/bufio
git.code.oa.com/trpc-go/trpc-go/transport/internal/dialer
git.code.oa.com/trpc-go/trpc-go/transport/internal/errs
git.code.oa.com/trpc-go/trpc-go/transport/internal/frame
git.code.oa.com/trpc-go/trpc-go/transport/internal/msg
git.code.oa.com/trpc-go/trpc-go/transport/internal/pool
git.code.oa.com/trpc-go/trpc-go/transport/tnet
git.code.oa.com/trpc-go/trpc-go/transport/tnet/multiplexed
# git.code.oa.com/trpc-go/trpc-metrics-runtime v0.4.0
## explicit; go 1.17
git.code.oa.com/trpc-go/trpc-metrics-runtime
# git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.24
## explicit; go 1.12
git.code.oa.com/trpc-go/trpc-naming-polaris
git.code.oa.com/trpc-go/trpc-naming-polaris/circuitbreaker
git.code.oa.com/trpc-go/trpc-naming-polaris/discovery
git.code.oa.com/trpc-go/trpc-naming-polaris/internal/metrics
git.code.oa.com/trpc-go/trpc-naming-polaris/loadbalance
git.code.oa.com/trpc-go/trpc-naming-polaris/registry
git.code.oa.com/trpc-go/trpc-naming-polaris/selector
git.code.oa.com/trpc-go/trpc-naming-polaris/servicerouter
# git.code.oa.com/trpcprotocol/oicq/oidb_transporter v1.1.3
## explicit; go 1.12
git.code.oa.com/trpcprotocol/oicq/oidb_transporter
# git.woa.com/QzonePlatform/intimate-space/backend v0.0.0-20250507031006-4693db22329e
## explicit; go 1.21.0
git.woa.com/QzonePlatform/intimate-space/backend/common/codec/psa
# git.woa.com/QzonePlatform/qzone-backend v0.0.0-20241017113531-166fdca5b959
## explicit; go 1.21.0
git.woa.com/QzonePlatform/qzone-backend/common/auth
git.woa.com/QzonePlatform/qzone-backend/common/iptool
git.woa.com/QzonePlatform/qzone-backend/common/photo/psa
git.woa.com/QzonePlatform/qzone-backend/common/trpcheadtool
# git.woa.com/jce/jce v1.2.0
## explicit; go 1.17
git.woa.com/jce/jce
# git.woa.com/polaris/polaris-server-api/api/metric v1.0.2
## explicit; go 1.13
git.woa.com/polaris/polaris-server-api/api/metric/v2
# git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7
## explicit; go 1.13
git.woa.com/polaris/polaris-server-api/api/monitor/polaris/v1
# git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2
## explicit; go 1.13
git.woa.com/polaris/polaris-server-api/api/v1/grpc
# git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.4
## explicit; go 1.13
git.woa.com/polaris/polaris-server-api/api/v1/model
# git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0
## explicit; go 1.13
git.woa.com/polaris/polaris-server-api/api/v2/grpc
# git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.4
## explicit; go 1.13
git.woa.com/polaris/polaris-server-api/api/v2/model
# git.woa.com/qqlogin/authentication/auth-sdk-go v1.0.5
## explicit; go 1.20
git.woa.com/qqlogin/authentication/auth-sdk-go
git.woa.com/qqlogin/authentication/auth-sdk-go/authinfo
git.woa.com/qqlogin/authentication/auth-sdk-go/entity/netx
# git.woa.com/social-AIGC/aigc_access v0.0.0-20250509102612-4eb87e17e2c5
## explicit; go 1.22
git.woa.com/social-AIGC/aigc_access/config
git.woa.com/social-AIGC/aigc_access/constant/errcode
git.woa.com/social-AIGC/aigc_access/qzone/face_id_storage
git.woa.com/social-AIGC/aigc_access/stat
# git.woa.com/trpc-go/go_reuseport v1.7.0
## explicit; go 1.17
git.woa.com/trpc-go/go_reuseport
# git.woa.com/trpc-go/tnet v0.1.0
## explicit; go 1.18
git.woa.com/trpc-go/tnet
git.woa.com/trpc-go/tnet/internal/asynctimer
git.woa.com/trpc-go/tnet/internal/autopostpone
git.woa.com/trpc-go/tnet/internal/buffer
git.woa.com/trpc-go/tnet/internal/cache/mcache
git.woa.com/trpc-go/tnet/internal/cache/systype
git.woa.com/trpc-go/tnet/internal/iovec
git.woa.com/trpc-go/tnet/internal/locker
git.woa.com/trpc-go/tnet/internal/netutil
git.woa.com/trpc-go/tnet/internal/poller
git.woa.com/trpc-go/tnet/internal/poller/event
git.woa.com/trpc-go/tnet/internal/safejob
git.woa.com/trpc-go/tnet/internal/stat
git.woa.com/trpc-go/tnet/internal/timer
git.woa.com/trpc-go/tnet/log
git.woa.com/trpc-go/tnet/metrics
git.woa.com/trpc-go/tnet/tls
# git.woa.com/trpcprotocol/qzone/storage_photo v1.1.6
## explicit; go 1.14
git.woa.com/trpcprotocol/qzone/storage_photo
# git.woa.com/trpcprotocol/zy_socail/qq_photos v0.0.0-00010101000000-000000000000 => ./stub/git.woa.com/trpcprotocol/zy_socail/qq_photos
## explicit; go 1.22
git.woa.com/trpcprotocol/zy_socail/qq_photos
# github.com/BurntSushi/toml v1.4.0
## explicit; go 1.18
github.com/BurntSushi/toml
github.com/BurntSushi/toml/internal
# github.com/andybalholm/brotli v1.1.0
## explicit; go 1.13
github.com/andybalholm/brotli
github.com/andybalholm/brotli/matchfinder
# github.com/corona10/goimagehash v1.1.0
## explicit
github.com/corona10/goimagehash
github.com/corona10/goimagehash/etcs
github.com/corona10/goimagehash/transforms
# github.com/creasty/defaults v1.7.0
## explicit; go 1.14
github.com/creasty/defaults
# github.com/forgoer/openssl v0.0.0-20210828150411-6c5378b5b719
## explicit; go 1.12
github.com/forgoer/openssl
# github.com/fsnotify/fsnotify v1.7.0
## explicit; go 1.17
github.com/fsnotify/fsnotify
# github.com/gabriel-vasile/mimetype v1.4.3
## explicit; go 1.20
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/ghodss/yaml v1.0.1-0.20190212211648-25d852aebe32
## explicit
github.com/ghodss/yaml
# github.com/go-playground/form/v4 v4.2.1
## explicit; go 1.13
github.com/go-playground/form/v4
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.16.0
## explicit; go 1.18
github.com/go-playground/validator/v10
# github.com/golang/mock v1.6.0
## explicit; go 1.11
github.com/golang/mock/gomock
# github.com/golang/protobuf v1.5.4
## explicit; go 1.17
github.com/golang/protobuf/jsonpb
github.com/golang/protobuf/proto
github.com/golang/protobuf/ptypes
github.com/golang/protobuf/ptypes/any
github.com/golang/protobuf/ptypes/duration
github.com/golang/protobuf/ptypes/timestamp
github.com/golang/protobuf/ptypes/wrappers
# github.com/golang/snappy v0.0.4
## explicit
github.com/golang/snappy
# github.com/google/flatbuffers v24.3.25+incompatible
## explicit
github.com/google/flatbuffers/go
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/hashicorp/errwrap v1.1.0
## explicit
github.com/hashicorp/errwrap
# github.com/hashicorp/go-multierror v1.1.1
## explicit; go 1.13
github.com/hashicorp/go-multierror
# github.com/jinzhu/copier v0.4.0
## explicit; go 1.13
github.com/jinzhu/copier
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/klauspost/compress v1.17.10
## explicit; go 1.21
github.com/klauspost/compress
github.com/klauspost/compress/flate
github.com/klauspost/compress/fse
github.com/klauspost/compress/gzip
github.com/klauspost/compress/huff0
github.com/klauspost/compress/internal/cpuinfo
github.com/klauspost/compress/internal/snapref
github.com/klauspost/compress/zlib
github.com/klauspost/compress/zstd
github.com/klauspost/compress/zstd/internal/xxhash
# github.com/leodido/go-urn v1.2.4
## explicit; go 1.16
github.com/leodido/go-urn
# github.com/lestrrat-go/strftime v1.1.0
## explicit; go 1.21
github.com/lestrrat-go/strftime
# github.com/mitchellh/go-homedir v1.1.0
## explicit
github.com/mitchellh/go-homedir
# github.com/mitchellh/mapstructure v1.5.0
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/natefinch/lumberjack v2.0.0+incompatible
## explicit
github.com/natefinch/lumberjack
# github.com/nfnt/resize v0.0.0-20180221191011-83c6a9932646
## explicit
github.com/nfnt/resize
# github.com/panjf2000/ants/v2 v2.10.0
## explicit; go 1.13
github.com/panjf2000/ants/v2
github.com/panjf2000/ants/v2/internal/sync
# github.com/pierrec/lz4/v4 v4.1.21
## explicit; go 1.14
github.com/pierrec/lz4/v4
github.com/pierrec/lz4/v4/internal/lz4block
github.com/pierrec/lz4/v4/internal/lz4errors
github.com/pierrec/lz4/v4/internal/lz4stream
github.com/pierrec/lz4/v4/internal/xxh32
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/r3labs/sse/v2 v2.10.0
## explicit; go 1.13
github.com/r3labs/sse/v2
# github.com/spaolacci/murmur3 v1.1.0
## explicit
github.com/spaolacci/murmur3
# github.com/spf13/cast v1.7.0
## explicit; go 1.19
github.com/spf13/cast
# github.com/valyala/bytebufferpool v1.0.0
## explicit
github.com/valyala/bytebufferpool
# github.com/valyala/fasthttp v1.56.0
## explicit; go 1.20
github.com/valyala/fasthttp
github.com/valyala/fasthttp/fasthttputil
github.com/valyala/fasthttp/stackless
# go.uber.org/atomic v1.11.0
## explicit; go 1.18
go.uber.org/atomic
# go.uber.org/automaxprocs v1.6.0
## explicit; go 1.20
go.uber.org/automaxprocs/internal/cgroups
go.uber.org/automaxprocs/internal/runtime
go.uber.org/automaxprocs/maxprocs
# go.uber.org/multierr v1.11.0
## explicit; go 1.19
go.uber.org/multierr
# go.uber.org/zap v1.27.0
## explicit; go 1.19
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/internal/pool
go.uber.org/zap/internal/stacktrace
go.uber.org/zap/zapcore
# gocv.io/x/gocv v0.41.0
## explicit; go 1.21
gocv.io/x/gocv
# golang.org/x/crypto v0.27.0
## explicit; go 1.20
golang.org/x/crypto/sha3
# golang.org/x/image v0.27.0
## explicit; go 1.23.0
golang.org/x/image/bmp
golang.org/x/image/ccitt
golang.org/x/image/draw
golang.org/x/image/math/f64
golang.org/x/image/riff
golang.org/x/image/tiff
golang.org/x/image/tiff/lzw
golang.org/x/image/vp8
golang.org/x/image/vp8l
golang.org/x/image/webp
# golang.org/x/net v0.29.0
## explicit; go 1.18
golang.org/x/net/context
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/timeseries
golang.org/x/net/trace
# golang.org/x/sync v0.14.0
## explicit; go 1.23.0
golang.org/x/sync/errgroup
golang.org/x/sync/singleflight
# golang.org/x/sys v0.25.0
## explicit; go 1.18
golang.org/x/sys/cpu
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/text v0.25.0
## explicit; go 1.23.0
golang.org/x/text/encoding
golang.org/x/text/encoding/internal
golang.org/x/text/encoding/internal/identifier
golang.org/x/text/encoding/simplifiedchinese
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# google.golang.org/genproto/googleapis/rpc v0.0.0-20240415180920-8c6c420018be
## explicit; go 1.19
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.63.2
## explicit; go 1.19
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/grpclog
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcrand
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/idle
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/dns/internal
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/resolver
google.golang.org/grpc/resolver/dns
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.36.6
## explicit; go 1.22
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/protolazy
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/protoadapt
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/emptypb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# gopkg.in/cenkalti/backoff.v1 v1.1.0
## explicit
gopkg.in/cenkalti/backoff.v1
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# git.woa.com/trpcprotocol/zy_socail/qq_photos => ./stub/git.woa.com/trpcprotocol/zy_socail/qq_photos
