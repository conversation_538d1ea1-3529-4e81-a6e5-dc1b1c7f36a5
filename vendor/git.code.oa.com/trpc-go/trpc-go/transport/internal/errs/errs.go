// Package errs provides common function for error handling.
package errs

import (
	"errors"
	"net"

	"git.code.oa.com/trpc-go/trpc-go/errs"
)

// WrapAsClientTimeoutErrOr wraps err as ClientTimeout error or returns a new error with errCode and msg.
// If err is nil, return the original nil err.
func WrapAsClientTimeoutErrOr(err error, errCode int, msg string) error {
	if err == nil {
		return nil
	}
	if e, ok := err.(net.Error); ok && e.Timeout() {
		return errs.WrapFrameError(err, errs.RetClientTimeout, msg)
	}
	return errs.WrapFrameError(err, errCode, msg)
}

// ErrListenerNotFound indicates that the requested listener was not found in the transport layer.
var ErrListenerNotFound = errors.New("listener not found")
