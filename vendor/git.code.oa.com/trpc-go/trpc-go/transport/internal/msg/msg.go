// Package msg provides utility functions for handling messages.
package msg

import (
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/internal/net"
)

// WithLocalAddr is a function that sets the local address of a given message.
// If the provided address is empty, it returns the original message without any modifications.
// Otherwise, it resolves the address using the provided network and sets it on the message.
// It then returns the modified message.
func WithLocalAddr(msg codec.Msg, network, addr string) codec.Msg {
	if addr == "" {
		return msg
	}
	msg.WithLocalAddr(net.ResolveAddress(network, addr))
	return msg
}
