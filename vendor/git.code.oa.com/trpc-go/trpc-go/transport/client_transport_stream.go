package transport

import (
	"context"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/internal/rpczenable"
	"git.code.oa.com/trpc-go/trpc-go/pool/multiplexed"
	"git.code.oa.com/trpc-go/trpc-go/rpcz"
)

// DefaultClientStreamTransport is the default client stream transport.
var DefaultClientStreamTransport = NewClientStreamTransport()

// NewClientStreamTransport creates a new ClientStreamTransport.
func NewClientStreamTransport(opt ...ClientTransportOption) ClientStreamTransport {
	t := &clientStreamTransport{
		clientTransport: newClientTransport(opt...),
		// Map streamID to connection.
		// On the client side, ensure that the streamID is incremented and unique, otherwise the map of
		// addr must be added.
		streamIDToConn: make(map[uint32]multiplexed.VirtualConn),
		m:              &sync.RWMutex{},
	}

	// If a stream multiplexed pool is set, use it directly.
	if t.opts.StreamMultiplexedPool != nil {
		t.multiplexedPool = t.opts.StreamMultiplexedPool
		return t
	}

	t.multiplexedPool = multiplexed.New(
		multiplexed.WithMaxVirConnsPerConn(t.opts.MaxConcurrentStreams),
		multiplexed.WithMaxIdleConnsPerHost(t.opts.MaxIdleConnsPerHost))
	return t
}

// clientStreamTransport keeps compatibility with the original client transport.
type clientStreamTransport struct {
	clientTransport
	multiplexedPool multiplexed.Pool

	m              *sync.RWMutex
	streamIDToConn map[uint32]multiplexed.VirtualConn
}

// RoundTrip keeps compatibility with the original transport RoundTrip.
// Call clientTransport.RoundTrip directly.
func (c *clientStreamTransport) RoundTrip(ctx context.Context, req []byte,
	opts ...RoundTripOption) (rsp []byte, err error) {
	return c.clientTransport.RoundTrip(ctx, req, opts...)
}

// getOptions inits RoundTripOptions and does some basic check.
func (c *clientStreamTransport) getOptions(_ context.Context,
	roundTripOpts ...RoundTripOption) (*RoundTripOptions, error) {
	opts := &RoundTripOptions{
		Multiplexed: c.multiplexedPool,
	}

	// use roundTripOpts to modify opts.
	for _, o := range roundTripOpts {
		o(opts)
	}

	if opts.FramerBuilder == nil {
		return nil, errs.NewFrameError(errs.RetClientConnectFail,
			"tcp client transport: framer builder empty")
	}

	if opts.Msg == nil {
		return nil, errs.NewFrameError(errs.RetClientConnectFail,
			"tcp client transport: message empty")
	}
	return opts, nil
}

// Init inits clientStreamTransport. It gets a connection from the multiplexing pool. A stream is
// corresponding to a virtual connection, which provides the interface for the stream.
func (c *clientStreamTransport) Init(ctx context.Context, roundTripOpts ...RoundTripOption) (err error) {
	var (
		span  rpcz.Span
		ender rpcz.Ender
	)
	if rpczenable.Enabled {
		span, ender, ctx = rpcz.NewSpanContext(ctx, "client stream transport init")
		defer func() {
			span.SetAttribute(rpcz.TRPCAttributeError, err)
			ender.End()
		}()
	}
	opts, err := c.getOptions(ctx, roundTripOpts...)
	if err != nil {
		return err
	}
	// If ctx has been canceled or timeout, just return.
	if ctx.Err() == context.Canceled {
		return errs.NewFrameError(errs.RetClientCanceled,
			"client canceled before tcp dial: "+ctx.Err().Error())
	}
	if ctx.Err() == context.DeadlineExceeded {
		return errs.NewFrameError(errs.RetClientTimeout,
			"client timeout before tcp dial: "+ctx.Err().Error())
	}
	msg := opts.Msg
	streamID := msg.StreamID()
	// Set requestID to streamID which will be used to obtain a connection from multiplexed pool.
	msg.WithRequestID(streamID)

	getOpts := multiplexed.NewGetOptions()
	getOpts.WithMsg(msg)
	getOpts.WithFramerBuilder(opts.FramerBuilder)
	getOpts.WithDialTLS(opts.TLSCertFile, opts.TLSKeyFile, opts.CACertFile, opts.TLSServerName)
	getOpts.WithLocalAddr(opts.LocalAddr)
	conn, err := opts.Multiplexed.GetVirtualConn(ctx, opts.Network, opts.Address, getOpts)
	if err != nil {
		return errs.NewFrameError(errs.RetClientConnectFail,
			"tcp client transport multiplexed pool: "+err.Error())
	}
	msg.WithRemoteAddr(conn.RemoteAddr())
	msg.WithLocalAddr(conn.LocalAddr())
	c.m.Lock()
	c.streamIDToConn[streamID] = conn
	c.m.Unlock()
	return nil
}

// Send sends stream data and provides interface for stream.
func (c *clientStreamTransport) Send(ctx context.Context, req []byte, roundTripOpts ...RoundTripOption) (err error) {
	var (
		span  rpcz.Span
		ender rpcz.Ender
	)
	if rpczenable.Enabled {
		span, ender, ctx = rpcz.NewSpanContext(ctx, "client stream transport send")
		defer func() {
			span.SetAttribute(rpcz.TRPCAttributeError, err)
			ender.End()
		}()
	}

	vc, ok := c.getVirtualConnection(ctx)
	if !ok {
		return errs.NewFrameError(errs.RetServerSystemErr, "Connection is Closed")
	}
	if err := vc.Write(req); err != nil {
		return errs.WrapFrameError(err, errs.RetClientConnectFail, "Connection writes failed")
	}
	return nil
}

func (c *clientStreamTransport) getVirtualConnection(ctx context.Context) (multiplexed.VirtualConn, bool) {
	msg := codec.Message(ctx)
	// StreamID is uniquely generated by stream client.
	streamID := msg.StreamID()
	c.m.RLock()
	vc, ok := c.streamIDToConn[streamID]
	c.m.RUnlock()
	return vc, ok
}

// Recv receives stream data and provides interface for stream.
func (c *clientStreamTransport) Recv(ctx context.Context, _ ...RoundTripOption) (_ []byte, err error) {
	var (
		span  rpcz.Span
		ender rpcz.Ender
	)
	if rpczenable.Enabled {
		span, ender, ctx = rpcz.NewSpanContext(ctx, "client stream transport recv")
		defer func() {
			span.SetAttribute(rpcz.TRPCAttributeError, err)
			ender.End()
		}()
	}

	vc, ok := c.getVirtualConnection(ctx)
	if !ok {
		return nil, errs.NewFrameError(errs.RetServerSystemErr, "Stream is not initialized yet")
	}

	select {
	case <-ctx.Done():
		if ctx.Err() == context.Canceled {
			return nil, errs.NewFrameError(errs.RetClientCanceled,
				"tcp client transport canceled before Write: "+ctx.Err().Error())
		}
		if ctx.Err() == context.DeadlineExceeded {
			return nil, errs.NewFrameError(errs.RetClientTimeout,
				"tcp client transport timeout before Write: "+ctx.Err().Error())
		}
	default:
	}
	return vc.Read()
}

// Close closes connections and cleans up.
func (c *clientStreamTransport) Close(ctx context.Context) {
	msg := codec.Message(ctx)
	streamID := msg.StreamID()
	c.m.Lock()
	defer c.m.Unlock()
	if conn, ok := c.streamIDToConn[streamID]; ok {
		conn.Close()
		delete(c.streamIDToConn, streamID)
	}
}
