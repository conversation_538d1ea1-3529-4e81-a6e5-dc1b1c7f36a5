package transport

import (
	"net/http"
	"runtime"
	"time"
)

const (
	defaultRecvMsgChannelSize      = 100
	defaultSendMsgChannelSize      = 100
	defaultRecvUDPPacketBufferSize = 65536
)

// ServerTransportOptions is options of the server transport.
type ServerTransportOptions struct {
	RecvMsgChannelSize      int
	SendMsgChannelSize      int
	RecvUDPPacketBufferSize int
	RecvUDPRawSocketBufSize int
	IdleTimeout             time.Duration
	KeepAlivePeriod         time.Duration
	ReusePort               bool

	EnableH2C bool
	// DecorateHTTPServer allows customization of the HTTP server before it starts serving requests.
	DecorateHTTPServer func(*http.Server) *http.Server
}

// ServerTransportOption modifies the ServerTransportOptions.
type ServerTransportOption func(*ServerTransportOptions)

// WithRecvMsgChannelSize returns a ServerTransportOption which sets the size of receive buf of
// ServerTransport TCP.
func WithRecvMsgChannelSize(size int) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.RecvMsgChannelSize = size
	}
}

// WithReusePort returns a ServerTransportOption which enable reuse port or not.
func WithReusePort(reuse bool) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.ReusePort = reuse
		if runtime.GOOS == "windows" {
			options.ReusePort = false
		}
	}
}

// WithSendMsgChannelSize returns a ServerTransportOption which sets the size of sendCh of
// ServerTransport TCP.
func WithSendMsgChannelSize(size int) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.SendMsgChannelSize = size
	}
}

// WithRecvUDPPacketBufferSize returns a ServerTransportOption which sets the pre-allocated buffer
// size of ServerTransport UDP.
func WithRecvUDPPacketBufferSize(size int) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.RecvUDPPacketBufferSize = size
	}
}

// WithRecvUDPRawSocketBufSize returns a ServerTransportOption which sets the size of the operating
// system's receive buffer associated with the UDP connection.
func WithRecvUDPRawSocketBufSize(size int) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.RecvUDPRawSocketBufSize = size
	}
}

// WithIdleTimeout returns a ServerTransportOption which sets the server connection idle timeout.
func WithIdleTimeout(timeout time.Duration) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.IdleTimeout = timeout
	}
}

// WithKeepAlivePeriod returns a ServerTransportOption which sets the period to keep TCP connection
// alive.
// It's not available for TLS, since TLS neither use net.TCPConn nor net.Conn.
func WithKeepAlivePeriod(d time.Duration) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.KeepAlivePeriod = d
	}
}

// WithEnableH2C returns a ServerTransportOption which enable h2c.
func WithEnableH2C(enable bool) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.EnableH2C = enable
	}
}

// WithDecorateHTTPServer returns a ServerTransportOption which allows users to customize the HTTP server.
func WithDecorateHTTPServer(f func(*http.Server) *http.Server) ServerTransportOption {
	return func(options *ServerTransportOptions) {
		options.DecorateHTTPServer = f
	}
}

func defaultServerTransportOptions() *ServerTransportOptions {
	return &ServerTransportOptions{
		RecvMsgChannelSize:      defaultRecvMsgChannelSize,
		SendMsgChannelSize:      defaultSendMsgChannelSize,
		RecvUDPPacketBufferSize: defaultRecvUDPPacketBufferSize,
	}
}
