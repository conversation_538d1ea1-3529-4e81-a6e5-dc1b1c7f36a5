// Package fastop provides fast operations for HTTP.
package fastop

import "net/http"

// CanonicalHeaderGet gets the value of a header.
// The key provided must be of canonical form generated by textproto.CanonicalMIMEHeaderKey.
func CanonicalHeaderGet(header http.Header, key string) string {
	h := (map[string][]string)(header)
	v := h[key]
	if len(v) == 0 {
		return ""
	}
	return v[0]
}

// CanonicalHeaderAdd adds the k-v to the header.
// The key provided must be of canonical form generated by textproto.CanonicalMIMEHeaderKey.
func CanonicalHeaderAdd(header http.Header, key, val string) http.Header {
	h := (map[string][]string)(header)
	h[key] = append(h[key], val)
	return h
}

// CanonicalHeaderSet sets the k-v to the header.
// The key provided must be of canonical form generated by textproto.CanonicalMIMEHeaderKey.
func CanonicalHeaderSet(header http.Header, key, val string) http.Header {
	h := (map[string][]string)(header)
	h[key] = []string{val}
	return h
}
