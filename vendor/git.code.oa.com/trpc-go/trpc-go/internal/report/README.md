# tRPC-Go 上报 Metrics 定义

## 前言
tRPC-GO 提供了指标上报的方式，来监控程序运行时的状态。当服务部署后，可以通过伽利略等平台得到指标数据。tRPC-GO 中定义了一些指标，这些指标一般以 `"trpc.xxx"` 的形式出现。

> 注意：v0.3.6 版本起所有 trpc-go 框架中的指标增加了 "trpc." 前缀用以区分用户自定义的业务指标。

> 多数指标（特别是错误类）的上报只能说明大致问题，具体需要结合更多指标、服务端日志、客户端日志等信息一起排查。

> trpc-metrics-runtime 相关的上报指标可以参考：[tRPC-Go runtime 监控](https://git.woa.com/trpc-go/trpc-metrics-runtime)

## Metrics 总览

| 序号 | Metrics 名称 | 概要 |
|-----|---------------|------|
| 1 | [trpc.ServiceStart](#trpcservicestart) | 服务启动成功时上报。 |
| 2 | [trpc.ServerCodecEmpty](#trpcservercodecempty) | 服务未配置编码器时上报，通常意味着框架配置中的协议字段未正确设置。 |
| 3 | [trpc.ServiceHandleFail](#trpcservicehandlefail) | 服务处理函数失败时上报，除非错误是 ErrServerNoResponse。通常需要结合其他指标和日志定位具体原因。 |
| 4 | [trpc.ServiceCodecDecodeFail](#trpcservicecodecdecodefail) | 服务端解码请求失败时上报，通常发生在接收到的数据包不符合预期格式时。错误会被回包给主调方。 |
| 5 | [trpc.ServiceCodecEncodeFail](#trpcservicecodecencodefail) | 服务端编码回复失败时上报，通常由编码插件问题导致。此时客户端不会收到包含错误信息的响应包。 |
| 6 | [trpc.ServiceHandleRpcNameInvalid](#trpcservicehandlerpcnameinvalid) | 处理无效的 RPC 名称时上报，通常由调用方参数错误或 pb 文件版本不一致导致。错误会被打包恢复给客户端。 |
| 7 | [trpc.ServiceCodecUnmarshalFail](#trpcservicecodecunmarshalfail) | 服务端反序列化请求包体失败时上报，通常由调用方与被调方的 pb 文件不一致导致。错误会被打包回复给客户端。 |
| 8 | [trpc.ServiceCodecMarshalFail](#trpcservicecodecmarshalfail) | 服务端序列化响应包体失败时上报。错误会被打包回复给客户端，但不影响当前连接或服务。 |
| 9 | [trpc.ServiceCodecDecompressFail](#trpcservicecodecdecompressfail) | 服务端解压缩请求包体失败时上报，通常由 pb 文件不一致或压缩方法不一致导致。错误会被打包回复给客户端。 |
| 10 | [trpc.ServiceCodecCompressFail](#trpcservicecodeccompressfail) | 服务端压缩响应包体失败时上报。错误会被打包回复给客户端，但不影响当前连接或服务。 |
| 11 | [trpc.TcpServerTransportHandleFail](#trpctcpservertransporthandlefail) | TCP 服务器处理请求失败时上报，通常由编码失败导致。此时不会向客户端发送响应包。 |
| 12 | [trpc.UdpServerTransportHandleFail](#trpcudpservertransporthandlefail) | UDP 服务器处理请求失败时上报，通常由编码失败导致。此时不会向客户端发送响应包。 |
| 13 | [trpc.TcpServerTransportReadEOF](#trpctcpservertransportreadeof) | TCP 服务器接收到 EOF 时上报，通常由客户端主动关闭长连接引起，如空闲超时。 |
| 14 | [trpc.TcpServerTransportWriteFail](#trpctcpservertransportwritefail) | TCP 服务器写入响应失败时上报，通常由客户端已关闭连接导致。此时会关闭当前连接。 |
| 15 | [trpc.TcpServerTransportReceiveSize](#trpctcpservertransportreceivesize) | TCP 服务器接收到的原始请求二进制数据大小（字节）。 |
| 16 | [trpc.TcpServerTransportSendSize](#trpctcpservertransportsendsize) | TCP 服务器发送的二进制响应大小（字节），包括帧头、包头和包体。上报后写入可能失败。 |
| 17 | [trpc.UdpServerTransportWriteFail](#trpcudpservertransportwritefail) | UDP 服务器写入响应失败时上报，通常由调用方超时关闭连接导致。 |
| 18 | [trpc.TcpServerTransportIdleTimeout](#trpctcpservertransportidletimeout) | TCP 长连接达到空闲超时时上报，服务端会主动释放连接。不影响其他连接。 |
| 19 | [trpc.TcpServerTransportReadFail](#trpctcpservertransportreadfail) | TCP 服务器读取帧失败时上报，通常由包不合法导致。会释放对应的连接。 |
| 20 | [trpc.UdpServerTransportReadFail](#trpcudpservertransportreadfail) | UDP 服务器读取帧失败时上报，通常由包不合法导致。会继续处理后续请求。 |
| 21 | [trpc.UdpServerTransportUnRead](#trpcudpservertransportunread) | UDP 服务器读取完整帧后存在未读取的辅助数据时上报。当前帧不会被处理。 |
| 22 | [trpc.UdpClientTransportReadFail](#trpcudpclienttransportreadfail) | UDP 客户端读取帧失败时上报，通常由包不合法导致。 |
| 23 | [trpc.UdpClientTransportUnRead](#trpcudpclienttransportunread) | UDP 客户端读取完整帧后存在未读取的辅助数据时上报。读取将报告失败。 |
| 24 | [trpc.UdpServerTransportReceiveSize](#trpcudpservertransportreceivesize) | UDP 服务器接收到的二进制请求帧大小（字节）。即使上报也可能因剩余数据而读取失败。 |
| 25 | [trpc.UdpServerTransportSendSize](#trpcudpservertransportsendsize) | UDP 服务器发送的二进制响应帧大小（字节）。上报后写入可能失败。 |
| 26 | [trpc.TcpServerTransportJobQueueFullFail](#trpctcpservertransportjobqueuefullfail) | TCP 协程池队列已满时上报。请求会改用同步方式处理，错误码为 RetServerOverload。 |
| 27 | [trpc.UdpServerTransportJobQueueFullFail](#trpcudpservertransportjobqueuefullfail) | UDP 协程池队列已满时上报。请求会改用同步方式处理，错误码为 RetServerOverload。 |
| 28 | [trpc.TcpServerTransportRequestLimitedByOverloadCtrl](#trpctcpservertransportrequestlimitedbyoverloadctrl) | 请求被过载保护限制时上报。请求不会被处理，直接返回 RetServerOverload 错误。 |
| 29 | [trpc.TcpServerAsyncGoroutineScheduleDelay_us](#trpctcpserverasyncgoroutinescheduledelay_us) | 异步模式下协程池的调度延迟（微秒）。仅包括从提交到执行前的时间。 |
| 30 | [trpc.LogQueueDropNum](#trpclogqueuedropnum) | 日志队列已满导致日志被丢弃的数量。 |
| 31 | [trpc.LogWriteSize](#trpclogwritesize) | 写入日志的大小（字节）。 |
| 32 | [trpc.SelectNodeFail](#trpcselectnodefail) | 客户端选择服务节点失败时上报，通常由名称服务配置错误或节点熔断导致。 |
| 33 | [trpc.ClientCodecEmpty](#trpcclientcodecempty) | 客户端未配置协议时上报。 |
| 34 | [trpc.LoadClientConfigFail](#trpcloadclientconfigfail) | 加载客户端配置失败时上报，通常由配置错误导致。 |
| 35 | [trpc.LoadClientFilterConfigFail](#trpcloadclientfilterconfigfail) | 加载客户端过滤器配置失败时上报，通常由配置了不存在的过滤器导致。 |
| 36 | [trpc.TcpClientTransportSendSize](#trpctcpclienttransportsendsize) | TCP 客户端发送的请求包大小（字节）。 |
| 37 | [trpc.TcpClientTransportReceiveSize](#trpctcpclienttransportreceivesize) | TCP 客户端接收到的响应包大小（字节）。 |
| 38 | [trpc.UdpClientTransportSendSize](#trpcudpclienttransportsendsize) | UDP 客户端发送的请求包大小（字节）。 |
| 39 | [trpc.UdpClientTransportReceiveSize](#trpcudpclienttransportreceivesize) | UDP 客户端接收到的响应包大小（字节）。 |
| 40 | [trpc.ConnectionPoolGetNewConnection](#trpcconnectionpoolgetnewconnection) | 连接池创建新连接的数量。 |
| 41 | [trpc.ConnectionPoolGetConnectionErr](#trpcconnectionpoolgetconnectionerr) | 从连接池获取连接失败时上报。 |
| 42 | [trpc.ConnectionPoolRemoteErr](#trpcconnectionpoolremoteerr) | 连接池中远程对等方出现错误时上报。 |
| 43 | [trpc.ConnectionPoolRemoteEOF](#trpcconnectionpoolremoteeof) | 连接池中远程对等方返回 EOF 时上报。 |
| 44 | [trpc.ConnectionPoolIdleTimeout](#trpcconnectionpoolidletimeout) | 连接池中的连接达到空闲超时时上报。 |
| 45 | [trpc.ConnectionPoolLifetimeExceed](#trpcconnectionpoollifetimeexceed) | 连接池中的连接超过生命周期时上报。 |
| 46 | [trpc.ConnectionPoolOverLimit](#trpcconnectionpooloverlimit) | 连接池中的连接数量达到限制时上报。 |
| 47 | [trpc.MultiplexedReconnectErr](#trpcmultiplexedreconnecterr) | 多路复用模式下重连失败时上报。 |
| 48 | [trpc.MultiplexedReconnectOnReadErr](#trpcmultiplexedreconnectonreaderr) | 多路复用模式下读取时重连失败时上报。 |
| 49 | [trpc.MultiplexedReconnectOnWriteErr](#trpcmultiplexedreconnectonwriteerr) | 多路复用模式下写入时重连失败时上报。 |
| 50 | [trpc.PanicNum](#trpcpanicnum) | trpc.GoAndWait 发生 panic 的数量。 |
| 51 | [trpc.AdminPanicNum](#trpcadminpanicnum) | admin 模块发生 panic 的数量。 |

## 详细介绍

### trpc.ServiceStart
`trpc.ServiceStart` 指标在服务（`service`）启动成功时上报。

相关代码片段：

```go
// server.service.Serve()
func (s *service) Serve() error {
    // 调用 Transport 启动监听
    if err := s.opts.Transport.ListenAndServe(s.ctx, s.opts.ServeOptions...); err != nil {
        return err
    }
    // ...注册逻辑
    log.Infof("process: %d, %s service: %s launch success, %s: %s, serving ...",
        pid, s.opts.protocol, s.opts.ServiceName, s.opts.network, desensitize(s.opts.Address))
    report.ServiceStart.Incr() // 上报服务启动
    <-s.ctx.Done()
    return nil
}
```

该指标在服务重启时也会被触发，因此可以利用此指标来监控和判断服务的重启情况。

#### 版本差异
- 该指标最早出现于 v0.2.2。

### trpc.ServerCodecEmpty

在执行 `service` 的 `Handle` 方法时，若检测到服务端的 `codec` 为空，通常意味着框架配置的 `codec` 未设置，此时会触发错误信息的上报。同时会伴随着服务端 `Error` 日志 "server codec empty"。

相关代码片段：

```go
// server.service.Handle()
func (s *service) Handle(ctx context.Context, reqBuf []byte) (rspBuf []byte, err error) {
    // ...
    // if server codec is empty, simply returns error.
    if s.opts.Codec == nil { // 服务端未配置编码器  
        log.ErrorContextf(ctx, "server codec empty") // 记录错误日志
        report.ServerCodecEmpty.Incr() // 上报  
        return nil, errors.New("server codec empty") // 返回错误    
    }
    // ...
}
```

`service` 的 `Handle` 方法负责处理服务启动监听后接收到的每个请求。它会将请求的二进制包通过 `service` 中配置的 `codec` 进行解包、解压缩、反序列化等处理，执行相应的业务逻辑，然后将结果序列化、压缩、打包，最终返回响应的二进制包。

#### 版本差异
- 该指标最早出现于 v0.2.2。

### trpc.ServiceHandleFail

在 `service` 的 `Handle` 方法中，负责将请求的二进制包体 `reqBodyBuf` 处理成响应结构体 `rspBody`。若在处理过程中遇到错误，则应上报 `trpc.ServiceHandleFail`。除非错误是 `ErrServerNoResponse` 则不上报，因为这可能是由于框架类型错误、服务端全链路超时，或 `CallType` 为 `SendOnly` 导致，这并不一定意味着当前服务存在问题，因此无需上报。
相关代码片段：

```go
// server.service.Handle()
// ...
rspBody, err := s.handle(ctx, msg, reqBodyBuf) // 处理请求
if err != nil { 
    // 无响应，不向客户端回包
    if err == errs.ErrServerNoResponse {
        token.OnResponse(ctx, nil)
        return nil, err
    }
    defer token.OnResponse(ctx, err)
    // failed to handle, should respond to client with error code, ignore rspBody.
    report.ServiceHandleFail.Incr() //上报
    return s.encode(ctx, msg, nil, err) // 向客户端回包
}
```

在此过程中，会打包一个空的响应二进制包体 `rspBodyBuf` 作为响应二进制包 `rspBuf`，并将错误信息写入 `msg`（响应包头）中。这一过程发生在解析出 `reqBodyBuf` 之后，处理业务请求时出现错误的情况下。最后，会向客户端发送响应。


这里 `ErrServerNoResponse`，`RetServerFullLinkTimeout` 全链路超时和 `SendOnly` 模式，都会转化为 `ErrServerNoResponse`，希望跳过 `encode` 阶段，向传输层报告不需要回包。
相关代码片段：

```go
// server.service.handle()
// ...
rspBody, err := handler(ctx, newFilterFunc) // 处理请求
if err != nil {
    if e, ok := err.(*errs.Error); ok &&
        e.Type == errs.ErrorTypeFramework &&
        e.Code == errs.RetServerFullLinkTimeout {
        err = errs.ErrServerNoResponse //转化 1
    }
    return nil, err
}
if msg.CallType() == codec.SendOnly {
    return nil, errs.ErrServerNoResponse // 转化 2
}
return rspBody, nil
```

ServiceHandleFail 可能的错误原因：

- `handleStream` 流式 RPC 相关的错误
    - `RetServerSystemErr`, `frameHeadNotInMsg`：帧头缺失。
    - `serverRspErr`：服务端响应错误等。
    - `RetServerNoService (11)`, "Stream method no Handle"：没有找到注册的 `stream Handle`。
    - 处理流式请求相关的错误。

- `RetServerNoFunc(12)` rpc name %s invalid, current service：一般是 pb 文件过时了。

- `handler`（用户注册的 `handler`）出错。

这里的有些错误还可以细分更小的错误，也可以看出 `trpc.ServiceHandleFail` 是一个比较宽泛的错误，单独依赖一个指标很难定位具体的问题。可以考虑：

- 结合其他指标，比如 `trpc.ServiceHandleRpcNameInvalid`，可以定为出是 RPC 名字无效，大多是因为 pb 文件版本过时了。
- 结合服务端日志，有一些错误会以日志的方式记录下来。
- 结合被调方收到的回包，有些错误信息会放在给被调方的回包中，此时 `body` 为空。

#### 版本差异

- 该指标最早出现于 v0.2.2。


### trpc.ServiceCodecDecodeFail

这个指标上报发生在服务端解码请求失败时，这通常是由于接收到的数据包不符合预期格式所致。这种情况多发生在服务端尝试对请求的二进制网络数据包进行解包的过程中。解包过程涉及提取帧头、请求包头以及请求体 `reqBodyBuf`（二进制格式）。值得注意的是，在此阶段，得到的请求体仍处于二进制状态，并未被反序列化。

```go
// server.service.decode
func (s *service) decode(_ context.Context, msg codec.Msg, reqBuf []byte) ([]byte, error) {
    s.setOpt(msg)
    reqBodyBuf, err := s.opts.Codec.Decode(msg, reqBuf) // 解码
    if err != nil { // 解码失败
        report.ServiceCodecDecodeFail.Incr() //上报
        return nil, errs.NewFrameError(errs.RetServerDecodeFail, "service codec Decode: "+err.Error()) // 返回错误信息
    }

    // call setOpt again to avoid some msg infos (namespace, env name, etc.)
    // being modified by request decoding.
    s.setOpt(msg)
    return reqBodyBuf, nil
}
```

此错误是可以被回包给主调的，被调端在 `encode` 的时候会记录 `trace` 级别的日志。

常见的错误包括：
- `server decode req buf len invalid`
- `total len %d is not actual buf len %d`
- `server decode pb head len empty`
- `server decode pb head len invalid`

#### 版本差异

- 该指标最早出现于 v0.2.2。

#### 相关案例

- `server` 接受到非法包，上报 `trpc.ServiceCodecDecodeFail` 并提前返回。提前返回导致不会有后续的错误上报了（`decode` 成功后才会走后续的反序列化、处理等逻辑）。
[trpc-go 服务 007 上报了很多这个 metric: trpc.ServiceCodecDecodeFail，但接口没有请求过来 && 接口被失败率是 0?](https://mk.woa.com/q/282485?ADTAG=search)

### trpc.ServiceCodecEncodeFail

服务端编码回复失败，通常是由于编码插件出现问题所致。服务端的 `encode()` 方法主要在以下两种情况下被调用：

1. 在成功获取响应二进制包体且前一步骤无错误时：此时会调用 `encode` 方法将响应打包成二进制响应帧，准备发送回客户端，这是一个预期的正常流程。
2. 在步骤 1 之前出现某些错误，但 `codec` 不为空时：此时会通过 `encode` 方法将错误信息打包并发送回客户端。通常，此时的包体为空或无效。

在 `encode()` 方法内，会调用 `Codec.Encode` 方法，若失败，则会触发 `trpc.ServiceCodecEncodeFail` 的错误上报。
相关代码片段：

```go
// server.service.encode()
func (s *service) encode(ctx context.Context, msg codec.Msg, rspBodyBuf []byte, e error) (rspBuf []byte, err error) {
    if e != nil { // 有错误
        log.TraceContextf(ctx, "service: %s handle err: %+v", s.opts.ServiceName, e) // 记录错误日志
        msg.WithServerRspErr(e) // 打包错误
    }

    rspBuf, err = s.opts.Codec.Encode(msg, rspBodyBuf) // 编码
    if err != nil { // 编码失败
        report.ServiceCodecEncodeFail.Incr() // 上报
        log.ErrorContextf(ctx, "service: %s encode fail: %v", s.opts.ServiceName, err) // 服务端 trace 级别的日志
        return nil, err
    }
    return rspBuf, nil
}
```

当此类错误发生时，它会被传递到 `transport` 层并提前返回，从而避免向网络层写入数据包。这意味着客户端将不会收到包含错误信息的响应包，但可以通过查看服务端的 `error` 级别日志来发现此错误以及具体信息。
相关代码片段：

```go
// transport.tcpconn.handleSyncWithErr()
// ...

rsp, err := c.conn.handle(ctx, req) // 处理请求 

if err != nil { // 处理请求失败
    if err != errs.ErrServerNoResponse {
        report.TCPServerTransportHandleFail.Incr() // 上报
        log.Trace("transport: tcpconn serve handle fail ", err) // 记录错误日志
        c.close() // 关闭 socket，清除数据。
        return
    }
    // On stream RPC, server does not need to write rsp, just returns.
    return
}
```

#### 排查思路

当观察到 `trpc.ServiceCodecEncodeFail` 指标上报时，建议结合服务端的 `error` 级别日志进行问题定位，通常这类问题与 `Codec` 插件有关。

#### 版本差异

- 该指标最早出现于 v0.2.2。

###  trpc.ServiceHandleRpcNameInvalid

该指标上报发生在处理无效的 RPC 名称时，通常是由于调用者填写了不正确的参数，或者是 pb 文件版本不一致或过时的情况下。
相关代码片段：

```go
// server.service.handle()
// ...
handler, ok := s.handlers[msg.ServerRPCName()] // 索引 handler
if !ok {
    handler, ok = s.handlers["*"] // wildcard
    if !ok { // 没有找到注册的 handler
        report.ServiceHandleRPCNameInvalid.Incr() // 上报
        return nil, errs.NewFrameError(errs.RetServerNoFunc,
            fmt.Sprintf("service handle: rpc name %s invalid, current service: %s. "+
                "This error occurs if the current service (which the client wants to access) isn't registered "+
                "on the server or the RPC name isn't registered with the current service, possibly due to an outdated pb file.",
                msg.ServerRPCName(), msg.CalleeServiceName()))
    }
}
```

此错误会被编码回包给客户端，并在服务端编码时记录 `trace` 级别的错误信息。

在流式 RPC 中也存在类似的情形：

```go
// stream.streamDispatcher.handleByStreamFrameType()
//...
switch streamFrameType {
case trpc.TrpcStreamFrameType_TRPC_STREAM_FRAME_INIT: // INIT 帧
    if sh == nil {
        // 只有当服务器 RPC 名称不匹配时，streamHandler 才会为空。
        report.ServiceHandleRPCNameInvalid.Incr() // 上报
        return nil, errs.NewFrameError(errs.RetServerNoFunc,
            fmt.Sprintf("stream service handle: rpc name %s invalid, current service: %s. "+
                "this error occurs if the current service (which the client wants to access) isn't registered on "+
                "the server or the RPC name isn't registered with the current service, possibly due to an outdated pb file.",
                msg.ServerRPCName(), msg.CalleeServiceName()))
    }
    return sd.handleInit(ctx, sh, si)
}
```

流式 RPC 中，也是使用类似的 serverRPCName 的方式索引用户注册的 streamHandler。相关代码片段：

```go
// server.service.handle(）
// 否是流式 RPC。
if fh, ok := msg.FrameHead().(icodec.FrameHead); ok && fh.IsStream() {
    // 只有 init 框架需要 stream handler，并且只有 init 框架才能找到 streamHandler。
    // 对于其他帧类型，msg.ServerRPCName() 为空。
    streamHandler := s.streamHandlers[msg.ServerRPCName()]
    return s.handleStream(ctx, msg, reqBodyBuf, streamHandler, s.opts)
}
```
也就是说，在流式客户端创建时，就可能出现这个错误。因此使用流式时如果观测到此指标，也可以排查是不是 pb 文件版本不一致或者过时了。


#### 排查思路

当观察到 `trpc.ServiceHandleRPCNameInvalid` 指标上报时，建议结合主调方的 `trace` 级别日志进行问题定位，通常这类问题与 `Codec` 插件有关。

#### 版本差异

- 该指标最早出现于 v0.2.2。

#### 相关案例
- `IAS` 健康检查定期发送 `SYN` 请求，但是 `IAS` 健康检查没有使用合法的 pb 协议，当成直接请求 `/` ，导致找不到方法的错误。由于存在多个调用方，因此会出现部分请求成功，部分请求失败的情况。
[trpc 很多 ServiceHandleRpcNameInvalid 错误上报是什么原因呢？](https://mk.woa.com/q/265002?ADTAG=search) 



### trpc.ServiceCodecUnmarshalFail

服务端在反序列化 `reqBodyBuf` 时遇到失败，这种情况通常发生在调用者与被调用者之间的 pb 文件不一致时。服务端处理请求二进制包体 `reqBodyBuf` 并生成响应包体 `rspBody` 的过程较为复杂，但关键在于理解 `Unmarshal` 步骤，即将二进制请求包体反序列化为请求对象。


相关代码片段：

```go
// server.service.filterFunc 中的 FilterFunc 闭包
// ...
err = codec.Unmarshal(serializationType, reqBodyBuf, reqBody) // 反序列化
if err != nil { // 反序列化失败
    report.ServiceCodecUnmarshalFail.Incr() // 上报
    return nil, errs.NewFrameError(errs.RetServerDecodeFail, "service codec Unmarshal: "+err.Error())
}
```

当 `Unmarshal` 失败时，会触发 `ServiceCodecUnmarshalFail` 的上报，并返回伴随错误码 `RetServerDecodeFail = 1` 的错误。错误会向上传递至服务的 `Handle` 方法，并将错误信息打包到消息中回复给客户端（此时响应包体为 `nil`）。在编码过程中，服务端会记录 `trace` 级别的日志。

```go
// server.service.Handle()
// 这个 handle 包含了注册桩代码的 Handler，其又包含了服务端拦截器的的执行和用户接口的执行。
rspBody, err := s.handle(ctx, msg, reqBodyBuf)
if err != nil { // 处理请求失败
    // ...
    // 伴随 trpc.ServiceHandleFail，并打包准备回给客户端
    report.ServiceHandleFail.Incr() // 上报
    return s.encode(ctx, msg, nil, err) // 打包错误信息
}
```

#### 排查思路
`ServiceCodecUnmarshalFail` 的上报时，可以结合服务端的 `trace` 级别日志和客户端的回包信息进行排查。常见的 `Unmarshal` 失败原因包括无法获取 `codec.Serializer`（例如序列化器未成功注册）、桩代码不匹配等。因此，此指标表明服务端在反序列化请求包体时发生错误时，通常可以通过检查 `Serializer` 和桩代码来排查问题。反序列化失败的原因很多，也可以参考码客现有相关问题的解决方法。

#### 版本差异

- 该指标最早出现于 v0.2.2。

#### 相关案例
- 序列化类型错误导致，但是服务端找不到日志。原因是这是一个 `trace` 级别的日志，还会往上返回到 `Handle` 函数中将错误打包传回给客户端，因此可以结合主调方的日志排查具体的原因。
[偶现 ServiceCodecUnmarshalFail，没有找到对应的 error 日志？](https://mk.woa.com/q/287811?ADTAG=search) 

### trpc.ServiceCodecMarshalFail

服务端序列化响应包体 `rspBody` 失败。当调用 `codec.Marshal` 进行序列化操作失败时，将触发 `ServiceCodecMarshalFail` 上报。在这种情况下，目标二进制包体 `rspBodyBuf` 将变为 `nil`，并向客户端返回错误码和错误信息。同时，`encode` 函数会在服务端记录 `trace` 级别的错误日志。

相关代码片段：

```go
// server.service.handleResponse()
// 该方法中会进行 序列化 + 压缩 + 打包 的过程。
// ...
rspBodyBuf, err := codec.Marshal(serializationType, rspBody) // 序列化
if err != nil { // 序列化失败
    report.ServiceCodecMarshalFail.Incr() // 上报
    // 如果压缩失败，rspBodyBuf 将为 nil，仅向客户端响应错误代码。
    return s.encode(ctx, msg, rspBodyBuf, errs.NewFrameError(
            errs.RetServerEncodeFail, "service codec Marshal: "+err.Error())) // 打包错误信息
}
```

其中错误码为 `RetServerEncodeFail = 2`。注意这个指标上报只限于说明该请求失败，并且是客户端还能收到带有错误信息的回包，并且不代表当前连接（非短连接）或者 `service` 异常。

可能导致此错误的原因包括序列化协议类型不支持、获取 `Serializer` 失败、`Serializer` 未注册（`serializer not registered`）以及桩代码不匹配等问题。

通常可以结合服务端的 `trace` 级别日志和客户端的回包信息，或者排查 `Serializer` 和桩代码来解决问题。


#### 版本差异

- 该指标最早出现于 v0.2.2。

### trpc.ServiceCodecDecompressFail

服务端在解压缩请求二进制包体 `reqBody` 时失败。这与 `ServiceCodecUnmarshalFail` 类似，同样是在 `filterFunc` 中返回的闭包中处理。

相关代码片段：

```go
// server.service.FilterFunc() 中返回的闭包
reqBodyBuf, err := codec.Decompress(compressType, reqBodyBuf) // 解压缩
if err != nil { // 解压缩失败
    report.ServiceCodecDecompressFail.Incr() // 上报
    return nil, errs.NewFrameError(errs.RetServerDecodeFail, "service codec Decompress: "+err.Error()) // 打包错误信息
}
```

当解压缩失败时，将触发 `ServiceCodecDecompressFail` 的上报。服务端会向上返回错误码 `RetServerDecodeFail`（值为 1）以及错误信息。该错误信息会传递至服务的 Handle 方法，并被打包到响应消息中回复给客户端（此时响应包体为 `nil`）。在 `encode` 过程中，服务端还会记录具体错误的 `trace` 级别日志。

注意这个指标上报只限于说明该请求失败，并且是客户端还能收到带有错误信息的回包，而不代表当前连接（非短连接）或者 `service` 异常。

此错误通常与获取 `compressor` 失败（如 "compressor not registered"）或解压缩失败等相关，往往发生在调用者和被调用者之间的 pb 文件不一致，或压缩方法不一致时。解决此类问题时，可以结合服务端的 `trace` 级别日志和客户端的回包信息，或者排查 `compressor` 和桩代码。


#### 版本差异

- 该指标最早出现于 v0.2.2。

### trpc.ServiceCodecCompressFail

服务端在执行压缩二进制响应包体 `rspBodyBuf` 时遇到失败。当调用 `codec.Compress` 方法进行压缩操作失败时，将触发 `ServiceCodecCompressFail` 事件。在这种情况下，目标二进制包体 `rspBodyBuf` 会被设置为 `nil`，同时服务端会向客户端返回错误码和错误信息。此外，`encode` 函数会在服务端记录 `trace` 级别的错误日志。


相关代码片段：

```go
// server.service.handleResponse()
// ...
rspBodyBuf, err = codec.Compress(compressType, rspBodyBuf)
if err != nil {
    report.ServiceCodecCompressFail.Incr() // 上报
    return s.encode(ctx, msg, rspBodyBuf, errs.NewFrameError(
        errs.RetServerEncodeFail, "service codec Compress: "+err.Error()))
}
```

其中错误相关的错误码为 `RetServerEncodeFail`（值为 2）。

注意这个指标上报只限于说明该请求失败，并且是客户端还能收到带有错误信息的回包，而不代表当前连接（非短连接）或者 `service` 异常。

此错误可能由获取 `compressor` 失败（例如："compressor not registered"）或压缩过程中的问题导致。解决此类问题时，可以结合服务端的 `trace` 级别日志和客户端的回包信息进行分析，或者对 `compressor` 和桩代码进行排查。

#### 版本差异

- 该指标最早出现于 v0.2.2。


### trpc.TcpServerTransportHandleFail

TCP 服务器传输处理失败通常发生在编码失败时。当服务端接受到请求时，会在 `tcpconn` 的 `handleSyncWithErr()` 中调用 `c.conn.handle()`，这通常是调用 `service` 的 `Handle`。如果发生错误且不是 `ErrServerNoResponse`，此时会上报 `TCPServerTransportHandleFail`。如果是 `ErrServerNoResponse`，则可能是流式 `RPC`，不需要返回响应包，直接返回即可。



相关代码片段：

```go
// transport.tcpconn.handleSyncWithErr()
// handle 会调用 service.Handle(),接受请求二进制帧，返回响应二进制帧。
rsp, err := c.conn.handle(ctx, req) // 处理请求
// ...
if err != nil { // 处理请求失败
    if err != errs.ErrServerNoResponse {
        report.TCPServerTransportHandleFail.Incr() // 上报
        log.Trace("transport: tcpconn serve handle fail ", err)
        c.close()
        return
    }
    // On stream RPC, server does not need to write rsp, just returns.
    return
}
// ...
_, err = c.write(rsp) // 没有 error 的情况下才会写回响应
```

只要 `handle` 返回错误，就不会发送响应包，因为这些错误大多导致无法向可客户端写回数据。查看 `service.Handle` 的代码可以发现，这些错误包括：
- `server codec empty`：可以结合 `trpc.ServerCodecEmpty` 指标和服务端 `trace` 级别日志进行确认。
- `service: %s encode fail`：可以结合 `trpc.ServiceCodecEncodeFail` 指标和服务端 `trace` 级别日志进行确认。

在发生该错误后，连接会被关闭，并退出 `handleSyncWithErr`。如果使用了协程池，相关的协程会被回收。这意味着该错误影响的范围仅限于对应的 `TCP` 连接（`net.Conn`）。

在 `tnet` 中也有类似的情况会上报 `trpc.TcpServerTransportHandleFail`，排查思路也类似。


#### 版本差异

- 该指标最早出现于 v0.2.2。
- trpc-go >= v0.3.6 后，上报逻辑基本跟当前逻辑一致。
- v0.2.2 <= trpc-go < v0.3.6 时，`handle` 返回错误直接上报 `TcpServerTransportHandleFail`，没有判断是不是 `ErrServerNoResponse`。

### trpc.UdpServerTransportHandleFail

UDP 服务器传输处理失败，类似于 `TCPServerTransportHandleFail`。
在 `udpconn` 的 `handleSyncWithErr` 调用 `c.conn.handle(ctx, req)`，一般是 `service` 的 `Handler` 方法。如果发生错误，并且该错误不是 `ErrServerNoResponse`，上报 `UdpServerTransportHandleFail`。

相关代码片段：

```go
// transport.udpconn.handleSyncWithErr()
// handle 跟 TCP 一样调用会调用到 service 的 Handle()
rsp, err := c.conn.handle(ctx, req) // 处理请求
if err != nil { // 处理请求失败
    if err != errs.ErrServerNoResponse {
        report.UDPServerTransportHandleFail.Incr() // 上报
        log.Tracef("udp handle fail: %v", err)
    }
    return
}
```

可以参考 `trpc.TcpServerTransportHandleFail` 的排查服务端 `encode` 失败的原因：
- `server codec empty`：可以结合 `trpc.ServerCodecEmpty` 指标和服务端 `trace` 级别日志进行确认。
- `service: %s encode fail`：可以结合 `trpc.ServiceCodecEncodeFail` 指标和服务端 `trace` 级别日志进行确认。

在 tnet 中也有类似的情况会上报 `trpc.UdpServerTransportHandleFail`，排查思路也类似。

#### 版本差异

- 该指标最早出现于 v0.2.2。
- trpc-go >= v0.3.6 后，上报逻辑基本一致。
- v0.2.2 <= trpc-go < v0.3.6 时，handle 返回错误直接上报 `UdpServerTransportHandleFail`，没有判断是不是 `ErrServerNoResponse`。

### trpc.TcpServerTransportReadEOF

`TcpServerTransportReadEOF` 指标表示 TCP 服务器接收到 `EOF`，通常是由于客户端主动关闭连接引起的，发生在长连接的场景中。
服务端会在每个连接对应的协程中使用 `ReadFrame()` 读取请求帧。
相关代码片段：

```go
// transport.tcpconn.serve()
// 读取请求帧并交给处理函数。
for {
    // ... 
    req, err := c.fr.ReadFrame() // 读帧
    if err != nil { 
        if err == io.EOF { // 客户端主动关闭连接
            report.TCPServerTransportReadEOF.Incr() // 上报
            return
        }
    }
    // ... 
    c.handle(req)
}
```

当 `ReadFrame()` 返回 `io.EOF` 错误时，服务器会上报 `TcpServerTransportReadEOF` 指标，并结束当前协程，释放 TCP 连接。需要注意的是，这个指标仅表示客户端主动关闭了连接（可以是正常结束），并不意味着服务本身或其他连接存在问题。

这里也可以参考 [tRPC-Go 指标监控](https://iwiki.woa.com/p/870029531#4-faq) 中的解释：

> TcpServerTransportReadEOF 这个代表当前 server 接收到上游 client 的 close connection 信号，当前服务是正常的，是上游调用方的问题。
> 对于 trpc 来说，client->server 之间的连接都是长连接的，正常情况下连接会一直保持。不过 client 端默认有 50s 的链接空闲时间，如果请求量较小，一个连接超过 50s 都没有数据，client 端就会自动关闭连接，这种情况也是正常的。
> 其他情况，需要详细定位一下 client 为什么会频繁的主动关闭连接了，大概率是 client 有 bug，比如使用了短连接方式，或者 client 端大量超时马上关闭连接。

在 tnet 出现 `TcpServerTransportReadEOF` 也是类似的原因。相关代码片段：

```go
// transport.tnet.tcpConn.onRequest
// onRequest 当与客户端的连接上有传入数据时触发。
req, err := tc.framer.ReadFrame()
if err != nil {
    // 连接已被关闭
    if err == tnet.ErrConnClosed {
        report.TCPServerTransportReadEOF.Incr() // 上报
        return err
    }
}
```

#### 版本差异

- 该指标最早出现于 trpc-go v0.2.8。

#### 相关案例

- 这个指标多是上游的问题，由上游来排查
[123 服务 007 属性监控中的 TcpServerTransportReadEOF 量比较大，一般是什么原因？](https://mk.woa.com/q/278386?ADTAG=search )
- 服务已经假死导致主调用方检测到后纷纷断开连接导致的被调读取到 EOF。
[trpc-go 使用的协程池看上去在某些容器上会死锁，该怎么排查具体的原因呢？](https://mk.woa.com/q/279165?ADTAG=search )
- 推测原因是服务过载导致主调主动断开连接。
[为什么 trpcgo 服务失败呢？](https://mk.woa.com/q/292647?ADTAG=search )

### trpc.TcpServerTransportWriteFail

`TcpServerTransportWriteFail` 指标表示 TCP 服务器在传输过程中写入回包失败，通常是由于客户端已关闭连接引起的。在 `handleSyncWithErr` 函数中，服务器会尝试写入响应。

相关代码片段：

```go
// transport.tcpconn.handleSyncWithErr
// ...
_, err = c.write(rsp) // 写回响应
if err != nil { // 写回响应失败
    report.TCPServerTransportWriteFail.Incr() // 上报
    log.Trace("transport: tcpconn write fail ", err) // 记录错误日志
    c.close() // 关闭连接
}
```

当写入操作失败时，服务器会上报 `TcpServerTransportWriteFail` 指标，并记录 `Trace` 级别的错误日志，随后关闭连接。需要注意的是，因为 `TcpServerTransportSendSize` 的上报发生在实际写操作之前，所以会出现能监控到发送数据大小但发送失败的情况。
可以结合服务端的 `trace` 级别日志具体定位问题。

tnet 的实现中 `TcpServerTransportWriteFail` 上报也是类似的，可以参考定位问题。

#### 版本差异

- 该指标最早出现于 v0.2.2。

#### 相关案例
- 推测是由于超时导致客户端已经关闭。
[服务出现大量 TCPServerTransportWriteFail 错误？](https://mk.woa.com/q/295157?ADTAG=search )

### trpc.TcpServerTransportReceiveSize

`TcpServerTransportReceiveSize` 指标表示 TCP 服务接收到的请求大小，单位为字节（byte）。该指标指的是通过 `req, err := c.fr.ReadFrame()` 读取到的原始请求二进制数据的大小。

相关代码片段：

```go
// transport.tcpconn.serve()
req, err := c.fr.ReadFrame() // 读取请求帧
// ...
report.TCPServerTransportReceiveSize.Set(float64(len(req))) // 上报
// ...
c.handle(req) // 处理请求
```

在读取请求帧后，服务器会将请求的大小上报为 `TcpServerTransportReceiveSize` 指标。这一逻辑在 tnet 中也有类似的实现。

#### 版本差异

- 该指标最早出现于 v0.7.0。

### trpc.TcpServerTransportSendSize

`TcpServerTransportSendSize` 指标表示 TCP 服务器发送的回复大小，单位为字节（byte）。该指标指的是二进制回包的整体大小，包括帧头、包头和包体。
上报动作发生在实际写操作之前，因此在上报了回复大小后，可能会发生写数据错误。
相关代码片段：

```go
// transport.tcpconn.handleSyncWithErr()
rsp, err := c.conn.handle(ctx, req) // 处理请求
// ...
report.TCPServerTransportSendSize.Set(float64(len(rsp))) // 大小 
_, err = c.write(rsp) // 向连接中写数据
if err != nil {
    report.TCPServerTransportWriteFail.Incr() // 可能还会上报失败
    // ... 
}
```

在处理请求后，服务器会将生成的回复大小上报为 `TcpServerTransportSendSize` 指标。需要注意的是，写入操作可能会失败，因此在上报回复大小后，仍需关注写入过程中的错误。

tnet 中的 `TcpServerTransportSendSize` 也一样的含义。


#### 版本差异

- 该指标最早出现于 v0.7.0。

### trpc.UdpServerTransportWriteFail

`UdpServerTransportWriteFail` 指标表示 UDP 服务器在写入回复时失败，通常发生在调用者因超时而关闭连接，导致服务器无法再向该端口发送数据。
相关代码片段：

```go
// transport.udpconn.handleSyncWithErr()
rsp, err := c.conn.handle(ctx, req) // 处理请求
// ...
if _, err := c.writeTo(rsp, remoteAddr); err != nil { // 写回响应
    report.UDPServerTransportWriteFail.Incr() // 上报
    log.Tracef("udp write to fail:%v", err) // 记录错误日志
    return
}
```

在写入响应时，如果发生错误，服务器会上报 `UdpServerTransportWriteFail` 指标，并记录 `trace` 级别的日志，以便于后续问题的定位和排查。

tnet 中上报 `trpc.UdpServerTransportWriteFail` 也有类似逻辑。

#### 版本差异

- 该指标最早出现于 v0.2.2。



### trpc.TcpServerTransportIdleTimeout

`TcpServerTransportIdleTimeout` 指标表示服务端 TCP 长连接因达到空闲超时而主动释放连接。这里的连接指的是在服务器接受到新的 TCP 连接时，封装成 `tcpconn` 进行处理（包括读数据、处理数据和写数据）。这一处理过程是通过一个 goroutine 执行的，并且在 `serve` 函数开始时，会启动一个协程来监听服务器调用 `close` 以关闭连接。
相关代码片段：

```go
// transport.tcpconn.serve()
// ...
for {
    now := time.Now()
    if c.idleTimeout > 0 && now.Sub(lastVisited) > c.idleTimeout { // 空闲时间超过配置
        report.TCPServerTransportIdleTimeout.Incr() // 上报
        return
    }
    // ...
    req, err := c.fr.ReadFrame() // 读取请求帧
    // ...
}
```

循环会定期检查空闲时间是否超过设定的 `idleTimeout`。如果超过了这个时间，服务器会上报 `TcpServerTransportIdleTimeout` 指标，并结束当前的 `serve` 过程。随后，监听结束的线程会关闭该 TCP 连接。

需要注意的是，这个指标仅代表服务中的某个连接因超时而关闭，并不意味着服务的其他连接或服务本身出现了异常。

#### 版本差异

- 以上逻辑从 v0.17.0 开始适用。
- 版本 >= v0.2.8 (最早出现版本) 且 < v0.17.0 时，会在每次读帧时，根据返回的错误是否 `net.Error` 并且 `e.Timeout()` 来判断客户端超过空闲时间没有发包，上报 `TcpServerTransportIdleTimeout`，服务端主动超时关闭。

### trpc.TcpServerTransportReadFail

`TcpServerTransportReadFail` 指标表示 TCP 服务器在读取帧时失败，通常发生在接收到的包不合法的情况下。在排除了 `ReadFrame()` 方法返回的错误为 `io.EOF`（表示客户端已关闭连接）和 TCP 连接空闲时间超时错误之后，其他错误都会被上报为 `TCPServerTransportReadFail`，并伴随服务端的 `trace` 级别日志记录具体原。
相关代码片段：

```go
// transport.tcpconn.serve()
req, err := c.fr.ReadFrame() // 读取请求帧
if err != nil {
    if err == io.EOF { // 检测客户端关闭
        report.TCPServerTransportReadEOF.Incr()
        return
    }
    // 排除超时错误
    // ... 
    report.TCPServerTransportReadFail.Incr() // 上报
    log.Trace("transport: tcpconn serve ReadFrame fail ", err) // 记录错误日志
    return
}
```

结束 `serve()`，释放对应的 tcp 连接。

需要强调的是，这个指标仅代表服务中的某个连接在读取帧时发生错误，并不意味着服务的其他连接或服务本身出现了异常。

tnet 中上报 `trpc.TcpServerTransportReadFail` 也是类似的逻辑。

```go
// transport.tnet.tcpConn.onRequest()
req, err := tc.framer.ReadFrame() // 读取请求帧
if err != nil {
    if err == tnet.ErrConnClosed { // 检测客户端关闭
        report.TCPServerTransportReadEOF.Incr()
        return err
    }
    report.TCPServerTransportReadFail.Incr() //上报
    log.Trace("transport: tcpConn onRequest ReadFrame fail ", err)
    return err
}
```
排查思路：检查是不是 trpc-go v0.17.0-v0.17.2，可能会出现该错误，请升级 trpc-go 版本。

#### 版本差异

- 该指标最早出现于 v0.2.2。

#### 相关案例
- 推测 trpc-go v0.17.0-v0.17.2 版本存在 bug，读包 `ReadFrame` 读了一半之后，`read deadline` 触发，然后下次再读包的时候没有接着上次的继续读，而是按一个新包开始重头读。
[trpc-cpp 请求 trpc-go，客户端 141 错误码，transport name:fiber_transport, sendrcv failed, ret:141，客户端 idletime<服务端 ideltime，还可能是什么原因呢？](https://mk.woa.com/q/293967?ADTAG=search )


### trpc.UdpServerTransportReadFail

`UdpServerTransportReadFail` 指标表示 UDP 服务器在读取帧时失败，通常发生在接收到的包不合法的情况下。当 UDP 服务器读取帧失败时，会上报 `UdpServerTransportReadFail` 指标，并继续循环读取数据到缓冲区，等待下次读取帧的机会。
相关代码片段：

```go
// transport.serverTransport.serveUDP
req, err := fr.ReadFrame() // 读取请求帧
if err != nil { // 读取失败
    report.UDPServerTransportReadFail.Incr() // 上报读取失败
    log.Trace("transport: udpconn serve ReadFrame fail ", err) // 记录错误日志
    continue // 继续下次循环
}
```

在发生错误后，服务器会使用 `continue` 跳过当前循环，放弃处理当前的非法请求帧，并继续处理后续的新请求。这意味着该错误不会直接导致当前的 UDP 服务关闭。

排查 `UdpServerTransportReadFail` 的思路是定位读取帧失败的原因，可以结合服务端的 `Trace` 级别日志进行分析。

tnet 会在以下三种情况下上报 `UdpServerTransportReadFail`：
- 读取 UDP 数据包失败：在 `ReadPacket` 调用中发生错误。
- 获取数据失败：在调用 `packet.Data()` 时发生错误。
- 读取帧失败：在调用 `framer.ReadFrame()` 时发生错误。
分别对应服务端 3 条 `trace` 级别的日志

```go
log.Trace("transport tnet: udpConn onRequest ReadPacket fail ", err)
// ...
log.Trace("transport tnet: udpConn onRequest GetData fail ", err)
// ...
log.Trace("transport tnet: udpConn onRequest ReadFrame fail ", err)
```
这些错误通常指示网络问题、数据包损坏或格式不正确等情况。

#### 版本差异

- 该指标最早出现于 v0.6.3。
- 特别注意 trpc-go v0.14.0 存在 bug 请升级。

#### 相关案例
- 使用的 trpc-go v0.14.0 中 Codec 插件存在复用 `msg` 大小的问题，导致读取帧失败。https://mk.woa.com/q/289575?ADTAG=search 


### trpc.UdpServerTransportUnRead

在 UDP 服务器成功读取完整帧后，如果仍然存在未读取的辅助数据，则会触发 `UdpServerTransportUnRead` 指标的上报。请注意，这个指标是一个计数器类型，每当出现这种情况时，计数器会增加 1，而不是上报剩余未读的字节数。
相关代码片段：

```go
// transport.serveUDP()
buf.Advance(num)
req, err := fr.ReadFrame() // 读帧
if err != nil {
    // ...
    if buf.UnRead() > 0 { // 检测剩余数据
        report.UDPServerTransportUnRead.Incr() // 上报
        log.Trace("transport: udpconn serve ReadFrame data remaining %d bytes data", buf.UnRead())
        continue
    }
}
```
在这种情况下，当前的帧将不会进行后续处理，程序会直接进入下一次循环。剩余的未读数据将在下次循环时被清空。

#### 版本差异

- 该指标最早出现在 v0.6.3。


### trpc.UdpClientTransportReadFail

`UdpClientTransportReadFail` 指标表示 UDP 客户端在读取帧时失败，通常发生在接收到的包不合法的情况下。
相关代码片段：

```go
// transport.clientTransport.udpReadFrame()
num, _, err := conn.ReadFrom(buf.Bytes()) // 读取数据到缓冲区
// ... 判断 err
buf.Advance(num)
// 读取完整二进制帧，包含了帧头的检测等操作
req, err := fr.ReadFrame() // 读取帧
if err != nil { // 读取失败
    report.UDPClientTransportReadFail.Incr() // 上报读取错误。
    return nil, errs.NewFrameError(errs.RetClientReadFrameErr,
    "udp client transport ReadFrame: "+err.Error())
}
```

在读取帧失败后，函数会向上返回错误信息。通过结合返回的错误信息，可以进一步定位问题的原因。

tnet 中上报 `UDPClientTransportReadFail` 也是类似的情况。

#### 版本差异

- 该指标最早出现在 v0.6.3。


### trpc.UdpClientTransportUnRead

`UdpClientTransportUnRead` 指标表示 UDP 客户端在成功读取完整帧后，仍然存在未读取的辅助数据。一个 UDP 包对应一个 TRPC 包，解析后不应有任何剩余数据。

相关代码片段：

```go
// transport.clientTransport.udpReadFrame()
// 读取完整二进制帧，包含了帧头的检测等操作
req, err := fr.ReadFrame() // 读取帧
// ...
if buf.UnRead() > 0 { // 检测剩余数据
    report.UDPClientTransportUnRead.Incr() // 上报
    return nil, errs.NewFrameError(errs.RetClientReadFrameErr,
    fmt.Sprintf("udp client transport ReadFrame: remaining %d bytes data", buf.UnRead()))
}
```
在这种情况下，读取将报告失败，并向上返回错误信息。开发者可以结合具体的错误信息进一步定位问题的根源。

#### 版本差异

- 该指标最早出现在 v0.6.5。


### trpc.UdpServerTransportReceiveSize

`UdpServerTransportReceiveSize` 指标表示 UDP 服务器接收到的请求包的大小。这里的包大小指的是服务端读取的二进制帧的大小，该大小在读取操作之后进行上报。
相关代码片段：

```go
// transport.serveUDP()
req, err := fr.ReadFrame() // 读取帧
// ...check
report.UDPServerTransportReceiveSize.Set(float64(len(req))) // 上报
if buf.UnRead() > 0 { // 检测剩余数据
    report.UDPServerTransportUnRead.Incr() // 上报
    log.Trace("transport: udpconn serve ReadFrame data remaining %d bytes data", buf.UnRead()) // 记录错误日志
    continue // 继续下次循环
}
```

即使上报了接收数据的大小，也有可能因为后续判断 UDP 包中有剩余数据而导致当前读取失败。
tnet 中上报 `UdpServerTransportReceiveSize`，也有类似情况，但没有未读数据的判断。
```go
// transport.tnet.udpConn.onRequest()
req, err := framer.ReadFrame() // 读取帧
// ...
report.UDPServerTransportReceiveSize.Set(float64(len(req))) //上报
// 提交到协程池处理 ...
```

#### 版本差异

- 该指标最早出现在 v0.7.0。


### trpc.UdpServerTransportSendSize

`UdpServerTransportSendSize` 指标表示 UDP 服务器发送的响应包的大小，这里指的是响应二进制帧的字节数。


相关代码片段：

```go
// transport.udpconn.handleSyncWithErr()
// 处理请求二进制帧得到响应二进制帧
rsp, err := c.conn.handle(ctx, req) // 处理请求
// ...check
// 上报字节数
report.UDPServerTransportSendSize.Set(float64(len(rsp))) // 上报
// 写操作
if _, err := c.writeTo(rsp, remoteAddr); err != nil {
    report.UDPServerTransportWriteFail.Incr()
    log.Tracef("udp write to fail:%v", err)
    return
}
```

在处理请求后，服务器会将生成的回复大小上报为 `UdpServerTransportSendSize` 指标。需要注意的是，随后的写入操作可能会失败，因此在上报回复大小后，仍可能发生写入过程中的错误。

tnet 中上报 `UDPServerTransportSendSize` 也是类似的情况。

#### 版本差异

- 该指标最早出现在 v0.7.0。


### trpc.TcpServerTransportJobQueueFullFail

`TcpServerTransportJobQueueFullFail` 指标表示 TCP 协程池的接收队列已满，请求过多的情况。该情况发生在服务端开启异步处理请求的模式下。当 TCP 协程读取到数据后，尝试将请求交给处理协程池时，如果队列已满，则会出现错误，并上报 `TcpServerTransportJobQueueFullFail`，同时记录 `trace` 级别的日志。


相关代码片段：

```go
// transport.tcpconn.handle()
if err := c.pool.Invoke(args); err != nil { // 提交失败
    report.TCPServerTransportJobQueueFullFail.Incr() // 上报
    log.Trace("transport: tcpconn serve routine pool put job queue fail ", err)
    c.handleSyncWithErr(req, errs.ErrServerRoutinePoolBusy) // 同步处理请求
}
```

此错误可以被写入响应的 msg 的 `serverRspErr` 中，并返回给客户端。但是这不会导致连接报错或结束，而是会使用同步方式处理请求。客户端得到的 `serverRspErr` 为：

```go
var ErrServerRoutinePoolBusy error = NewFrameError(RetServerOverload, "server goroutine pool too small")
```

其中包含过载错误码 22，意味着服务端希望将过载状态传达给客户端。

在 tnet 中上报 `TcpServerTransportJobQueueFullFail` 也有类似的情况。
相关代码片段：
```go
// transport.tnet.tcpConn.onRequest()
if err := tc.pool.Invoke(newTask(req, tc.handleSync)); err != nil { // 提交失败
    report.TCPServerTransportJobQueueFullFail.Incr() // 上报
    log.Trace("transport: tcpConn serve routine pool put job queue fail ", err)
    tc.handleWithErr(req, errs.ErrServerRoutinePoolBusy) // 同步处理请求    
}
```
观测到 `TcpServerTransportJobQueueFullFail` 时，说明服务出于过载状态，可以考虑增加过载保护来提高服务的健壮性。

#### 版本差异

- 该指标最早出现在 v0.5.0。


### trpc.UdpServerTransportJobQueueFullFail

`UdpServerTransportJobQueueFullFail` 指标表示 UDP goroutine 池的接收队列已满，说明处于请求过多的情况。当与客户端的连接上有传入数据时，会触发调用调用 UDP goroutine 池。目前，该指标仅在 tnet 中使用。
相关代码片段：

```go
// transport.tnet.udpConn.onRequest()
req, err := framer.ReadFrame() // 读取帧
// ...check
// 向线程池提交失败
if err := uc.pool.Invoke(newUDPTask(req, remoteAddr, uc.handleSync)); err != nil { // 提交失败  
    report.UDPServerTransportJobQueueFullFail.Incr() // 上报
    log.Trace("transport tnet: udpConn serve routine pool put job queue fail ", err)
    uc.handleWithErr(req, remoteAddr, errs.ErrServerRoutinePoolBusy) // 同步处理请求    
}
```
此错误可以被写入响应的 msg 的 `serverRspErr` 中。这不会导致连接报错或结束，而是会使用同步方式处理请求。主调得到的 `serverRspErr` 为：
```go
var ErrServerRoutinePoolBusy error = NewFrameError(RetServerOverload, "server goroutine pool too small")
```
也就是包含过载错误码 22。

#### 版本差异

- 该指标最早出现在 v0.5.0。


### trpc.TcpServerTransportRequestLimitedByOverloadCtrl

`TcpServerTransportRequestLimitedByOverloadCtrl` 指标表示请求受到过载控制的限制。在开启服务端过载保护后，在服务端的 service 的 Handle 方法中，在解码请求后、执行请求逻辑处理之前，首先通过 OverloadCtrl.Acquire 获取 `token`。
相关代码片段：

```go
// server.service.Handle()
token, err = s.opts.OverloadCtrl.Acquire(ctx, addr) // 获取 token   
if err != nil { // 获取 token 失败
    report.TCPServerTransportRequestLimitedByOverloadCtrl.Incr() // 上报    
    return s.encode(ctx, msg, nil,
        errs.NewFrameError(errs.RetServerOverload, err.Error())) // 返回并打包错误  
    }
}
// ...
rspBody, err := s.handle(ctx, msg, reqBodyBuf)
```

如果获取 token 失败，则会上报 `TcpServerTransportRequestLimitedByOverloadCtrl`。此时，处理逻辑将不会执行，服务器会立即向客户端打包一个响应体为空的回包，错误码为服务端过载 `RetServerOverload = 22`。
需要注意的是，这个上报并不意味着服务器崩溃，而只是表明该次请求受到了过载保护的限制。

#### 版本差异

- 该指标最早出现在 v0.7.0。


### trpc.TcpServerAsyncGoroutineScheduleDelay_us

`TcpServerAsyncGoroutineScheduleDelay_us` 指标表示在 server_transport_tcp 中，当异步处理开启时，`goroutine` 池的调度延迟。该延迟以微秒为单位。

相关代码片段：

```go
// transport.createRoutinePool()
pool, err := ants.NewPoolWithFunc(size, func(args interface{}) { // 创建协程池
    // 记录调度时刻，上报调度耗时
    report.TCPServerAsyncGoroutineScheduleDelay.Set(float64(time.Since(param.start).Microseconds()))
    // ...
}
```
```go
// tansport.tcpconn.handle()
args.start = time.Now() //  在提交时记录开始时刻
if err := c.pool.Invoke(args); err != nil { // 提交
    // ...	
}
```
可以看到，这个时延仅包括从提交任务开始到执行处理逻辑之前的时间，不包括处理逻辑本身。因此，它反映的是协程池的调度延迟。
导致协程池调度延迟高的原因可能有以下几种：
- 任务负载过重：如果任务比较重，可能会占用过多的协程，导致调度延迟。
- CPU 负载高：当 CPU 负载较高时，也可能导致调度延迟。

tnet 中上报 `TcpServerAsyncGoroutineScheduleDelay_us` 也是一样的含义。

#### 版本差异

- 该指标最早出现在 v0.7.0。

#### 相关案例

- 通过优化了包大小后有解决问题。因为这一块的处理主要是收包之后的逻辑处理（也包括收到二进制请求包之后的 `decode`、`encode` 等逻辑）+ 写回数据（这个可能是异步实现），优化包大小可以提升处理效率。也存在 CPU 不足的可能。
  - [TRPC-go 属性监控 中的 trpc.TcpServerAsyncGoroutineScheduleDelay_us 是什么含义？](https://mk.woa.com/q/284266?from=iSearch)
- CPU 相关问题导致的，例如服务过载了，可是尝试使用过载保护。
  - [为什么 trpcgo 服务失败呢？](https://mk.woa.com/q/292647?ADTAG=search)
  - [trpc-go 服务某个节点超时率上升，trpc 连接池激增导致调用被调超时？](https://mk.woa.com/q/288645?from=iSearch)
  - [trpc-go 服务，当 CPU 配置低时接口耗时高，但是 CPU 负载不高，换高 CPU 就没有问题，这是为什么呢？](https://mk.woa.com/q/284487/answer/102158?ADTAG=u_q_with_answer)

关于协程池可以了解：
- [ants 库](https://github.com/panjf2000/ants)


### trpc.LogQueueDropNum

`LogQueueDropNum` 指标表示由于日志队列已满而被丢弃的日志数量。此情况发生在日志写入模式为 3-极速（异步丢弃）时，如果未配置，则默认使用极速模式，即异步丢弃。
日志配置示例：
```yaml
plugins:
  log:  # 所有日志配置
    default:  # 默认日志配置，log.Debug("xxx")
      - writer: file  # 本地文件日志
        writer_config:
          write_mode: 3  # 日志写入模式，1-同步，2-异步，3-极速 (异步丢弃), 不配置默认异步模式
```
当日志队列满时，新增的日志将被丢弃，并上报 `LogQueueDropNum`。


相关代码片段：

```go
// log.rollwriter.AsyncRollWriter.Write()
func (w *AsyncRollWriter) Write(data []byte) (int, error) {
    log := make([]byte, len(data))
    copy(log, data)
    if w.opts.DropLog { // 异步丢弃模式
        select {
        case w.logQueue <- log:
        default: // 队列已满
            report.LogQueueDropNum.Incr() // 上报
            return 0, errors.New("log queue is full")
        }
    } else {
        w.logQueue <- log
    }
    return len(data), nil
}
```

#### 相关案例

- 注意不配置日志模式默认是（3-异步丢弃）。
  - [trpc-go 日志 write_mode 没有配置，但有 trpc.LogQueueDropNum 上报？](https://mk.woa.com/q/286742?ADTAG=search)
- 服务过载时有可能会出现这种情况。
  - [为什么 trpcgo 服务失败呢？](https://mk.woa.com/q/292647?ADTAG=search)

#### 版本差异

- 该指标最早出现于 v0.5.2。

### trpc.LogWriteSize

`LogWriteSize` 指标表示日志的写入大小。在写操作之前，构造日志字符串后，会上报该字符串的长度。
相关代码片段：

```go
func getLogMsg(args ...interface{}) string {
    msg := fmt.Sprintln(args...) // 构造日志字符串
    msg = msg[:len(msg)-1] // 去掉换行符
    report.LogWriteSize.IncrBy(float64(len(msg))) // 上报长度
    return msg
}

func getLogMsgf(format string, args ...interface{}) string {
    msg := fmt.Sprintf(format, args...) // 构造日志字符串
    report.LogWriteSize.IncrBy(float64(len(msg))) // 上报长度
    return msg
}
```

#### 版本差异

- 该指标最早出现于 v0.5.2。


### trpc.SelectNodeFail

`SelectNodeFail` 指标表示客户端选择服务器节点失败。这种情况通常发生在名称服务未正确配置或所有节点已被熔断时。当客户端在调用 `SelectNode` 时，`getNode` 失败时会上报该指标。此时，客户端会返回错误原因，结束此次调用，不会发起后续请求，因此服务端不会受到影响。
相关代码片段：

```go
// client.selectNode()
node, err := getNode(opts) // 会调用具体的 selector.Select()
if err != nil {
    report.SelectNodeFail.Incr() // 上报
    return nil, err // 具体的错误会在主调方返回
}
```

#### 返回的错误常见的是

- `errs.NewFrameError(errs.RetClientRouteErr, "client Select: "+err.Error())`
- `errs.NewFrameError(errs.RetClientRouteErr, fmt.Sprintf("client Select: node address empty: %+v", node))`

其中错误码为：

- `RetClientRouteErr = 131`

另一个场景是在流式的 `Init` 方法中，也会调用普通客户端的 `SelectNode`。
相关代码片段：
```go
// client.stream.Init()
node, err := selectNode(ctx, msg, opts) // 调用 selectNode
if err != nil {
    report.SelectNodeFail.Incr() // 上报
    return nil, err
}
```
当出现 `SelectNodeFail` 上报时，可以结合主调方的日志来定位具体的错误信息，以便进行排查。

#### 版本差异

- 该指标最早出现于 v0.2.2。


### trpc.ClientCodecEmpty

客户端未配置编码协议，即 `Codec` 为空时会进行上报。在这种情况下，客户端会返回相应的错误。

相关代码片段：

```go
// client.callFunc()
if opts.Codec == nil {
    report.ClientCodecEmpty.Incr() // 上报
    return errs.NewFrameError(errs.RetClientEncodeFail, "client: codec empty")
}
// ...
// 序列化、（压缩）、打包
reqBuf, err := prepareRequestBuf(span, msg, reqBody, opts)
```

错误码为 `RetClientEncodeFail = 121`。

在流式的 `Init` 方法中，也会检查 `Codec` 是否为空：

```go
// client.stream.Init()
if opts.Codec == nil {
    report.ClientCodecEmpty.Incr() // 上报
    return nil, errs.NewFrameError(errs.RetClientEncodeFail, "client: codec empty")
}
// ...
return s.opts, nil
```

#### 版本差异

- 该指标最早出现于 v0.2.2。

### trpc.LoadClientConfigFail

> 注意：当前该指标目前未被框架使用。

`LoadClientConfigFail` 指标表示加载客户端配置失败，这通常发生在客户端未正确配置的情况下。当客户端的配置未能正确加载时，客户端通常会启动失败。此时，可以结合报错信息进行排查，以确定具体问题所在。

#### 版本差异

- 该指标最早出现于 v0.2.2。
- 在 v0.8.2 中被移除。

### trpc.LoadClientFilterConfigFail

> 注意：当前该指标目前未被框架使用。

`LoadClientFilterConfigFail` 指标表示加载客户端过滤器配置失败，这通常发生在客户端过滤器数组中配置了不存在的过滤器。当客户端过滤器配置失败时，客户端在启动时会报出与过滤器相关的错误。此时，可以结合报错信息进行排查，以确定具体问题所在。

#### 版本差异

- 该指标最早出现于 v0.2.2。
- 在 v0.8.2 中被移除。

### trpc.TcpClientTransportSendSize

`TcpClientTransportSendSize` 指标表示 TCP 客户端请求包的大小。这里的包大小指的是经过编码打包后的字节数，记录发生在写操作之前。
相关代码片段：

```go
// transport.clientTransport.tcpRoundTrip()
report.TCPClientTransportSendSize.Set(float64(len(reqData))) // 上报
// Write data to connection.
err = c.tcpWriteFrame(ctx, conn, reqData) // 写包
```

使用 tnet 包中的发包操作也是类似逻辑：

```go
// transport.tnet.clientTransport.tcpRoundTrip()
report.TCPClientTransportSendSize.Set(float64(len(reqData))) // 上报
// Send a request.
err = tcpWriteFrame(conn, reqData) // 写包
```

#### 版本差异

- 该指标最早出现于 v0.7.0。

### trpc.TcpClientTransportReceiveSize

`TcpClientTransportReceiveSize` 指标表示 TCP 客户端接收到的响应包大小。这里的包大小指的是从网络中读取的二进制帧的字节数，记录发生在读操作之后。

相关代码片段：

```go
// transport.clientTransport.tcpReadFrame()
rspData, err := fr.ReadFrame() // 读取二进制响应帧
// ...
report.TCPClientTransportReceiveSize.Set(float64(len(rspData))) // 上报
```

使用 tnet 包中的收包操作时，逻辑也是类似的：

```go
// transport.tnet.clientTransport.tcpReadFrame()
rspData, err := fr.ReadFrame() // 读取二进制响应帧
// ...
report.TCPClientTransportReceiveSize.Set(float64(len(rspData))) // 上报
```

#### 版本差异

- 该指标最早出现于 v0.7.0。

### trpc.UdpClientTransportSendSize

`UdpClientTransportSendSize` 指标表示 UDP 客户端请求包的大小。这里的包大小指的是经过编码打包后的字节数。该指标上报发生在写操作之前，因为在监控到 `UdpClientTransportSendSize` 后，后续还可能会出现发包失败的情况。

相关代码片段：

```go
// transport.clientTransport.udpRoundTrip()
// 创建 UDP 连接、超时检查等过程。
// ...
report.UDPClientTransportSendSize.Set(float64(len(reqData))) // 上报
// 写
if err := c.udpWriteFrame(conn, reqData, addr, opts); err != nil {
    return nil, err
}
// 读
return c.udpReadFrame(ctx, conn, opts)
```

使用 tnet 包中上报 UdpClientTransportSendSize 也是类似逻辑。

#### 版本差异

- 该指标最早出现于 v0.7.0。

### trpc.UdpClientTransportReceiveSize

`UdpClientTransportReceiveSize` 指标表示 UDP 客户端接收到的响应包大小。这里的包大小指的是从网络中读取的二进制包的字节数。该指标上报发生在读操作之后。

相关代码片段：

```go
// transport.clientTransport.udpReadFrame()
// 读取完整二进制帧，包含了帧头的检测等操作
req, err := fr.ReadFrame() // 读取帧
// ...
report.UDPClientTransportReceiveSize.Set(float64(len(req))) // 上报
```

使用 tnet 包中上报 `UdpClientTransportReceiveSize` 的逻辑也是类似的。

#### 版本差异

- 该指标最早出现于 v0.7.0。

### trpc.ConnectionPoolGetNewConnection

`ConnectionPoolGetNewConnection` 指标表示连接池中新创建的连接数量。当尝试从连接池中获取一个连接时，如果没有空闲连接可用，则会新增一个连接。在新增连接之前，会上报 `ConnectionPoolGetNewConnection`。

相关代码片段：

```go
// pool.connpool.ConnectionPool.getNew()
func (p *ConnectionPool) getNewConn(ctx context.Context) (*PoolConn, error) {
    // ...
    c, err := p.dial(ctx) // 创建连接
    // ...
    report.ConnectionPoolGetNewConnection.Incr() // 上报
    return p.newPoolConn(c), nil
}
```

#### 版本差异

- 该指标最早出现于 v0.7.2。

### trpc.ConnectionPoolGetConnectionErr

`ConnectionPoolGetConnectionErr` 指标在从连接池获取连接失败时上报，同时会将错误信息向上返回。这些错误可能包括并发错误、连接池关闭、创建新连接失败等情况。

相关代码片段：

```go
// pool.connpool.ConnectionPool.Get()
func (p *ConnectionPool) Get(ctx context.Context) (*PoolConn, error) {
    var (
        pc  *PoolConn
        err error
    )
    if pc, err = p.get(ctx); err != nil { // 获取连接失败
        report.ConnectionPoolGetConnectionErr.Incr() // 上报
        return nil, err // 返回错误
    }
    return pc, nil
}
```

在发生错误时，可以结合客户端的错误日志进行进一步排查。

#### 版本差异

- 该指标最早出现于 v0.7.2。

### trpc.ConnectionPoolRemoteErr

`ConnectionPoolRemoteErr` 指标表示连接池的远程对等方出现错误，例如远程对等方已关闭连接。`defaultChecker` 是默认的空闲连接检查方法，返回 `true` 表示连接正常可用。

相关代码片段：

```go
// pool.connpool.ConnectionPool.defaultChecker()
func (p *ConnectionPool) defaultChecker(pc *PoolConn, isFast bool) bool {
    // 判断是否是对等方失败。关闭、网络异常、粘包处理异常。
    if pc.isRemoteError(isFast) {
        return false
    }
    // ...
    return true
}

// pool.connpool.ConnectionPool.isRemoteError()
// isRemoteError 尝试接收一个字节以检测对等方是否主动关闭了连接。
// 如果对等方返回 io.EOF 错误，则表示对等方已关闭。
// 空闲连接不应读取数据，如果读取了数据，则意味着上层的粘包处理未完成，
// 该连接也应被丢弃。
// 如果连接出现错误，则返回 true。
func (pc *PoolConn) isRemoteError(isFast bool) bool {
    var err error
    if isFast {
        err = checkConnErrUnblock(pc.Conn, globalBuffer)
    } else {
        err = checkConnErr(pc.Conn, globalBuffer)
    }
    if err != nil { // 连接池的远程对等方出现错误
        report.ConnectionPoolRemoteErr.Incr() // 上报。
        return true
    }
    return false
}
```

#### 版本差异

- 该指标最早出现于 v0.7.2。

### trpc.ConnectionPoolRemoteEOF

`ConnectionPoolRemoteEOF` 指标表示连接池的远程对等方返回了 EOF（文件结束符）。客户端连接池的健康检查机制会定期检查空闲连接是否已经被服务端关闭，以防止客户端获取到已经被服务端关闭的连接。`trpc.ConnectionPoolRemoteEOF` 的出现说明某些空闲连接已被服务端关闭，但由于这些连接是空闲的，因此不会影响业务逻辑。
相关代码片段：

```go
// pool.connpool.checkConnErrUnblock()
err = rawConn.Read(func(fd uintptr) bool {
    // Go 默认将 socket 设置为非阻塞模式，调用 syscall 可以直接返回。
    n, sysErr = unix.Read(int(fd), buf)
    // 返回 true，net 库封装的阻塞等待不会被执行，直接返回。
    return true
})
if err != nil {
    return err
}
// 连接关闭，返回 io.EOF
if n == 0 && sysErr == nil {
    report.ConnectionPoolRemoteEOF.Incr() // 上报
    return io.EOF
}
```

一般来说，连接大部分情况下是由客户端（而不是服务端）主动关闭的。上报 `trpc.ConnectionPoolRemoteEOF` 通常是由于服务端偶发了一些异常，导致服务端主动关闭连接，或者是服务端的空闲超时时间比客户端短，导致服务端在客户端之前触发空闲超时（例如 trpc-go 框架默认客户端空闲超时为 50 秒，服务端空闲超时为 60 秒）。

#### 版本差异

- 该指标最早出现于 v0.7.2。

#### 相关案例

- 检查空闲连接中被服务端关闭的连接，可能是服务端偶发异常，或者服务端触发了连接空闲超时。
[trpc-go 服务上报的自定义指标 trpc.ConnectionPoolRemoteEOF 是当前服务请求下游，下游没数据吧？还是说本服务的连接有问题？](https://mk.woa.com/q/296668?ADTAG=search)

### trpc.ConnectionPoolIdleTimeout

`ConnectionPoolIdleTimeout` 指标表示连接已达到其空闲超时。在连接池默认的空闲连接检查方法中，会检查连接是否超过最大空闲时间。如果连接超过了该时间，则会关闭连接并上报 `ConnectionPoolIdleTimeout`。
相关代码片段：

```go
// pool.connpool.ConnectionPool.defaultChecker()
func (p *ConnectionPool) defaultChecker(pc *PoolConn, isFast bool) bool {
    // ...
    // 检查连接是否超过最大空闲时间，如果超过则关闭连接
    if p.IdleTimeout > 0 && pc.t.Add(p.IdleTimeout).Before(time.Now()) {
        report.ConnectionPoolIdleTimeout.Incr() // 上报
        return false
    }
    // ...
    return true
}
```

#### 版本差异

- 该指标最早出现于 v0.7.2。

### trpc.ConnectionPoolLifetimeExceed

`ConnectionPoolLifetimeExceed` 指标表示连接已超过其生命周期。在连接池默认的空闲连接检查方法中，会检查连接是否超过最大生命周期。如果连接超过了该时间，则会关闭连接并上报 `ConnectionPoolLifetimeExceed`。
相关代码片段：

```go
// pool.connpool.ConnectionPool.defaultChecker()
func (p *ConnectionPool) defaultChecker(pc *PoolConn, isFast bool) bool {
    // ...
    // 检查连接是否仍然有效
    if p.MaxConnLifetime > 0 && pc.created.Add(p.MaxConnLifetime).Before(time.Now()) {
        report.ConnectionPoolLifetimeExceed.Incr() // 上报
        return false
    }
    return true
}
```

#### 版本差异

- 该指标最早出现于 v0.7.2。

### trpc.ConnectionPoolOverLimit

> 注意：该指标目前在框架中未被使用。

`ConnectionPoolOverLimit` 指标表示连接数量已达到其限制。

#### 版本差异

- 该指标最早出现于 v0.7.2。
- 于 v0.11.0 移除。


### trpc.MultiplexedReconnectErr

`MultiplexedReconnectErr` 指标表示在多路复用时重连失败。
相关代码片段：

```go
// pool.multiplexed.Connection.reconnect()
for {
    conn, err := tryConnect(c.dialOpts) // 尝试连接
    if err != nil { // 连接失败
        report.MultiplexedTCPReconnectErr.Incr() // 上报
        log.Tracef("reconnect fail: %+v", err)
        if !c.doReconnectBackoff() {
            // 如果当前重试次数超过最大重试次数，
            // doReconnectBackoff 将返回 false，需要移除相应的连接。
            return false // 新的请求将触发重新连接。
        }
        continue
    }
}
```

#### 版本差异

- 该指标最早出现于 v0.7.3。

### trpc.MultiplexedReconnectOnReadErr

`MultiplexedReconnectOnReadErr` 指标表示在读取时多路复用重连失败。
相关代码片段：

```go
// pool.multiplexed.Connection.reader()
for {
    select {
    case <-c.done:
        return
    default:
    }
    response, err := c.decode()
    if err != nil {
        // 如果在 TCP 解包时发生错误，可能会导致后续解析出现问题，
        // 因此需要关闭并进行重连。
        if c.isStream { // 如果是 TCP 连接
            lastErr = err // 记录错误
            report.MultiplexedTCPReconnectOnReadErr.Incr() // 上报
            log.Tracef("reconnect on read err: %+v", err) // 记录错误日志
            c.close(lastErr, c.shouldReconnect(lastErr)) // 关闭连接
            return
        }
        // 对于 UDP，处理是基于单个数据包的，
        // 接收到非法数据包不会影响后续数据包的处理逻辑，
        // 可以继续接收数据包。
        log.Tracef("decode packet err: %s", err) // 记录错误日志
        continue // 继续接收数据包
    }
}
```

#### 版本差异

- 该指标最早出现于 v0.7.3。

### trpc.MultiplexedReconnectOnWriteErr

`MultiplexedReconnectOnWriteErr` 指标表示在写入时多路复用重连失败。

相关代码片段：

```go
// pool.multiplexed.Connection.writer()
if err := c.writeAll(it); err != nil {
    if c.isStream { // 如果 TCP 写入数据失败，将导致对端关闭连接。
        lastErr = err // 记录错误
        report.MultiplexedTCPReconnectOnWriteErr.Incr() // 上报
        log.Tracef("reconnect on write err: %+v", err) // 记录错误日志
        c.close(lastErr, c.shouldReconnect(lastErr)) // 关闭连接
        return
    }
    // 对于 UDP，发送数据包失败后仍然可以继续发送其他数据包。
    log.Tracef("multiplexed send UDP packet failed: %v", err) // 记录错误日志
    continue // 继续发送数据包
}
```

#### 版本差异

- 该指标最早出现于 v0.7.3。

### trpc.PanicNum

`PanicNum` 指标表示 `trpc.GoAndWait` 的 `panic` 数量。当发生 `panic` 时，该指标会被上报。
相关代码片段：

```go
// tprc.asyncGoer.handle()
func (g *asyncGoer) handle(ctx context.Context, handler func(context.Context), cancel context.CancelFunc) {
    defer func() {
        if g.shouldRecover {
            if err := recover(); err != nil { // 发生 panic
                buf := make([]byte, g.panicBufLen)
                buf = buf[:runtime.Stack(buf, false)]
                log.ErrorContextf(ctx, "[PANIC]%v\n%s\n", err, buf) // 记录错误日志
                report.PanicNum.Incr() // 上报
            }
        }
        cancel()
    }()
    handler(ctx)
}
```

`GoAndWait` 提供了安全的并发处理。对于每个输入的处理器，它会启动一个 `goroutine`，并等待所有处理器完成。如果任何处理器发生 `panic`，它会进行恢复。返回的错误是第一个由处理器返回的非 `nil` 错误。可以设置为当特定的 `key` 处理器失败时返回非 `nil` 错误，而其他处理器则始终返回 `nil` 错误。

#### 版本差异

- 该指标最早出现于 v0.6.6。

### trpc.AdminPanicNum

`AdminPanicNum` 指标表示 `admin` 的 `panic` 数量。当 `ServeHTTP` 失败时，该指标会被上报，并伴随相应的日志记录。
相关代码片段：

```go
// admin.router.ServeHTTP()
func (r *router) ServeHTTP(w http.ResponseWriter, req *http.Request) {
    defer func() {
        if err := recover(); err != nil { // 发生 panic
            var ret = newDefaultRes()
            ret[ReturnErrCodeParam] = http.StatusInternalServerError
            ret[ReturnMessageParam] = fmt.Sprintf("PANIC : %v", err)
            buf := make([]byte, panicBufLen)
            buf = buf[:runtime.Stack(buf, false)]
            log.Errorf("[PANIC]%v\n%s\n", err, buf) // 记录错误日志
            report.AdminPanicNum.Incr() // 上报
            _ = json.NewEncoder(w).Encode(ret)
        }
    }()
    r.ServeMux.ServeHTTP(w, req)
}
```

在上述代码中，当 `ServeHTTP` 方法发生 `panic` 时，程序会捕获该错误，并记录详细的错误信息和堆栈信息。同时，`AdminPanicNum` 指标会被上报，以便进行监控和分析。

#### 版本差异

- 该指标最早出现于 v0.11.0。

## 参考链接

- [tRPC-Go 指标监控](https://iwiki.woa.com/p/870029531)
- [tRPC-Cpp 属性上报各字段含义](https://iwiki.woa.com/p/1542164171)
- [tRPC-GO prometheus 监控插件](https://iwiki.woa.com/p/4009549074?from=iWiki_AI_reference)
- [tRPC-Go 模块：metrics](https://iwiki.woa.com/p/99485605?from=iWiki_AI_reference)
- [tRPC-Go TEG 智研 - 监控平台插件](https://iwiki.woa.com/p/4009549008?from=iWiki_AI_reference)
- [tRPC-Go runtime 监控](https://git.woa.com/trpc-go/trpc-metrics-runtime/blob/master/README.zh_CN.md)

