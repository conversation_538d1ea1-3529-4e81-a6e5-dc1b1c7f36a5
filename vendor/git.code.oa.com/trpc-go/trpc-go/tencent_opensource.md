# 腾讯内部开源协同管理规范（暨公约）

为规范腾讯内部开源协同的代码使用、权限管理和对外披露等场景，本标准将于 2023 年 6 月制定发布。请全体员工自觉遵守本标准。腾讯公司对违反公约的行为保留追究权利。

# 1. 目的及适用范围

## 1.1. 目的

为规范公司内部开源的使用与管理，同时也切实维护公司知识产权和充分尊重原发布者/原发布团队的著作权，特制定本标准。

## 1.2. 适用范围

```text
本标准适用于腾讯控股集团 (含分公司等各级分支机构) 所有 Oteam。
```

本规范适用于腾讯集团（以下简称“集团”）及下属分子公司、办事处的全体员工，包括但不限于：正式员工（含试用期）、实习生、外包人员、顾问等，上述人员均应当遵守本规范规定。

腾讯集团是指腾讯控股有限公司、纳入腾讯集团统一管理的附属公司及为会计而综合入账的公司，包括腾讯集团本部及腾讯集团下属全资子公司和分支机构。

其中腾讯集团本部公司是指由人委会及 GCTSM 审批确认为“腾讯集团本部”管理主体的公司，包括但不限于：

——注册在中国大陆的腾讯主体——深圳市腾讯计算机系统有限公司、腾讯科技（深圳）有限公司、腾讯科技（北京）有限公司、腾讯科技（上海）有限公司、腾讯科技（成都）有限公司、腾讯云计算（北京）有限公司、财付通支付科技有限公司等。

腾讯集团下属全资子公司是指由人委会及 GCTSM 审批确认为“腾讯集团下属全资管理主体”的公司，如重庆市瑞德铭科技发展有限公司、腾讯云雀（青岛）信息技术有限公司、腾讯云科技（武汉）有限责任公司、腾讯云计算（西安）有限责任公司、腾讯云计算（长沙）有限责任公司、腾讯云计算（重庆）有限责任公司、深圳市腾佳管理咨询有限公司等。

# 2. 总体原则及概念定义

## 2.1. 总体原则

1. 技术委员会第四次会议明确提出“非敏感项目（含隐私数据等）均需内部开源”。
2. 集团所有内部开源协同的代码管理与使用必须依照本规范执行，各 Oteam 可基于各自情况在本规范基础上扩充、细化具体要求。

## 2.2. 重点角色定义

|        概念        |                             定义                             |
| :----------------: | :----------------------------------------------------------: |
|     技术委员会     | 负责统筹公司整体技术战略规划，指导大型技术项目的落地；提升研发团队的技术能力，优化技术人员培训和管理机制；制定公司级技术规范与流程，维护高效的研发环境；营造创新合作的技术氛围，提高技术人员的归属感，由决策委员、执行委员和 PMO 小组构成。 |
| 开源协同项目管理组 | 负责推动项目执行策略在各 BG 的落地，赋能 Oteam 协同管理的能力、进展跟进和落地推广等，由各技术领域开源经理（包含技术经理和运营经理）构成。 |
|        PMC         | Project Management Committees，开源项目管理委员，负责 Oteam 的管理及决策，包括但不限于 Oteam 的技术规划，顶层设计，版本开发，推广及落地等；Oteam 成立之初，团队 PMC 成员由组建 Oteam 的各团队推荐产生，建议各团队推荐 1-2 人保持人数均等；PMC 成员包含技术负责人、社区运营负及项目经理等角色。 |
|       Oteam        | “Oteam”为一个开源协同小组，由在同一技术方向下开发不同产品的多个团队组成。同时也代表了公司在某一领域的技术实力，能够输出公司级的解决方案。 |

# 3. **代码管理规范**

 Oteam 代码须遵守《软件源代码安全管理规范》，如对外开源须遵守《腾讯开源管理规范》。

## 3.1. 代码开源方式

原则上建议公司内开源，Oteam 可根据自身情况设置 fork 权限、clone 权限，相关方有需要可以向 PMC 进行权限申请（如部分 Oteam 需商业化或涉及敏感代码，Oteam 可根据实际情况设置 Oteam 内开源权限）

## 3.2. 代码权限管理

1. Oteam 须建立独立代码库并统一管理，各方在此基础上进行协同开发。
2. Oteam 代码需上传至集团代码管理工具 - 工蜂系统，包含初始版本和后续更新版本。协同后的 Oteam 代码权限管理方案由 PMC 共同讨论决定；PMC 可对工蜂代码库行使权限管理。

## 3.3. 代码使用管理

1. Oteam 代码只允许在权限范围内使用，不得私自通过任何介质（包含但不限于移动硬盘、网盘、网络传输、打印等）传播。
2. 衍生的代码中（对开源项目的应用、二次修改等）须携带且遵循源代码中的协议，商标，专利声明和其他源代码的规定和说明；原则上鼓励为 Oteam 回流共建。
3. 引用 Oteam 代码或服务进行商业化、申请专利、行业认证等须经 OteamPMC 共同审核同意。
4. 原则上鼓励使用 Oteam 的统一服务，如有特殊需求，须与 Oteam PMC 沟通协商确认。

## 3.4. 对外披露管理

1. Oteam 协同后的成果（包括但不限于专利、论文、测试结果等）归属于 Oteam，如需申请专利、发表论文、公共宣传等需经过 PMC 的讨论决策。
2. Oteam 代码被用作外部商业应用时，需经原开源项目代码贡献团队的同意，若原贡献团队发生变更，则由现有团队共同决策。

# 4. 违规责任

1. Oteam 协同后，任何事项均由 Oteam PMC 协商决定，如无法决策，可升级到对应的开源经理（详见附件）。
2. 存在违反本规范行为时，可联系开源协同项目管理组进行举报（举报邮箱：[<EMAIL>](mailto:<EMAIL>)）。
3. 开源协同项目管理组将对举报的违反行为展开调查并处理相关涉事团队或个人，同时会全力维护举报人的权益；调查结果将依据情节严重程度，进行公司级/BG 级通报处理，并作为违规当事人及项目负责人、部门负责人追究法律责任和管理责任的重要依据。
4. 公司禁止任何对举报者采取打击、报复的行为，一经发现将会按公司相关规定严格处理。

# 5. 其他

1. Oteam 代码库须携带该规范，下载[tencent_opensource.md](https://iwiki.woa.com/download/attachments/2582474373/tencent_opensource.md?version=1&modificationDate=1686038054000&api=v2)文件，并放置到代码工程的根目录。
2. 本规范由腾讯技术委员会开源协同项目管理组负责修订、解释。
3. 本规范自发布之日起实施。

**附录：**

| 序号 |       领域       |      开源经理       |
| :--: | :--------------: | :-----------------: |
|  1   |   AI 技术委员会   |  ciciliaguan(管蓉)  |
|  2   |  安全技术委员会  | cyndizhang(张彩萍)  |
|  3   |  测试技术委员会  |  jeffpeng(彭浩书)   |
|  4   | 大数据技术委员会 |    waypeng(彭伟)    |
|  5   | 多媒体技术委员会 | derekgbzhou(周桂邦) |
|  6   |  前端技术委员会  | cyndizhang(张彩萍)  |
|  7   |  设计技术委员会  | cyndizhang(张彩萍)  |
|  8   | 数据库技术委员会 | derekgbzhou(周桂邦) |
|  9   |  研效技术委员会  |  jeffpeng(彭浩书)   |
|  10  |  硬件技术委员会  |    dashwei(魏旸)    |
|  11  |      无领域      |    dashwei(魏旸)    |
