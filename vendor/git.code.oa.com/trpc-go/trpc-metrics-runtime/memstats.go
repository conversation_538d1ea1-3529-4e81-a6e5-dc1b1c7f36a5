// Package runtime provides runtime metrics.
package runtime

import (
	"runtime/debug"
	"runtime/metrics"
	"time"

	tmetrics "git.code.oa.com/trpc-go/trpc-go/metrics"
)

const (
	goGCCyclesTotalCycles           = "/gc/cycles/total:gc-cycles"
	goGCHeapAllocsBytes             = "/gc/heap/allocs:bytes"
	goGCHeapGoalBytes               = "/gc/heap/goal:bytes"
	goMemoryClassesTotalBytes       = "/memory/classes/total:bytes"
	goMemoryClassesHeapObjectsBytes = "/memory/classes/heap/objects:bytes"
	goCPUClassesGCTotalSeconds      = "/cpu/classes/gc/total:cpu-seconds"
	goCPUClassesTotalSeconds        = "/cpu/classes/total:cpu-seconds"
	mebibyte                        = 1024 * 1024
)

var (
	// lastGcNum 上次 gc num
	lastGcNum = -1
	// lastGcPause in nanoseconds
	lastGcPause time.Duration
	// lastGcTime from runtime/debug
	lastGcTime time.Time
	// all samples retrieved from runtime/metrics
	sampleBuf []metrics.Sample
	// allow lookup sample by Name
	sampleMap map[string]*metrics.Sample
	// gcstats from runtime/debug
	gcStats debug.GCStats
)

// sample metrics names for memstats
var rmNamesForMemStatsMetrics = []string{
	goGCCyclesTotalCycles,
	goGCHeapAllocsBytes,
	goGCHeapGoalBytes,
	goMemoryClassesTotalBytes,
	goMemoryClassesHeapObjectsBytes,
	goCPUClassesGCTotalSeconds,
	goCPUClassesTotalSeconds,
}

// init runtime/metrics sample
func init() {
	sampleBuf = make([]metrics.Sample, 0, len(rmNamesForMemStatsMetrics))
	sampleMap = make(map[string]*metrics.Sample, len(rmNamesForMemStatsMetrics))
	for i, name := range rmNamesForMemStatsMetrics {
		sampleBuf = append(sampleBuf, metrics.Sample{Name: name})
		sampleMap[name] = &sampleBuf[i]
	}
}

// getMemStats get metrics about memory from runtime/metrics
func getMemStats() {
	metrics.Read(sampleBuf)
	debug.ReadGCStats(&gcStats)

	currentGcNum := int(lookupOrZeroUint64(sampleMap, goGCCyclesTotalCycles))

	if currentGcNum == lastGcNum {
		tmetrics.Gauge("trpc.PauseNs_us").Set(0.0)
	} else {
		pauseNs := gcStats.PauseTotal - lastGcPause
		var pause100us, pause500us, pause1ms, pause10ms, pause50ms, pause100ms, pause500ms, pause1s, pause1sp int

		for i, pause := range gcStats.Pause {
			if gcStats.PauseEnd[i].Before(lastGcTime) {
				continue
			}
			if pause < 100*time.Microsecond {
				pause100us++
			} else if pause < 500*time.Microsecond {
				pause500us++
			} else if pause < time.Millisecond {
				pause1ms++
			} else if pause < 10*time.Millisecond {
				pause10ms++
			} else if pause < 50*time.Millisecond {
				pause50ms++
			} else if pause < 100*time.Millisecond {
				pause100ms++
			} else if pause < 500*time.Millisecond {
				pause500ms++
			} else if pause < time.Second {
				pause1s++
			} else {
				pause1sp++
			}
		}
		tmetrics.Gauge("trpc.PauseNsLt100usTimes").Set(float64(pause100us))
		tmetrics.Gauge("trpc.PauseNs100_500usTimes").Set(float64(pause500us))
		tmetrics.Gauge("trpc.PauseNs500us_1msTimes").Set(float64(pause1ms))
		tmetrics.Gauge("trpc.PauseNs1_10msTimes").Set(float64(pause10ms))
		tmetrics.Gauge("trpc.PauseNs10_50msTimes").Set(float64(pause50ms))
		tmetrics.Gauge("trpc.PauseNs50_100msTimes").Set(float64(pause100ms))
		tmetrics.Gauge("trpc.PauseNs100_500msTimes").Set(float64(pause500ms))
		tmetrics.Gauge("trpc.PauseNs500ms_1sTimes").Set(float64(pause1s))
		tmetrics.Gauge("trpc.PauseNsBt1sTimes").Set(float64(pause1sp))

		tmetrics.Gauge("trpc.PauseNs_us").Set(float64(pauseNs.Microseconds() / int64(currentGcNum-lastGcNum)))
		lastGcNum = currentGcNum
		lastGcPause = gcStats.PauseTotal
		lastGcTime = gcStats.LastGC
	}

	// 因为没有ReadMemStats强制flush分配情况， 所以可能不是最准确的情况， 存在漏掉小部分内存分配的情况
	// 但是去除了STW防止影响GC, 目前认为保证GC时间稳定更重要
	alloc := lookupOrZeroUint64(sampleMap, goMemoryClassesHeapObjectsBytes)
	tmetrics.Gauge("trpc.AllocMem_MB").Set(float64(alloc) / mebibyte)
	sys := lookupOrZeroUint64(sampleMap, goMemoryClassesTotalBytes)
	tmetrics.Gauge("trpc.SysMem_MB").Set(float64(sys) / mebibyte)
	nextGC := lookupOrZeroUint64(sampleMap, goGCHeapGoalBytes)
	tmetrics.Gauge("trpc.NextGCMem_MB").Set(float64(nextGC) / mebibyte)
	// 这个指标可能没有实际用途， 见 https://github.com/prometheus/client_golang/issues/842#issuecomment-861812034
	// 因此考虑go1.20之前该指标都设置为0, 1.20之后可以通过runtime/metrics计算
	// /cpu/classes/gc/total:cpu-seconds / /cpu/classes/total:cpu-seconds
	total := lookupOrZeroFloat64(sampleMap, goCPUClassesTotalSeconds)
	gc := lookupOrZeroFloat64(sampleMap, goCPUClassesGCTotalSeconds)
	if total != 0 {
		tmetrics.Gauge("trpc.GCCPUFraction_ppb").Set(gc / total)
	} else {
		tmetrics.Gauge("trpc.GCCPUFraction_ppb").Set(0.0)
	}
}

// lookupOrZeroUint64 find runtime/metrics Value as uint64
func lookupOrZeroUint64(rm map[string]*metrics.Sample, name string) uint64 {
	if s, ok := rm[name]; ok && s.Value.Kind() == metrics.KindUint64 {
		return s.Value.Uint64()
	}
	return 0
}

// lookupOrZeroFloat64 find runtime/metrics Value as float64
func lookupOrZeroFloat64(rm map[string]*metrics.Sample, name string) float64 {
	if s, ok := rm[name]; ok && s.Value.Kind() == metrics.KindFloat64 {
		return s.Value.Float64()
	}
	return 0
}
