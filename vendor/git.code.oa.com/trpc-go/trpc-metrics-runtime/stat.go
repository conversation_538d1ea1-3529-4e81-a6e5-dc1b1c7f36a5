package runtime

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/fs"
	"net"
	"net/http"
	"os"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
)

// Plugin
const (
	pluginType = "runtime"
	pluginName = "stat"

	// TKE environment
	tkeClusterIDEnvName    = "CLUSTER_ID"
	tkePodNamespaceEnvName = "POD_NAMESPACE"
	tkePodNameEnvName      = "POD_NAME"
	tkeNodeIPEnvName       = "NODE_IP"

	// 123-Platform environment
	p123ContainerNameEnvName = "SUMERU_CONTAINER_NAME"
)

var extraConf *Config

// InStatDomainName internal statistical domain name
var InStatDomainName = "stat.trpc.oa.com"

// OutStatDomainName public statistical domain name
var OutStatDomainName = "stat.trpc.tencent.com"

// ReportInterval is reporting interval
var ReportInterval = time.Hour * 24

// defaultIP is the default IP for reporting
var defaultIP = ""

// defaultContainer is the default container name for reporting
var defaultContainer = ""

func init() {
	// register plugin
	plugin.Register(pluginName, &Plugin{})

	// startup statistical data monitoring, and report it every 24 hours
	go StatReport(trpc.Version(), "")
}

// StatReport report statistics every 24 hours
func StatReport(version string, plug string) {

	time.Sleep(time.Second * 3)
	plugin.WaitForDone(time.Minute) // Wait for the framework startup to complete

	if GetExtraConf().Disable {
		return
	}

	// init default report settings
	defaultIP = getDefaultIP()
	defaultContainer = getDefaultContainer()

	for {
		err := Stat(InStatDomainName, version, plug)
		if err != nil {
			metrics.Counter("InStatReportFail").Incr()
			err = Stat(OutStatDomainName, version, plug)
			if err != nil {
				metrics.Counter("OutStatReportFail").Incr()
			}
		}

		time.Sleep(ReportInterval)
	}
}

type statInfo struct {
	App       string   `json:"app"`
	Server    string   `json:"server"`
	IP        string   `json:"ip"`
	Container string   `json:"container"`
	Lang      string   `json:"lang"`
	Version   string   `json:"version"`
	Plugin    string   `json:"plugin"`
	Git       string   `json:"git"`
	Owners    []string `json:"owners"`
}

// Stat data reporting API
func Stat(domain string, version string, plugin string) error {
	// http://stat.trpc.tencent.com/report?app=xx&server=xx&ip=**********&version=v0.1.0-rc.1&lang=go&container=xx
	stat := statInfo{
		App:       trpc.GlobalConfig().Server.App,
		Server:    trpc.GlobalConfig().Server.Server,
		IP:        trpc.GlobalConfig().Global.LocalIP,
		Container: trpc.GlobalConfig().Global.ContainerName,
		Git:       GetExtraConf().Git,
		Owners:    GetExtraConf().Owners,
		Lang:      "go",
		Version:   version,
		Plugin:    plugin,
	}
	// if the stat.IP is invalid, use the default IP
	if !isIPStrValid(stat.IP) {
		stat.IP = defaultIP
	}
	// if the stat.Container is empty, use default container name
	if len(stat.Container) == 0 {
		stat.Container = defaultContainer
	}
	statBytes, _ := json.Marshal(stat)
	rsp, err := http.Post(fmt.Sprintf("http://%s/api/trpc/stat/add", domain),
		"application/json", bytes.NewReader(statBytes))
	if err != nil {
		return err
	}
	rsp.Body.Close()

	return nil
}

// Config is a struct of configurations
type Config struct {
	Git    string   `yaml:"git"`    // git repo url of the project, not the repo of trpc framework
	Owners []string `yaml:"owners"` // a list of project owners

	Disable bool `json:"disable"` // disable plugin
}

// Plugin is a stat plugin
type Plugin struct{}

// Type returns the plugin type
func (m *Plugin) Type() string {
	return pluginType
}

// Setup initialize this plugin
func (m *Plugin) Setup(name string, configDesc plugin.Decoder) (err error) {
	var config Config
	if err = configDesc.Decode(&config); err != nil {
		return
	}
	extraConf = &config
	return nil
}

// GetExtraConf returns *Config
func GetExtraConf() *Config {
	if extraConf == nil {
		return &Config{}
	}
	return extraConf
}

type osFuncs struct {
	LookupEnv func(name string) (string, bool)
	Stat      func(name string) (fs.FileInfo, error)
}

var osImpl = osFuncs{
	LookupEnv: os.LookupEnv,
	Stat:      os.Stat,
}

// isIPStrValid check if a IP string is a valid IP
func isIPStrValid(ipStr string) bool {
	if len(ipStr) == 0 {
		return false
	}
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}
	return isIPValid(ip)
}

// isIPValid check if IP is valid
func isIPValid(ip net.IP) bool {
	if ip.IsUnspecified() {
		// ipv4 0.0.0.0/24, ipv6 ::
		return false
	}
	if ip.IsLoopback() {
		// ipv4 *********/8, ipv6 ::1
		return false
	}
	if ip.IsLinkLocalUnicast() {
		// ipv4 ***********/16, ipv6 FE80::/10
		return false
	}
	if ip4 := ip.To4(); ip4 != nil {
		if (ip4[0] == 172 && ip4[1]&0xf0 == 16) ||
			(ip4[0] == 192 && ip4[1] == 168) {
			// ipv4 **********/12
			// ipv4 ***********/16
			return false
		}
	} else if len(ip) == net.IPv6len {
		if ip[0]&0xfe == 0xfc {
			// ipv6 FC00::/7
			return false
		}
		if ip[0] == 0xfe && ip[1]&0xc0 == 0xc0 {
			// ipv6 FEC0::/10
			return false
		}
	} else {
		return false
	}
	return true
}

// isInDocker check if it's in a Docker environment
func isInDocker() bool {
	if _, err := osImpl.Stat("/.dockerenv"); os.IsNotExist(err) {
		return false
	}
	return true
}

// tryGetIPFromConfig returns the IP that was tried to get from the configuration
func tryGetIPFromConfig() string {
	// try to get IP from the global configuration
	if isIPStrValid(trpc.GlobalConfig().Global.LocalIP) {
		return trpc.GlobalConfig().Global.LocalIP
	}
	// try to get IP from the service configuration
	for _, service := range trpc.GlobalConfig().Server.Service {
		if isIPStrValid(service.IP) {
			return service.IP
		}
	}
	// try to get IP from NIC in each service
	for _, service := range trpc.GlobalConfig().Server.Service {
		ip := trpc.GetIP(service.Nic)
		if isIPStrValid(ip) {
			return ip
		}
	}
	return ""
}

// getDefaultIPSlow get first valid IP from network interfaces
func getDefaultIPSlow() string {
	interfaces, err := net.Interfaces()
	if err != nil {
		// get network interface list error
		return ""
	}
	var validIPv4, validIPv6 string
	for _, i := range interfaces {
		addrs, err := i.Addrs()
		if err != nil {
			continue
		}

		for _, v := range addrs {
			ipNet, ok := v.(*net.IPNet)
			if !ok {
				continue
			}
			if !isIPValid(ipNet.IP) {
				continue
			}
			if ipNet.IP.To4() != nil {
				if len(validIPv4) == 0 {
					validIPv4 = ipNet.IP.String()
				}
			} else if ipNet.IP.To16() != nil {
				if len(validIPv6) == 0 {
					validIPv6 = ipNet.IP.String()
				}
			}
		}
	}
	if len(validIPv4) > 0 {
		return validIPv4
	} else if len(validIPv6) > 0 {
		return validIPv6
	}
	return ""
}

// tryGetIPFromTKEENV try to get IP from TKE environment
func tryGetIPFromTKEENV() string {
	ip, ok := osImpl.LookupEnv(tkeNodeIPEnvName)
	if ok && isIPStrValid(ip) {
		return ip
	}
	return ""
}

// getDefaultIP get default IP for reporting
func getDefaultIP() string {
	if isInDocker() {
		// if it's a docker environment, try to get IP from environment variable
		ip := tryGetIPFromTKEENV()
		if len(ip) > 0 {
			return ip
		}
		return ""
	}

	ip := tryGetIPFromConfig()
	if len(ip) > 0 {
		return ip
	}
	return getDefaultIPSlow()
}

// getReplacedContainer get overlay IP as a container name
func getReplacedContainer() string {
	ip := tryGetIPFromConfig()
	if len(ip) > 0 {
		return ip
	}

	return getDefaultIPSlow()
}

// tryGetContainerFromTKEENV try to get container name from TKE environment variable
func tryGetContainerFromTKEENV() string {
	// get information from environment variable
	clusterID, ok := osImpl.LookupEnv(tkeClusterIDEnvName)
	if !ok {
		return ""
	}
	podNamespace, ok := osImpl.LookupEnv(tkePodNamespaceEnvName)
	if !ok {
		return ""
	}
	podName, ok := osImpl.LookupEnv(tkePodNameEnvName)
	if !ok {
		return ""
	}
	container := fmt.Sprintf("%s/%s/%s", clusterID, podNamespace, podName)
	return container
}

// tryGetContainerFrom123ENV try to get container name from 123-Platform environment variable
func tryGetContainerFrom123ENV() string {
	// get information from environment variable
	container, ok := osImpl.LookupEnv(p123ContainerNameEnvName)
	if !ok {
		return ""
	}
	return container
}

// getDefaultContainer try to get default container name for reporting
func getDefaultContainer() string {
	if !isInDocker() {
		// non-docker environment returns empty container name
		return ""
	}

	container := tryGetContainerFromTKEENV()
	if len(container) > 0 {
		return container
	}
	container = tryGetContainerFrom123ENV()
	if len(container) > 0 {
		return container
	}
	return getReplacedContainer()
}
