// Package runtime provides runtime metrics.
package runtime

import (
	"runtime"
	"runtime/pprof"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

func init() {
	// startup runtime monitor
	go func() {
		time.Sleep(time.Second * 3) // Waiting for the framework to finish starting up
		for {
			// report data at around the 30th second of each minute to avoid
			// the issue of 0 or 2 interval between minutes.
			time.Sleep((time.Duration)(90-time.Now().Second()) * time.Second)
			// if disable plugin, stop run metric
			if GetExtraConf().Disable {
				return
			}
			RuntimeMetrics()
		}
	}()
}

// getProfData get the metrics of goroutine, thread, CPU and etc.
func getProfData() {
	profiles := pprof.Profiles()
	for _, p := range profiles {
		switch p.Name() {
		case "goroutine":
			metrics.Gauge("trpc.GoroutineNum").Set(float64(p.Count()))
		case "threadcreate":
			metrics.Gauge("trpc.ThreadNum").Set(float64(p.Count()))
		default:
		}
	}
	metrics.Gauge("trpc.GOMAXPROCSNum").Set(float64(runtime.GOMAXPROCS(0)))
	metrics.Gauge("trpc.CPUCoreNum").Set(float64(runtime.NumCPU()))
}
