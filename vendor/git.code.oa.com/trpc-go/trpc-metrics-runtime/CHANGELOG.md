# Change Log

## [0.4.0](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.4.0) (2023-07-20)

### Performance Improvements

- 使用更高效的 runtime/metrics 代替 runtime.ReadMemStats 获取相关指标，减少了服务的延迟 (!51)

### Bug Fixes

- 修复 getMemStats 在计算 trpc.PauseNsXXX 指标时多次上报的问题，实际上只需要在计算完上报一次(!51)

## [0.3.6](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.3.6) (2023-06-14)

### Bug Fixes

- 修复计算 "trpc.PauseNs_us" 指标时发生的”除零错“(!49)

## [0.3.5](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.3.5) (2023-06-12) 

### Bug Fixes

- 去除 go build tag 前不该有的空格 (!48)

## [0.3.4](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.3.4) (2023-06-12) 

### Features

- 代码注释/介绍英文化 (!42)
- doc: code.oa => woa (!41)
- 添加重复指标名以适应智研及 Prometheus 命名规范 (!40)
- 添加 stream 代码扫描 (!35)
- 自动获取 IP 和 container 信息用于上报 (!34)

### Bug Fixes

- 修复 gc 时延是全量上报的问题，对齐伽利略 (!45)

## [0.3.3](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.3.3) (2022-04-07) 

### Bug Fixes
- fix 插件未配置extraConf初始化问题

## [0.3.2](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.3.2) (2022-04-06) 

### Features
- 添加插件功能开关


## [0.2.3](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.2.3) (2021-07-21) 

### Features
- 因运营版本上报需要，在trpc-metrics-runtime 增加git和owners字段的上报

## [0.2.2](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.2.2) (2021-01-11) 

### Bug Fixes
- 获取磁盘指标，openbsd与其他unix系统分别处理


## [0.2.1](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.7) (2020-11-24)

### Features
- 支持windows平台编译，暂不支持fd\pid等数据

## [0.2.0](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.7) (2020-07-28)

### Features
- runtime 修改代码风格, 修改json生成方式

## [0.1.7](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.7) (2020-06-22)

### Doc
- 添加指标说明
- 框架上报的属性名加上trpc.前缀，方便和业务上报的属性名区分

## [0.1.6](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.6) (2020-05-07)

### Bug Fixes
- 等待框架启动完成才上报，解决data race问题

## [0.1.5](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.5) (2020-03-23)

### Features
- 属性名全部改成英文

## [0.1.4](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.4) (2020-03-18)

### Features
- 添加服务启动监控

## [0.1.3](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.3) (2020-03-02)

### Features
- 添加数据统计api

## [0.1.2](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.2) (2020-02-14)

### Bug Fixes
- 修复windows编译失败问题

## [0.1.1](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.1) (2020-01-17)

### Bug Fixes
- 修复上报url

## [0.1.0](https://git.woa.com/trpc-go/trpc-metrics-runtime/tree/v0.1.0) (2020-01-12)

### Features
- 支持上报runtime监控
- 支持上报框架统计监控
