// Code generated by MockGen. DO NOT EDIT.
// Source: forwarder_client.go

// Package oidb is a generated GoMock package.
package oidb

import (
	context "context"
	reflect "reflect"

	client "git.code.oa.com/trpc-go/trpc-go/client"
	gomock "github.com/golang/mock/gomock"
)

// MockForwarderClient is a mock of ForwarderClient interface.
type MockForwarderClient struct {
	ctrl     *gomock.Controller
	recorder *MockForwarderClientMockRecorder
}

// MockForwarderClientMockRecorder is the mock recorder for MockForwarderClient.
type MockForwarderClientMockRecorder struct {
	mock *MockForwarderClient
}

// NewMockForwarderClient creates a new mock instance.
func NewMockForwarderClient(ctrl *gomock.Controller) *MockForwarderClient {
	mock := &MockForwarderClient{ctrl: ctrl}
	mock.recorder = &MockForwarderClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockForwarderClient) EXPECT() *MockForwarderClientMockRecorder {
	return m.recorder
}

// DoWithIP mocks base method.
func (m *MockForwarderClient) DoWithIP(ctx context.Context, head *OIDBHead, reqbody, rspbody interface{}, opts ...client.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, head, reqbody, rspbody}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoWithIP", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoWithIP indicates an expected call of DoWithIP.
func (mr *MockForwarderClientMockRecorder) DoWithIP(ctx, head, reqbody, rspbody interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, head, reqbody, rspbody}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoWithIP", reflect.TypeOf((*MockForwarderClient)(nil).DoWithIP), varargs...)
}

// DoWithKnocknock mocks base method.
func (m *MockForwarderClient) DoWithKnocknock(ctx context.Context, head *OIDBHead, reqbody, rspbody interface{}, opts ...client.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, head, reqbody, rspbody}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoWithKnocknock", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoWithKnocknock indicates an expected call of DoWithKnocknock.
func (mr *MockForwarderClientMockRecorder) DoWithKnocknock(ctx, head, reqbody, rspbody interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, head, reqbody, rspbody}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoWithKnocknock", reflect.TypeOf((*MockForwarderClient)(nil).DoWithKnocknock), varargs...)
}
