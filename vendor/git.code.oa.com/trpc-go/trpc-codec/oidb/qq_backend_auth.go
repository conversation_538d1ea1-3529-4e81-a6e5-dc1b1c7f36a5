package oidb

import (
	"net"
	sync "sync"

	authsdkgo "git.woa.com/qqlogin/authentication/auth-sdk-go"
)

var (
	o sync.Once
)

const (
	calleeName = "oidb"
	signMethod = "HMAC-MD5"
)

// InitQQBackendAuthenticator 初始化QQ后台鉴权功能，多次调用仅初始化一次
//
// CmdbID为当前模块的CMDB ID，authKey为qqdata平台生成
func InitQQBackendAuthenticator(cmdbID, authKey string) error {
	var err error
	o.Do(func() {
		authKeyCfg := &authsdkgo.Config{
			Name: cmdbID,
			Callees: []*authsdkgo.Callee{
				{
					Name:       calleeName,
					Enable:     true,
					VerifyIP:   true, //oidb服务端会校验ip，这里一定要设置为true
					SignMethod: signMethod,
					// 模块签名key，在qqdata上生成并获取到，配置在七彩石即可
					// 注意~！！！此模块签名key仅用于模块内服务使用，切勿泄露给其他模块使用。也不要多模块混用。一旦监控发现有滥用情况，oidb会将此key下线
					AuthKey: authKey,
				},
			},
		}
		//指定配置初始化鉴权器，使用once，初始化一次就好了
		err = authsdkgo.SetupAuthenticatorWithConfig(authKeyCfg)
		if err != nil {
			return
		}
	})
	return err
}

// AddQQBackendAuthInfo 添加QQ后台鉴权信息到OIDB头中，使用eth1网络接口ip地址计算签名
func AddQQBackendAuthInfo(oidbHead *OIDBHead) (*OIDBHead, error) {
	// 没有指定ip，使用eth1网卡生成模块签名内容
	authInfo, err := authsdkgo.GenAuthInfoByEth1(calleeName)
	if err != nil {
		return nil, err
	}
	oidbHead.BytesAuthInfo = authInfo
	return oidbHead, nil
}

// AddQQBackendAuthInfoByIP 添加QQ后台鉴权信息到OIDB头中，使用传入的ip地址计算签名
func AddQQBackendAuthInfoByIP(oidbHead *OIDBHead, authIP net.IP) (*OIDBHead, error) {
	// 生成模块签名内容，如果你的应用绑定的网卡不是eth1，则使用authsdkgo.GenAuthInfo，自行指定ip
	authInfo, err := authsdkgo.GenAuthInfo(calleeName, authIP)
	if err != nil {
		return nil, err
	}
	oidbHead.BytesAuthInfo = authInfo
	return oidbHead, nil
}
