package oidb

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-codec/oidb/entity"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"github.com/golang/protobuf/proto"
)

//go:generate mockgen -source=trpc_oidb_client.go -destination=trpc_oidb_client_mock.go -package=oidb OIDBClient

// OIDBClient oidb客户端接口
type OIDBClient interface {
	DoWithKnocknock(ctx context.Context,
		head *OIDBHead,
		reqbody interface{},
		rspbody interface{},
		opts ...client.Option) error
	DoWithIP(ctx context.Context,
		head *OIDBHead,
		reqbody interface{},
		rspbody interface{},
		opts ...client.Option) error
}

// DoWithKnocknock 通过oidb transporter调用oidb平台(knocknock鉴权)
// 发起oidb后端请求, 每次调用后端需要自己new一个新的head, 里面至少包括 uin command servicetype
// 参考:http://km.oa.com/group/38650/articles/show/445997
// 注意:OIDB采用北极星寻址。
// 北极星服务名：trpc.oicq.oidb.Transporter。
// 命名空间：Production&Development。
// 目前仅支持udp，不支持tcp。
// 已废弃，使用DoWithKnocknockNew来代替
func DoWithKnocknock(ctx context.Context,
	head *OIDBHead,
	reqBody, rspBody interface{},
	opts ...client.Option) error {
	return DoWithKnocknockNew(ctx, head, reqBody, rspBody, entity.Transporter, opts...)
}

// DoWithIP 通过oidb transporter调用oidb平台(ip鉴权)
// 发起oidb后端请求, 每次调用后端需要自己new一个新的head, 里面至少包括 uin command servicetype
// 参考:https://km.woa.com/group/38650/articles/show/461450
// 注意:OIDB采用北极星寻址。
// 北极星服务名：trpc.oicq.oidb.IpTrans
// 命名空间：Production&Development。
// 目前仅支持udp，不支持tcp。
// 已废弃，使用DoWithIPNew来代替
func DoWithIP(ctx context.Context,
	head *OIDBHead,
	reqBody, rspBody proto.Message,
	opts ...client.Option) error {
	return DoWithIPNew(ctx, head, reqBody, rspBody, entity.Transporter, opts...)
}

// DoWithKnocknockNew 通过传入的网关类型调用oidb平台(knocknock鉴权)
// 发起oidb后端请求, 每次调用后端需要自己new一个新的head, 里面至少包括 uin command servicetype
// 参考:http://km.oa.com/group/38650/articles/show/445997
// 注意:OIDB采用北极星寻址。
// 北极星服务名：trpc.oicq.oidb.Transporter或trpc.oicq.oidb.Forwarder。
// 命名空间：Production&Development。
// Forwarder支持tcp&udp，Transporter只支持udp
func DoWithKnocknockNew(ctx context.Context,
	head *OIDBHead,
	reqBody, rspBody interface{},
	backendType entity.OIDBGatewayType,
	opts ...client.Option) error {
	var c OIDBClient
	switch backendType {
	case entity.Transporter:
		c = NewTransporterClientProxy()
	case entity.Forwarder:
		c = NewForwarderClientProxy()
	default:
		return errors.New("not support backendType")
	}
	return c.DoWithKnocknock(ctx, head, reqBody, rspBody, opts...)
}

// DoWithQQBackendAuth 通过传入的网关类型调用oidb平台(QQ后台统一鉴权)
//
// 调用此方法之前，请确保已经调用过本包下的InitQQBackendAuthenticator方法初始化QQ后台鉴权器。
//
// 发起oidb后端请求, 每次调用后端需要自己new一个新的head, 里面至少包括 uin command servicetype
//
// 参考:http://km.oa.com/group/38650/articles/show/445997
//
// 注意:OIDB采用北极星寻址。
//
// 北极星服务名：trpc.oicq.oidb.IpTrans/trpc.oicq.oidb.IpFwd
//
// 命名空间：Production/Development。
//
// 支持tcp&udp
func DoWithQQBackendAuth(ctx context.Context,
	head *OIDBHead,
	reqBody, rspBody interface{},
	backendType entity.OIDBGatewayType,
	opts ...client.Option) error {
	// 对oidb头进行修饰，添加auth_info字段信息
	if len(head.GetBytesAuthInfo()) == 0 {
		var err error
		head, err = AddQQBackendAuthInfo(head)
		if err != nil {
			return fmt.Errorf("generate qq backend auth_info failed with err %+v", err)
		}
	}
	return DoWithIPNew(ctx, head, reqBody, rspBody, backendType, opts...)
}

// DoWithIPNew 通过传入的网关类型调用oidb平台(ip鉴权)
// 发起oidb后端请求, 每次调用后端需要自己new一个新的head, 里面至少包括 uin command servicetype
// 参考:https://km.woa.com/group/38650/articles/show/461450
// 注意:OIDB采用北极星寻址。
// 北极星服务名：trpc.oicq.oidb.IpTrans/trpc.oicq.oidb.IpFwd
// 命名空间：Production&Development。
// Forwarder支持tcp&udp，Transporter只支持udp
func DoWithIPNew(ctx context.Context,
	head *OIDBHead,
	reqBody, rspBody interface{},
	backendType entity.OIDBGatewayType,
	opts ...client.Option) error {
	var c OIDBClient
	switch backendType {
	case entity.Transporter:
		c = NewTransporterClientProxy()
	case entity.Forwarder:
		c = NewForwarderClientProxy()
	default:
		return errors.New("not support backendType")
	}
	return c.DoWithIP(ctx, head, reqBody, rspBody, opts...)
}
