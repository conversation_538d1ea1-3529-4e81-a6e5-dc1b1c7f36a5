package oidb

import (
	"context"
	"errors"
	"os"
	"path"

	"github.com/golang/protobuf/proto"
	protoV2 "google.golang.org/protobuf/proto"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

//go:generate mockgen -source=forwarder_client.go -destination=forwarder_client_mock.go -package=oidb ForwarderClient

// ForwarderClient trpc.oicq.oidb.Forwarder接口
type ForwarderClient interface {
	DoWithKnocknock(ctx context.Context,
		head *OIDBHead,
		reqbody interface{},
		rspbody interface{},
		opts ...client.Option) error
	DoWithIP(ctx context.Context,
		head *OIDBHead,
		reqbody interface{},
		rspbody interface{},
		opts ...client.Option) error
}

// oidbForwarderCli 封装使用Knocknock鉴权方式和使用ip模块鉴权方式调用oidb的结构体
type oidbForwarderCli struct {
	cli client.Client
}

type oidbAuthType int

const (
	knocknockAuth oidbAuthType = 0
	ipAuth        oidbAuthType = 1
)

// NewForwarderClientProxy 新建一个oidbForwarderCli代理
var NewForwarderClientProxy = func() ForwarderClient {
	c := &oidbForwarderCli{
		cli: client.DefaultClient,
	}
	return c
}

// DoWithKnocknock 发起oidb后端请求, 每次调用后端需要自己new一个新的head,
// 里面至少包括 uin command servicetype
func (c *oidbForwarderCli) DoWithKnocknock(ctx context.Context,
	head *OIDBHead,
	reqBody interface{},
	rspBody interface{},
	opts ...client.Option) error {

	params := oidbCallParams{
		Head: head,
		Req:  reqBody,
		Rsp:  rspBody,
	}
	return c.doWithForwarder(ctx, params, knocknockAuth, opts...)
}

// DoWithIP 发起oidb后端请求, 每次调用后端需要自己new一个新的head,
// 里面至少包括 uin command servicetype
func (c *oidbForwarderCli) DoWithIP(ctx context.Context,
	head *OIDBHead,
	reqBody interface{},
	rspBody interface{},
	opts ...client.Option) error {

	params := oidbCallParams{
		Head: head,
		Req:  reqBody,
		Rsp:  rspBody,
	}
	return c.doWithForwarder(ctx, params, ipAuth, opts...)
}

func (c *oidbForwarderCli) doWithForwarder(ctx context.Context,
	params oidbCallParams,
	authType oidbAuthType,
	opts ...client.Option) error {
	head := params.Head
	head.Uint32Result = proto.Uint32(0) // 重置错误码
	if len(head.GetStrServiceName()) == 0 {
		head.StrServiceName = proto.String(path.Base(os.Args[0]))
	}

	// 序列化包头,填充到MetaData中,并接收回包的透传数据
	byteHead, err := proto.Marshal(head)
	if err != nil {
		return errs.NewFrameError(errs.RetClientEncodeFail, err.Error())
	}

	trpcRspHead := &trpc.ResponseProtocol{}
	opt := []client.Option{
		client.WithMetaData(transKeyOIDBPkgType, []byte(transValueOIDBPkgTypeV2PB)),
		client.WithMetaData(transKeyOIDBHead, byteHead),
		client.WithRspHead(trpcRspHead),
	}

	opts = append(opt, opts...)

	// 这一段代码是从oidb_forwarder的桩代码中复制过来的
	// 因为oidb_forwarder并不是一个rpc服务，它完全不解析包体，而是透传
	// 因此我们也不能使用桩代码里面定义的rpc接口，而是需要直接Invoke发起网络请求
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithCalleeApp("oicq")
	msg.WithCalleeServer("oidb")
	msg.WithCalleeService("Forwarder")
	msg.WithCalleeMethod("HandleProcess")
	switch authType {
	case knocknockAuth:
		msg.WithClientRPCName("/trpc.oicq.oidb.Forwarder/HandleProcess")
		msg.WithCalleeServiceName("trpc.oicq.oidb.Forwarder")
	case ipAuth:
		msg.WithClientRPCName("/trpc.oicq.oidb.IpFwd/HandleProcess")
		msg.WithCalleeServiceName("trpc.oicq.oidb.IpFwd")
	default:
		return errors.New("invalid oidb auth type")
	}
	// 复制end

	req := params.Req
	rsp := params.Rsp
	switch req.(type) {
	// 只有pb2，pb3，二进制是oidb包体的合法类型
	case proto.Message, protoV2.Message:
		msg.WithSerializationType(codec.SerializationTypePB)
		opts = append(opts, client.WithCurrentSerializationType(codec.SerializationTypePB))
	case *codec.Body:
		msg.WithSerializationType(codec.SerializationTypeNoop)
		opts = append(opts, client.WithCurrentSerializationType(codec.SerializationTypeNoop))
	default:
		return errs.NewFrameError(errs.RetClientEncodeFail, "request not pb v2/pb v3/codec.Body pointer type")
	}

	// 发起oidb后端请求
	if err := c.cli.Invoke(ctx, req, rsp, opts...); err != nil {
		return err
	}

	// 反序列化包头
	byteRspHead, ok := trpcRspHead.GetTransInfo()[transKeyOIDBHead]
	if !ok {
		return errs.NewFrameError(errs.RetClientValidateFail, "no oidb head in rsp trans info")
	}

	// 直接使用输入参数head来反序列化回包head
	if err := proto.Unmarshal(byteRspHead, head); err != nil {
		return errs.NewFrameError(errs.RetClientDecodeFail, err.Error())
	}

	// 检查oidb返回码
	if head.GetUint32Result() != 0 {
		return errs.New(int(head.GetUint32Result()), head.GetStrErrorMsg())
	}

	return nil
}
