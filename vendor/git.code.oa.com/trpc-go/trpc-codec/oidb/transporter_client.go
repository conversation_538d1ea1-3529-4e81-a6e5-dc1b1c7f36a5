package oidb

import (
	"context"
	"errors"
	"os"
	"path"

	"github.com/golang/protobuf/proto"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/oicq/oidb_transporter"
)

//go:generate mockgen -source=transporter_client.go -destination=transporter_client_mock.go -package=oidb TransporterClient

// TransporterClient trpc.oicq.oidb.Transporter接口
type TransporterClient interface {
	DoWithKnocknock(ctx context.Context,
		head *OIDBHead,
		reqbody interface{},
		rspbody interface{},
		opts ...client.Option) error
	DoWithIP(ctx context.Context,
		head *OIDBHead,
		reqbody interface{},
		rspbody interface{},
		opts ...client.Option) error
}

// oidbTransporterCli 封装使用Knocknock鉴权方式和使用ip模块鉴权方式调用oidb的结构体
type oidbTransporterCli struct {
	TransportClientProxy oidb_transporter.TransporterClientProxy
	IPTransClientProxy   oidb_transporter.IpTransClientProxy
}

// NewTransporterClientProxy 新建一个oidbTransporterCli代理
var NewTransporterClientProxy = func() TransporterClient {
	c := &oidbTransporterCli{
		TransportClientProxy: oidb_transporter.NewTransporterClientProxy(),
		IPTransClientProxy:   oidb_transporter.NewIpTransClientProxy(),
	}
	return c
}

// DoWithKnocknock 发起oidb后端请求, 每次调用后端需要自己new一个新的head,
// 里面至少包括 uin command servicetype
func (c *oidbTransporterCli) DoWithKnocknock(ctx context.Context,
	head *OIDBHead,
	reqBody interface{},
	rspBody interface{},
	opts ...client.Option) error {

	params := oidbCallParams{
		Head: head,
		Req:  reqBody,
		Rsp:  rspBody,
	}
	return c.doWithTransporter(ctx, c.TransportClientProxy, params, opts...)
}

// DoWithIP 发起oidb后端请求, 每次调用后端需要自己new一个新的head,
// 里面至少包括 uin command servicetype
func (c *oidbTransporterCli) DoWithIP(ctx context.Context,
	head *OIDBHead,
	reqBody interface{},
	rspBody interface{},
	opts ...client.Option) error {

	params := oidbCallParams{
		Head: head,
		Req:  reqBody,
		Rsp:  rspBody,
	}
	return c.doWithTransporter(ctx, c.IPTransClientProxy, params, opts...)
}

// transCodec converts between its data and encoded binary data/decoded pb data or raw data.
// 这个 interface 更应该设计为不对 receiver 内 data 操作的，用入参做输入用返回值做输出的形式，
// 至于不同 implements 的交互对象类型不同的问题，在放弃老版本 go 后应该使用 generic 解决。
// type payload interface{ proto.Message | *codec.Body }
// 用 interface{} 存在类型丢失放任误用的问题，不是很想用。
type transCodec interface {
	Marshal() ([]byte, error)
	Unmarshal([]byte) error
}

var newTransCode = func(params oidbCallParams) (transCodec, error) {
	switch val := params.Req.(type) {
	case proto.Message:
		rsp, ok := params.Rsp.(proto.Message)
		if !ok {
			return nil, errors.New("rsp is not pb message")
		}
		return &pbCodec{req: val, rsp: rsp}, nil
	case *codec.Body:
		if val == nil {
			return nil, errors.New("request body is nil")
		}
		rsp, ok := params.Rsp.(*codec.Body)
		if !ok {
			return nil, errors.New("input rsp isnot codec.Body")
		}
		if rsp == nil {
			return nil, errors.New("input rsp is nil")
		}
		return &rawCodec{req: val, rsp: rsp}, nil
	default:
		return nil, errors.New(
			"request type is unsupported, use type proto.Message (for pb) or *codec.Body (for binary)")
	}
}

// pbCodec implements transCodec that suitable for oidb v2 pb form.
type pbCodec struct {
	req proto.Message
	rsp proto.Message
}

// rawCodec implements transCodec that suitable for oidb v1 binary form.
type rawCodec struct {
	req *codec.Body
	rsp *codec.Body
}

func (p *pbCodec) Marshal() ([]byte, error) {
	data, err := proto.Marshal(p.req)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (p *pbCodec) Unmarshal(raw []byte) error {
	if p.rsp == nil {
		return errors.New("response is nil")
	}
	return proto.Unmarshal(raw, p.rsp)
}

func (r *rawCodec) Marshal() ([]byte, error) {
	if r.req == nil {
		return nil, errors.New("request is nil")
	}
	return r.req.Data, nil
}

func (r *rawCodec) Unmarshal(raw []byte) error {
	if r.rsp == nil {
		return errors.New("response is nil")
	}
	r.rsp.Data = raw
	return nil
}

func (c *oidbTransporterCli) doWithTransporter(ctx context.Context,
	proxy oidbClientProxy,
	params oidbCallParams,
	opts ...client.Option) error {
	head := params.Head
	coder, err := newTransCode(params)
	if err != nil {
		return err
	}
	head.Uint32Result = proto.Uint32(0) // 重置错误码
	if len(head.GetStrServiceName()) == 0 {
		head.StrServiceName = proto.String(path.Base(os.Args[0]))
	}

	// 序列化包头,填充到MetaData中,并接收回包的透传数据
	byteHead, err := proto.Marshal(head)
	if err != nil {
		return errs.NewFrameError(errs.RetClientEncodeFail, err.Error())
	}

	trpcRspHead := &trpc.ResponseProtocol{}
	opt := []client.Option{
		client.WithMetaData(transKeyOIDBPkgType, []byte(transValueOIDBPkgTypeV2PB)),
		client.WithMetaData(transKeyOIDBHead, byteHead),
		client.WithRspHead(trpcRspHead),
		client.WithNetwork("udp"),
	}

	opts = append(opt, opts...)

	// 序列化请求包
	byteReq, err := coder.Marshal()
	if err != nil {
		return errs.NewFrameError(errs.RetClientEncodeFail, err.Error())
	}

	req := &oidb_transporter.MsgRequest{
		Msg: byteReq,
	}
	// 发起oidb后端请求
	rsp, err := proxy.HandleProcess(ctx, req, opts...)
	if err != nil {
		return err
	}

	// 反序列化包头
	byteRspHead, ok := trpcRspHead.GetTransInfo()[transKeyOIDBHead]
	if !ok {
		return errs.NewFrameError(errs.RetClientValidateFail, "no oidb head in rsp trans info")
	}

	// 直接使用输入参数head来反序列化回包head
	if err := proto.Unmarshal(byteRspHead, head); err != nil {
		return errs.NewFrameError(errs.RetClientDecodeFail, err.Error())
	}

	// 检查oidb返回码
	if head.GetUint32Result() != 0 {
		return errs.New(int(head.GetUint32Result()), head.GetStrErrorMsg())
	}

	// 反序列化应答包
	if err := coder.Unmarshal(rsp.GetMsg()); err != nil {
		return errs.NewFrameError(errs.RetClientDecodeFail, err.Error())
	}

	return nil
}

const (
	transKeyOIDBPkgType       = "oidb_pkg_type"
	transKeyOIDBHead          = "oidb_head"
	transValueOIDBPkgTypeV2PB = "v2_pb"
)

type oidbClientProxy interface {
	HandleProcess(ctx context.Context, req *oidb_transporter.MsgRequest,
		opts ...client.Option) (rsp *oidb_transporter.MsgReply, err error)
}

type oidbCallParams struct {
	Head *OIDBHead
	Req  interface{}
	Rsp  interface{}
}
