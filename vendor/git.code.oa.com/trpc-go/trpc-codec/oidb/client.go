package oidb

import (
	"context"
	"fmt"
	"os"
	"path"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"github.com/golang/protobuf/proto"
)

// Client 接口
type Client interface {
	Do(ctx context.Context,
		head *OIDBHead,
		reqbody proto.Message,
		rspbody proto.Message,
		opts ...client.Option) error
}

// oidbCli oidb Client
type oidbCli struct {
	Client client.Client
	opts   []client.Option
}

// NewClientProxy 新建一个oidbCli代理
func NewClientProxy(opts ...client.Option) Client {
	c := &oidbCli{
		Client: client.DefaultClient,
		opts: []client.Option{
			client.WithProtocol("oidb"),
			client.WithNetwork("udp"),
		},
	}
	c.opts = append(c.opts, opts...)
	return c
}

// Do 发起oidb后端请求, 每次调用后端需要自己new一个新的head,
// 里面至少包括 uin command servicetype
func (c *oidbCli) Do(ctx context.Context,
	head *OIDBHead,
	reqbody proto.Message,
	rspbody proto.Message,
	opts ...client.Option) error {

	cmd := head.GetUint32Command()
	serviceType := head.GetUint32ServiceType()
	head.Uint32Result = proto.Uint32(0) // 重置错误码
	if len(head.GetStrServiceName()) == 0 {
		head.StrServiceName = proto.String(path.Base(os.Args[0]))
	}

	ctx, msg := codec.WithCloneMessage(ctx)
	msg.WithClientReqHead(head)
	msg.WithClientRspHead(head)
	msg.WithClientRPCName(fmt.Sprintf("/0x%x/%d", cmd, serviceType))
	msg.WithCalleeServiceName(fmt.Sprintf("trpc.oidb.cmd0x%x.downservice", cmd))
	msg.WithSerializationType(codec.SerializationTypePB)

	varopts := append(c.opts, opts...)

	return c.Client.Invoke(ctx, reqbody, rspbody, varopts...)
}

// Do 发起oidb后端请求, 每次调用后端需要自己new一个新的head,
// 里面至少包括 uin command servicetype
func Do(ctx context.Context,
	head *OIDBHead,
	reqbody proto.Message,
	rspbody proto.Message,
	opts ...client.Option) error {
	c := NewClientProxy(opts...)
	return c.Do(ctx, head, reqbody, rspbody)
}

// CopyHead 拷贝一个Head
func CopyHead(head *OIDBHead) *OIDBHead {
	h := proto.Clone(head)
	return h.(*OIDBHead)
}
