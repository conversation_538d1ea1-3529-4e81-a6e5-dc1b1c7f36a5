package oidb

import (
	"bytes"
	"context"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"os"
	"path"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/transport"
	"google.golang.org/protobuf/proto"
)

func init() {
	codec.Register("oidb", DefaultServerCodec, DefaultClientCodec)
	transport.RegisterFramerBuilder("oidb", DefaultFrameBuilder)
}

// default codec
var (
	DefaultServerCodec  = &ServerCodec{}
	DefaultClientCodec  = &ClientCodec{}
	DefaultFrameBuilder = &FramerBuilder{}
)

// Head 从ctx获取oidb head
func Head(ctx context.Context) *OIDBHead {

	msg := codec.Message(ctx)
	head, ok := msg.ServerReqHead().(*OIDBHead)
	if !ok {
		return &OIDBHead{}
	}

	return head
}

const (
	stx     = uint8(0x28) // 起始魔数
	etx     = uint8(0x29) // 终止魔数
	headLen = uint32(9)   // 1 bytes stx + 4 bytes oidb header length + 4 bytes pb body length
)

// MaxFrameSize 最大数据帧大小
var MaxFrameSize uint32 = 64 * 1024 // 64k

// FramerBuilder 数据帧构造器
type FramerBuilder struct {
}

// New 生成一个数据帧
func (fb *FramerBuilder) New(reader io.Reader) transport.Framer {
	return &framer{
		reader: reader,
	}
}

type framer struct {
	reader io.Reader
	head   [9]byte
}

// ReadFrame 从io reader拆分出完整数据桢
// 格式：( + 4 byte header len + 4 byte body len + head data + body data + )
func (f *framer) ReadFrame() (msgbuf []byte, err error) {

	num, err := io.ReadFull(f.reader, f.head[:])
	if err != nil {
		return nil, err
	}
	if num != int(headLen) {
		return nil, errors.New("read frame header num invalid")
	}

	headerLen := binary.BigEndian.Uint32(f.head[1:5])
	bodyLen := binary.BigEndian.Uint32(f.head[5:])
	if f.head[0] != stx {
		return nil, errors.New("read msg header stx invalid")
	}
	if headerLen == 0 {
		return nil, errors.New("read msg header empty")
	}

	totalLen := headLen + headerLen + bodyLen + 1
	if totalLen > MaxFrameSize {
		return nil, errors.New("frame size invalid")
	}

	msg := make([]byte, totalLen)
	copy(msg, f.head[:])

	num, err = io.ReadFull(f.reader, msg[headLen:totalLen])
	if err != nil {
		return nil, err
	}
	if num != int(headerLen+bodyLen+1) {
		return nil, errors.New("read msg body num invalid")
	}

	if msg[headLen+headerLen+bodyLen] != etx {
		return nil, errors.New("read msg header etx invalid")
	}
	return msg, nil
}

// IsSafe implements codec.SafeFramer.
func (f *framer) IsSafe() bool {
	return true
}

// ServerCodec 服务端编解码
type ServerCodec struct {
}

// Decode 服务端收到客户端二进制请求数据解包到reqbody结构体
func (s *ServerCodec) Decode(msg codec.Msg, reqbuf []byte) (reqbody []byte, err error) {

	if len(reqbuf) < int(headLen) {
		return nil, errors.New("server decode req buf len invalid")
	}

	headerLen := binary.BigEndian.Uint32(reqbuf[1:5])
	bodyLen := binary.BigEndian.Uint32(reqbuf[5:9])

	if int(headLen+headerLen+bodyLen+1) != len(reqbuf) {
		return nil, errors.New("server decode req buf len invalid")
	}

	head := &OIDBHead{}

	if err := proto.Unmarshal(reqbuf[headLen:headLen+headerLen], head); err != nil {
		return nil, err
	}

	msg.WithServerReqHead(head)
	msg.WithServerRspHead(head)
	msg.WithSerializationType(codec.SerializationTypePB)
	msg.WithServerRPCName(fmt.Sprintf("/0x%x/%d", head.GetUint32Command(), head.GetUint32ServiceType()))

	// 需要分别设置 caller callee 的 app server service method，但是oidb协议只有下游的命令字，所以先只设置server method
	msg.WithCalleeMethod(msg.ServerRPCName())
	msg.WithCalleeServiceName(fmt.Sprintf("trpc.oidb.%s.%s",
		fmt.Sprintf("cmd0x%x", head.GetUint32Command()), path.Base(os.Args[0])))

	msg.WithCallerServiceName(fmt.Sprintf("trpc.oidb.upserver.%s",
		fmt.Sprintf("%s", head.GetStrServiceName())))
	msg.WithCallerServer(msg.CallerService()) // oidb协议无法知道上游服务的命令字,以service代替

	metadata := make(map[string][]byte)

	metadata["oidb_head"] = reqbuf[headLen : headLen+headerLen]
	metadata["uin"] = []byte(fmt.Sprintf("%d", head.GetUint64Uin()))

	msg.WithServerMetaData(metadata)

	return reqbuf[headLen+headerLen : headLen+headerLen+bodyLen], nil
}

// Encode 服务端打包rspbody结构体到二进制 回给客户端
func (s *ServerCodec) Encode(msg codec.Msg, rspbody []byte) (rspbuf []byte, err error) {

	head, ok := msg.ServerRspHead().(*OIDBHead)
	if !ok {
		head = &OIDBHead{}
	}

	// 将处理函数handler返回的error转成协议包头里面的错误码字段
	if e := msg.ServerRspErr(); e != nil {
		head.StrErrorMsg = proto.String(e.Msg)
		if e.Type == errs.ErrorTypeBusiness {
			head.Uint32Result = proto.Uint32(uint32(e.Code))
		} else {
			head.Uint32Result = proto.Uint32(100000 + uint32(e.Code))
		}
	}

	h, err := proto.Marshal(head)
	if err != nil {
		return nil, err
	}

	buf := bytes.NewBuffer(make([]byte, 0, int(headLen)+len(h)+len(rspbody)+1))
	if err = binary.Write(buf, binary.BigEndian, stx); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, uint32(len(h))); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, uint32(len(rspbody))); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, h); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, rspbody); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, etx); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// ClientCodec 客户端编解码
type ClientCodec struct {
	Seq uint32
}

// Encode 客户端打包reqbody结构体到二进制数据 发到服务端
func (c *ClientCodec) Encode(msg codec.Msg, reqbody []byte) (reqbuf []byte, err error) {

	// 构造后端请求包头
	var head *OIDBHead
	if msg.ClientReqHead() != nil {
		// client req head不为空 说明是用户首次创建，直接使用即可
		request, ok := msg.ClientReqHead().(*OIDBHead)
		if !ok {
			return nil, errors.New("client encode req head not OIDBHead")
		}
		head = request
	} else {
		// client req head为空，需要复制server req head, clone防止并发问题
		request, ok := msg.ServerReqHead().(*OIDBHead)
		if ok {
			head = proto.Clone(request).(*OIDBHead)
		} else {
			head = &OIDBHead{}
		}
		// 保存新的client req head
		msg.WithClientReqHead(head)
	}

	// 框架自动生成全局唯一seq
	if head.GetUint32Seq() == 0 {
		head.Uint32Seq = proto.Uint32(atomic.AddUint32(&c.Seq, 1))
	}

	var cmd, serviceType uint32
	rpcname := msg.ClientRPCName() // rpcname /0x97d/1
	if rpcname == "" {
		return nil, errors.New("client encode rpcname empty")
	}
	_, _ = fmt.Sscanf(rpcname, "/0x%x/%d", &cmd, &serviceType)

	head.Uint32Command = proto.Uint32(cmd)
	head.Uint32ServiceType = proto.Uint32(serviceType)

	// 需要分别设置 caller callee 的 app server service method，但是oidb协议只有下游的命令字，所以先只设置server method
	if msg.CalleeServiceName() == "" {
		msg.WithCalleeServiceName(fmt.Sprintf("trpc.oidb.%s.downservice",
			fmt.Sprintf("cmd0x%x", head.GetUint32Command())))
	}
	msg.WithCalleeService(msg.CalleeServer()) // oidb协议无法知道下游的服务名,以server代替
	msg.WithCalleeMethod(msg.ClientRPCName())

	if msg.CallerServiceName() == "" {
		msg.WithCallerServiceName(fmt.Sprintf("trpc.oidb.server.%s", head.GetStrServiceName()))
	}
	msg.WithCallerServer(msg.CallerService()) // client小工具，没有命令字，以service代替

	h, err := proto.Marshal(head)
	if err != nil {
		return nil, err
	}

	buf := bytes.NewBuffer(make([]byte, 0, int(headLen)+len(h)+len(reqbody)+1))
	if err = binary.Write(buf, binary.BigEndian, stx); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, uint32(len(h))); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, uint32(len(reqbody))); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, h); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, reqbody); err != nil {
		return nil, err
	}
	if err = binary.Write(buf, binary.BigEndian, etx); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// Decode 客户端收到服务端二进制回包数据解包到rspbody结构体
func (c *ClientCodec) Decode(msg codec.Msg, rspbuf []byte) (rspbody []byte, err error) {

	if len(rspbuf) < int(headLen) {
		return nil, errors.New("client decode rsp buf len invalid")
	}

	// 构造后端响应包头
	var head *OIDBHead
	if msg.ClientRspHead() != nil {
		// client rsp head不为空 说明是用户故意创建，希望底层回传后端响应包头
		response, ok := msg.ClientRspHead().(*OIDBHead)
		if !ok {
			return nil, errors.New("client decode rsp head not OIDBHead")
		}
		head = response
	} else {
		// client rsp head为空 说明用户不关心后端响应包头
		head = &OIDBHead{}
		// 保存新的client rsp head
		msg.WithClientRspHead(head)
	}

	headerLen := binary.BigEndian.Uint32(rspbuf[1:5])
	bodyLen := binary.BigEndian.Uint32(rspbuf[5:9])
	if int(headLen+headerLen+bodyLen+1) != len(rspbuf) {
		return nil, errors.New("client decode rsp buf len invalid")
	}

	if err := proto.Unmarshal(rspbuf[headLen:headLen+headerLen], head); err != nil {
		return nil, err
	}

	if head.GetUint32Result() != 0 {
		msg.WithClientRspErr(errs.New(int(head.GetUint32Result()), head.GetStrErrorMsg()))
	}

	return rspbuf[headLen+headerLen : headLen+headerLen+bodyLen], nil
}
