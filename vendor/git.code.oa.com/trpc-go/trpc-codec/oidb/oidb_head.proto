syntax="proto2";
package tencent.im.oidb;
option go_package="git.code.oa.com/trpc-go/trpc-codec/oidb";

import "git.code.oa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos/QQHead.proto";

message Oidb2ServerSsoInfo
{
    optional uint32 uint32_seq = 1;    //sso的seq
    optional uint32 uint32_app_id = 2;    //appid, 由SSO的LC的前4位获取
    optional bytes bytes_imei = 3;    //手机IMEI
    optional bytes bytes_client_version = 4;    //客户端协议版本号(客户端上传, 兼容旧版本)
    optional bytes bytes_sso_client_version = 5;    //客户端协议版本号(SSO添加, 使用这个字段)
    optional uint32 uint32_sso_bid = 6;    //SSO包头的BID(即客户端带上来的AppId)
    optional uint32 uint32_apn_type = 7;    // 用户网络类型
    // optional Tencent.QQService.SSO.UserData msg_sso_user_data = 8;    // SSO原生态的UserData数据
    optional string str_cross_data = 9;    // web请求时cookie内的cross_data透传字段的数据
    optional uint32 uint32_sso_client_version = 10;    //OIDB不填此字段的!!!! 后端业务可使用Agent将版本(A/I)XX.YY.ZZZZZ转换成数字的形式传给后续的业务做版本控制用.

    //qqhead link: http://git.woa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos.git
    optional Tencent.QQService.Common.QQHead msg_qq_head = 11;	// SSO原生态的qqHead数据
    optional LoginSig msg_web_login_sig = 12;  // SSO jsapi传递过来的登录态，取值等同于oidb登录态
    optional string web_context = 13;  // 业务拓展字段
};

message Oidb2ServerPCInfo
{
    optional uint32 uint32_version = 1;  //客户端版本号
    optional uint32 uint32_client_type = 2; //客户端类型
    optional uint32 uint32_pub_no = 3;   //pub_no
    optional uint32 uint32_instanceid = 4;  //实例id
}

message LoginSig
{
    optional uint32 uint32_type = 1;    //登录态类型
    optional bytes bytes_sig = 2;       //登录态内容
    optional uint32 uint32_appid = 3;   //第三方调用的appid
};

//透传数据
message TransInfo
{
    optional bytes       caller      = 1;    //主调服务的名称 trpc协议下的规范格式: trpc.应用名.服务名.pb的service名
    optional bytes       callee      = 2;    //被调服务的路由名称，trpc协议下的规范格式，trpc.应用名.服务名.pb的service名[.接口名]
    optional bytes       trace_id    = 3;    //trace_id
    repeated TransParam  params      = 4;    //自定义参数
};

//透传参数
message TransParam
{
    optional string key       = 1; //参数key
    optional bytes  value     = 2; //参数value
}

message UsrAccountMap
{
    optional uint64 uint64_uin = 1;
    optional bytes  bytes_usr_id = 2;    // 非Uin帐号，包括openid/小程序sessionKey/uid等
};

message OIDBHead
{
    optional uint64 uint64_uin = 1;             //QQ号码
    optional uint32 uint32_command = 2;         //主命令号, 即协议号
    optional uint32 uint32_service_type = 3;    //子命令号, 即在门户上申请的业务类型
    optional uint32 uint32_seq = 4;             //序列值, 用于给请求方区分不同的请求回包
    optional fixed32 uint32_client_addr = 5;    //客户端IP -- 发起请求的Uin的IP, 指触发请求的用户的外网IP, 网络序,  可以使用inet_addr()函数生成, struct in_addr格式
    optional bytes   bytes_client_addr  = 15;   //客户端IP，ipv6存储格式，16个字节，如果是ipv4ip采用兼容ipv6方式存储。
    optional fixed32 uint32_server_addr = 6;     //服务端IP -- 最前端与用户交互的服务器IP, 如果触发请求是通过Cs通道的, 则为Conn透传后的第一台服务器, 如果触发请求是通过Web, 则为Cgi所在的服务器. 网络序, 可以使用inet_addr()函数生成, struct in_addr格式
    optional bytes  bytes_server_addr = 16;              //服务端IP，ipv6存储格式，16个字节，如果是ipv4ip采用兼容ipv6方式存储。
    optional uint32 uint32_result = 7;          //返回值: 0--处理正确
    optional string str_error_msg = 8;          //错误描述 -- 给返回值非0的描述
    optional LoginSig msg_login_sig = 9;        //登录态, 指Server给用户派发的签名, 用于校验用户的合法性, 详细请见门户上的"开发指南"->"使用帮助"->"OIDB接口说明书"
    optional string str_user_name = 10;         //申请权限时的用户名
    optional string str_service_name = 11;      //申请权限时的业务名
    optional uint32 uint32_flag = 12;           //标志(某些业务可能需要调用方填写标志, 如群)
    optional uint32 uint32_from_addr = 13;      //发起请求的IP, 安全业务用, 网络序, struct in_addr格式
    optional uint32 uint32_local_addr = 14;     //收到请求的IP, 安全业务用, 网络序, struct in_addr格式
    optional uint32 uint32_moduleid = 17;       //模块id，使用模块鉴权时用到
    optional bytes  reserved = 18;     //reserved
    optional uint32 uint32_locale_id = 19;    //语言ID
    optional Oidb2ServerSsoInfo msg_sso_info = 20;    //SSO相关信息
    optional uint64 uint64_long_seq = 21;    //uint64的Seq, 有些业务使用uint64作为Session的
    optional Oidb2ServerPCInfo msg_pc_info = 25;    // PCQQ相关信息

    //tag 26~31废弃不用, 直接使用QQHead.proto里面相应的字段
    //optional uint32 uint32_route_id = 26;   //联调环境转包id
    //optional bytes  bytes_trace_id = 28; // 全链路跟踪上下文(trace_id+span_id+parent_id)
    //optional bytes  bytes_span_id = 29;
    //optional bytes  bytes_parent_id = 30;
    //optional bytes  bytes_client_guid = 31;  //Guid
    optional uint64 uint64_apply_id = 32;             //权限单id，oidb查到之后传递给后端用，返回时会抹除
    optional uint32 uint32_account_type = 33;  // 包头的帐号类型, 不填或者0:uin, 1:uid, 2:openid, 3:小程序sessionkey
    optional uint32 uint32_original_account_type = 34;  // oidb内部使用, 业务不要动
    repeated UsrAccountMap rpt_msg_usr_account_map = 35;  // uin和非uin映射
    optional string str_knocknock_id = 36;  // oidb上游的knocknock server id
    optional uint32 uint32_open_app_id = 37;  // openid/小程序登录态的appid，主要openid登录态时使用
    optional bytes bytes_auth_info = 40;  // 模块鉴权字段，oidb识别上游&oidb访问下游使用
    optional uint64 uint64_to_uin = 100;    //业务to uin，安全业务用
    optional bytes bytes_ignore_head_uin_signature = 101;    //小微项目(forestli)用，测试环境识别出这个标志之后，就不对包头uin(din)进行检查
    optional uint64 uint64_seq = 102;    //seq uint64版本
    //extensions 1000 to 1999;    //预留给业务自定义使用

    extensions 1000 to 1999;    //预留给业务自定义使用
    optional bytes bytes_service_info = 2000;   // oidb 0xc56 增加，透传给 imagnet User
};
