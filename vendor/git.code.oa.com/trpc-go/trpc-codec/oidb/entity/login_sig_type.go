// Package entity 提供了oidb的一些常量定义
package entity

// oidb.LoginSig的Uint32Type枚举定义
// 具体可参考文档：https://iwiki.woa.com/pages/viewpage.action?pageId=1220077182

// LoginSigType OIDB登录态类型
type LoginSigType = uint32

const (
	// LoginSigTypeSkey 网页类登录，由Ptlogin派发，长度10个字节
	LoginSigTypeSkey LoginSigType = 1
	// LoginSigTypeGameSignature CS 0x82登录时下发的业务签名
	LoginSigTypeGameSignature LoginSigType = 2
	// LoginSigTypeST 统一登录组件获取到的业务签名
	LoginSigTypeST LoginSigType = 3
	// LoginSigTypeClientKey QQ登录时Conn下发
	LoginSigTypeClientKey LoginSigType = 4
	// LoginSigTypesOfflineMsgSig 0x38手机取离线消息的签名(ServiceType==0)
	LoginSigTypesOfflineMsgSig LoginSigType = 5
	// LoginSigTypeValueSignature 0x38验证密码时下发的签名(ServiceType==2)
	LoginSigTypeValueSignature LoginSigType = 6
	// LoginSigTypeLSkey 网页类登录，由Ptlogin派发，字符串格式
	LoginSigTypeLSkey LoginSigType = 7
	// LoginSigTypePLSkey 网页类登录，由Ptlogin派发，字符串格式
	LoginSigTypePLSkey LoginSigType = 7
	// LoginSigTypeA2 3G的IMService服务用，VS派发
	LoginSigTypeA2 LoginSigType = 8
	// LoginSigTypeMiniA2 3G的IMService服务用，VS派发
	LoginSigTypeMiniA2 LoginSigType = 17
	// LoginSigTypeA8 Oidb派发的无线验密签名
	LoginSigTypeA8 LoginSigType = 10
	// LoginSigTypeCompressedLSkey 网页类登录，由Ptlogin派发，压缩格式
	LoginSigTypeCompressedLSkey LoginSigType = 11
	// LoginSigTypeMailSig64 广研邮箱登陆时oidb派发
	LoginSigTypeMailSig64 LoginSigType = 12
	// LoginSigTypeGYSig64 独立密码用户，广研派发，广研自有密码情况下派发
	LoginSigTypeGYSig64 LoginSigType = 13
	// LoginSigTypeQQSIGN Conn派发，从QQ客户端跳转到无线类页面时候用
	LoginSigTypeQQSIGN LoginSigType = 14
	// LoginSigTypeMiniGame Oidb协议0xfa派发，用ptlogin签名换到的游戏签名
	LoginSigTypeMiniGame LoginSigType = 15
	// LoginSigTypeMusic 音乐客户端签名
	LoginSigTypeMusic LoginSigType = 18
	// LoginSigTypeBizImpression 好友印象服务器派发
	LoginSigTypeBizImpression LoginSigType = 21
	// LoginSigTypeConn2WB Conn带给业务Server的签名
	LoginSigTypeConn2WB LoginSigType = 22
	// LoginSigTypeWB2OIDB 微博到OIDB
	LoginSigTypeWB2OIDB LoginSigType = 23
	// LoginSigTypeTenpay 财富通到OIDB
	LoginSigTypeTenpay LoginSigType = 24
	// LoginSigTypeQPlus Q+到OIDB
	LoginSigTypeQPlus LoginSigType = 25
	// LoginSigTypeWin8 Win8到OIDB
	LoginSigTypeWin8 LoginSigType = 26
	// LoginSigTypePTLogin ptlogin到OIDB(pskey) ，其中domainId即oidb登录态里的appid
	LoginSigTypePTLogin LoginSigType = 27
	// LoginSigTypeQCallA2 QCall的A2登录态
	LoginSigTypeQCallA2 LoginSigType = 32
	// LoginSigTypeQCallQQ QCall的QQ登录态
	LoginSigTypeQCallQQ LoginSigType = 33
	// LoginSigTypeOA 公司的OA登录态, 只能在IPV6和ProtoBuf的包头使用(IPV4的包头登录态字段长度不够)
	LoginSigTypeOA LoginSigType = 34
	// LoginSigTypeSmartDevice 智能设备的登录态
	LoginSigTypeSmartDevice LoginSigType = 35
	// LoginSigTypeBizQQCombine 企业QQ的组合登录态, 用于主号修改工号/工号修改主号.
	LoginSigTypeBizQQCombine LoginSigType = 36
	// LoginSigTypeOpenPlatform 带OpenID访问的登录态
	LoginSigTypeOpenPlatform LoginSigType = 37
	// LoginSigTypeBigData 大数据通道的登录态
	LoginSigTypeBigData LoginSigType = 38
	// LoginSigTypeTinyIDPSKey tinydId的PSKey
	LoginSigTypeTinyIDPSKey LoginSigType = 39
	// LoginSigTypeRegister 注册登录态，登录业务使用
	LoginSigTypeRegister LoginSigType = 40
	// LoginSigTypeRecycle 回收时生成的登录态，登录业务使用
	LoginSigTypeRecycle LoginSigType = 41
	// LoginSigTypeAppeal 用户申诉时生成的登录态，登录业务使用
	LoginSigTypeAppeal LoginSigType = 42
	// LoginSigTypeMiniProgram 小程序登录态，小程序登录后台派发
	LoginSigTypeMiniProgram LoginSigType = 43
)
