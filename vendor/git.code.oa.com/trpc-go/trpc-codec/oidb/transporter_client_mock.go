// Code generated by MockGen. DO NOT EDIT.
// Source: transporter_client.go

// Package oidb is a generated GoMock package.
package oidb

import (
	context "context"
	reflect "reflect"

	client "git.code.oa.com/trpc-go/trpc-go/client"
	oidb_transporter "git.code.oa.com/trpcprotocol/oicq/oidb_transporter"
	gomock "github.com/golang/mock/gomock"
)

// MockTransporterClient is a mock of TransporterClient interface.
type MockTransporterClient struct {
	ctrl     *gomock.Controller
	recorder *MockTransporterClientMockRecorder
}

// MockTransporterClientMockRecorder is the mock recorder for MockTransporterClient.
type MockTransporterClientMockRecorder struct {
	mock *MockTransporterClient
}

// NewMockTransporterClient creates a new mock instance.
func NewMockTransporterClient(ctrl *gomock.Controller) *MockTransporterClient {
	mock := &MockTransporterClient{ctrl: ctrl}
	mock.recorder = &MockTransporterClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransporterClient) EXPECT() *MockTransporterClientMockRecorder {
	return m.recorder
}

// DoWithIP mocks base method.
func (m *MockTransporterClient) DoWithIP(ctx context.Context, head *OIDBHead, reqbody, rspbody interface{}, opts ...client.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, head, reqbody, rspbody}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoWithIP", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoWithIP indicates an expected call of DoWithIP.
func (mr *MockTransporterClientMockRecorder) DoWithIP(ctx, head, reqbody, rspbody interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, head, reqbody, rspbody}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoWithIP", reflect.TypeOf((*MockTransporterClient)(nil).DoWithIP), varargs...)
}

// DoWithKnocknock mocks base method.
func (m *MockTransporterClient) DoWithKnocknock(ctx context.Context, head *OIDBHead, reqbody, rspbody interface{}, opts ...client.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, head, reqbody, rspbody}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoWithKnocknock", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoWithKnocknock indicates an expected call of DoWithKnocknock.
func (mr *MockTransporterClientMockRecorder) DoWithKnocknock(ctx, head, reqbody, rspbody interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, head, reqbody, rspbody}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoWithKnocknock", reflect.TypeOf((*MockTransporterClient)(nil).DoWithKnocknock), varargs...)
}

// MocktransCodec is a mock of transCodec interface.
type MocktransCodec struct {
	ctrl     *gomock.Controller
	recorder *MocktransCodecMockRecorder
}

// MocktransCodecMockRecorder is the mock recorder for MocktransCodec.
type MocktransCodecMockRecorder struct {
	mock *MocktransCodec
}

// NewMocktransCodec creates a new mock instance.
func NewMocktransCodec(ctrl *gomock.Controller) *MocktransCodec {
	mock := &MocktransCodec{ctrl: ctrl}
	mock.recorder = &MocktransCodecMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MocktransCodec) EXPECT() *MocktransCodecMockRecorder {
	return m.recorder
}

// Marshal mocks base method.
func (m *MocktransCodec) Marshal() ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Marshal")
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Marshal indicates an expected call of Marshal.
func (mr *MocktransCodecMockRecorder) Marshal() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Marshal", reflect.TypeOf((*MocktransCodec)(nil).Marshal))
}

// Unmarshal mocks base method.
func (m *MocktransCodec) Unmarshal(arg0 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unmarshal", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unmarshal indicates an expected call of Unmarshal.
func (mr *MocktransCodecMockRecorder) Unmarshal(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unmarshal", reflect.TypeOf((*MocktransCodec)(nil).Unmarshal), arg0)
}

// MockoidbClientProxy is a mock of oidbClientProxy interface.
type MockoidbClientProxy struct {
	ctrl     *gomock.Controller
	recorder *MockoidbClientProxyMockRecorder
}

// MockoidbClientProxyMockRecorder is the mock recorder for MockoidbClientProxy.
type MockoidbClientProxyMockRecorder struct {
	mock *MockoidbClientProxy
}

// NewMockoidbClientProxy creates a new mock instance.
func NewMockoidbClientProxy(ctrl *gomock.Controller) *MockoidbClientProxy {
	mock := &MockoidbClientProxy{ctrl: ctrl}
	mock.recorder = &MockoidbClientProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockoidbClientProxy) EXPECT() *MockoidbClientProxyMockRecorder {
	return m.recorder
}

// HandleProcess mocks base method.
func (m *MockoidbClientProxy) HandleProcess(ctx context.Context, req *oidb_transporter.MsgRequest, opts ...client.Option) (*oidb_transporter.MsgReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleProcess", varargs...)
	ret0, _ := ret[0].(*oidb_transporter.MsgReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleProcess indicates an expected call of HandleProcess.
func (mr *MockoidbClientProxyMockRecorder) HandleProcess(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleProcess", reflect.TypeOf((*MockoidbClientProxy)(nil).HandleProcess), varargs...)
}
