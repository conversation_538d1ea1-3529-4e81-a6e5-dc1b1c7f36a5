// Code generated by MockGen. DO NOT EDIT.
// Source: trpc_oidb_client.go

// Package oidb is a generated GoMock package.
package oidb

import (
	context "context"
	reflect "reflect"

	client "git.code.oa.com/trpc-go/trpc-go/client"
	gomock "github.com/golang/mock/gomock"
)

// MockOIDBClient is a mock of OIDBClient interface.
type MockOIDBClient struct {
	ctrl     *gomock.Controller
	recorder *MockOIDBClientMockRecorder
}

// MockOIDBClientMockRecorder is the mock recorder for MockOIDBClient.
type MockOIDBClientMockRecorder struct {
	mock *MockOIDBClient
}

// NewMockOIDBClient creates a new mock instance.
func NewMockOIDBClient(ctrl *gomock.Controller) *MockOIDBClient {
	mock := &MockOIDBClient{ctrl: ctrl}
	mock.recorder = &MockOIDBClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOIDBClient) EXPECT() *MockOIDBClientMockRecorder {
	return m.recorder
}

// DoWithIP mocks base method.
func (m *MockOIDBClient) DoWithIP(ctx context.Context, head *OIDBHead, reqbody, rspbody interface{}, opts ...client.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, head, reqbody, rspbody}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoWithIP", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoWithIP indicates an expected call of DoWithIP.
func (mr *MockOIDBClientMockRecorder) DoWithIP(ctx, head, reqbody, rspbody interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, head, reqbody, rspbody}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoWithIP", reflect.TypeOf((*MockOIDBClient)(nil).DoWithIP), varargs...)
}

// DoWithKnocknock mocks base method.
func (m *MockOIDBClient) DoWithKnocknock(ctx context.Context, head *OIDBHead, reqbody, rspbody interface{}, opts ...client.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, head, reqbody, rspbody}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoWithKnocknock", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoWithKnocknock indicates an expected call of DoWithKnocknock.
func (mr *MockOIDBClientMockRecorder) DoWithKnocknock(ctx, head, reqbody, rspbody interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, head, reqbody, rspbody}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoWithKnocknock", reflect.TypeOf((*MockOIDBClient)(nil).DoWithKnocknock), varargs...)
}
