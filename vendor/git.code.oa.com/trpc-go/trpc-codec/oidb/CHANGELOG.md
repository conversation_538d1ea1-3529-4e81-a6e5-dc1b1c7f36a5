# Change Log

## [0.3.2](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.3.2) (2023-12-14)

- oidb库支持CMDB key鉴权模式。

## [0.3.1](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.3.1) (2023-10-31)

- oidb_head.proto 加入bytes_auth_info字段，支持oidb模块签名。字段序列为40

## [0.3.0](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.3.0) (2023-09-18)

- oidb_head.proto 扩展字段回滚到 1000-1999，防止和老服务冲突

## [0.2.15](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.15) (2023-05-22)

- oidb支持Forwarder调用，IP鉴权方式下支持调用二进制协议。

## [0.2.14](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.14) (2023-04-06)

- refactor: use remote QQHead (!655)
- feat: update oidb_head.proto (!654)

## [0.2.13](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.13) (2023-02-20)
- 更新 oidb 中引用的 sso 库版本，新版 sso_protos.QQHead 中增加了压测相关字段

## [0.2.12](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.12) (2023-02-09)

## Features
- 增加oidb的LoginSig结构体中Uint32Type的取值枚举，以定义OIDB接受的多种登录态

## [0.2.11](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.11) (2022-12-12)

## Features
- 增加oidb透传功能

## [0.2.10](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.10) (2022-09-22)

## Features
- 更新sso_protos.QQHead增加安全相关字段

## [0.2.9](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.9) (2022-07-29)

## Features
- 修复0.2.7的oidb组包错误（无论是v2 pb还是二进制协议）

## [0.2.8](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.8) (2022-07-26)

## Features
- 移除sso_protos旧依赖


## [0.2.7](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.7) (2022-07-19)

## Features
- 更新qqHead路径
- 修复oidb v2 pb类型的打解包透传模式
- oidb.DoWithKnockknock参数修改

## [0.2.6](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.6) (2021-12-13)

### Features
- 更新`sso_protos`v0.0.6版本
- 新增一个`CopyHead`函数，方便业务拷贝head
- 删除trace透传

## [0.2.5](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.5) (2021-06-11)

### Features
- `google.golang.org/protobuf/proto`替换`github.com/golang/protobuf/proto`
- 持jaeger埋点trace透传

## [0.2.4](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.4) (2021-05-24)

### Features
- 支持使用IP鉴权模块进行oidb调用
- 优化代码，增加IP鉴权调用相应的单元测试

### Bug Fixes
- 解决cient.opt不断增长的[bug](http://tapd.oa.com/trpc_go/bugtrace/bugs/view?bug_id=1020418425087994425)


## [0.2.3](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.3) (2021-04-20)

### Features
- oidb生成mock文件，方便测试

## [0.2.2](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.2) (2021-04-15)

### Features
- oidb可测性优化，提供client interface和NewClientProxy函数

## [0.2.1](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.1) (2021-03-18)

### Features
- 支持以knocknock鉴权方式调用后端oidb服务
- 依赖的sso proto更新

## [0.2.0](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.2.0) (2020-11-30)

### Features
- OIDB增加w3c trace context支持

## [0.1.1](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.1.1) (2020-09-14)

### Features
- 更新oidb包头协议

## [0.1.0](https://git.woa.com/trpc-go/trpc-codec/tree/oidb/v0.1.0) (2020-01-12)

### Features
- 支持提供oidb协议的服务
- 支持调用oidb协议的后台
- 支持将oidb协议的数值命令字改造成rpc模式

