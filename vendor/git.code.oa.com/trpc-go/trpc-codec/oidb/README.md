# tRPC-Go 第三方协议实现之 oidb
[![BK Pipelines Status](https://api.bkdevops.qq.com/process/api/external/pipelines/projects/pcgtrpcproject/p-01c74f8679444a4eb12e59f5449b9ed8/badge?X-DEVOPS-PROJECT-ID=pcgtrpcproject)](http://api.devops.oa.com/process/api-html/user/builds/projects/pcgtrpcproject/pipelines/p-01c74f8679444a4eb12e59f5449b9ed8/latestFinished?X-DEVOPS-PROJECT-ID=pcgtrpcproject) [![Coverage](https://tcoverage.woa.com/api/getCoverage/getTotalImg/?pipeline_id=p-01c74f8679444a4eb12e59f5449b9ed8)](http://macaron.oa.com/api/coverage/getTotalLink/?pipeline_id=p-01c74f8679444a4eb12e59f5449b9ed8) [![GoDoc](https://img.shields.io/badge/API%20Docs-GoDoc-green)](http://godoc.oa.com/git.code.oa.com/trpc-go/trpc-codec/oidb)
##oidb是PCG信息流平台QQ看点，原SNG即通相关团队等多个业务广泛使用的通信协议，基于protobuf编码。

## 实现步骤

### oidb.Do
- 1. 实现tRPC-Go [拆包接口](https://git.woa.com/trpc-go/trpc-go/blob/master/transport/transport.go), 拆出一个完整的oidb消息包。
- 2. 实现tRPC-Go [Codec打解包接口](https://git.woa.com/trpc-go/trpc-go/blob/master/codec/codec.go)，需要注意以下几点：
 - 2.1 oidb server codec decode解包后，通过msg.WithServerRPCName将oidb数值型命令字 0x110.1 转化成rpcname /0x110/1。
 - 2.2 oidb server codec encode回包前，通过msg.ServerRspErr将handler返回的错误error转化成oidb包头错误码。
 - 2.3 oidb client codec encode发包前，通过msg.ClientRPCName取出rpcname /0x110/1 转化成oidb数值型命令字 0x110.1 。
 - 2.4 oidb client codec decode收包后，通过msg.WithClientRspErr将oidb回包包头错误码转化成error返回给调用方。
- 3. init函数将具体实现注册到trpc框架中。

### oidb.DoWithKnocknock
OIDB后台支持原生tRPC协议,但该调用方式与上面的不同
该调用方式,实质上是调用OIDB后台提供的tRPC服务(trpc.oicq.oidb.Transporter),路由到OIDB平台后,再路由到OIDB后端业务服务
在调用trpc.oicq.oidb.Transporter服务时,使用knocknock鉴权,详情请见:http://km.oa.com/group/38650/articles/show/445997
* 支持pb和二进制协议的oidb body

已废弃，请使用oidb.DoWithKnocknockNew，可支持透传包体的trpc-oidb(trpc.oicq.oidb.Forwarder，oidb官方推荐使用)和需要用protobuf结构体包裹业务包体的trpc-oidb(trpc.oicq.oidb.Transporter)。

## trpc框架调用oidb协议
 
### oidb.Do
```golang
import (
    "git.code.oa.com/trpc-go/trpc-codec/oidb"
    "github.com/golang/protobuf/proto"
) 

func (s *server) SayHello(ctx context.Context, req *pb.ReqBody, rsp *pb.RspBody) error {
    head := &oidb.OIDBHead{
        Uint64Uin:         proto.Uint64(10000),
        Uint32Command:     proto.Uint32(0x1100),
        Uint32ServiceType: proto.Uint32(1), 
    }
    
    // 后端 callee 为: trpc.oidb.cmd0x1100.downservice
    // network: udp
    // 具体配置请看 https://git.woa.com/trpc-go/trpc-codec/blob/master/oidb/examples/helloworld/trpc_go.yaml#L19
    if err := oidb.Do(ctx, head, req, rsp); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }

	return
}
```

### 【已废弃】oidb.DoWithKnocknock
```golang
import (
    "git.code.oa.com/trpc-go/trpc-codec/oidb"
    "github.com/golang/protobuf/proto"
) 

func (s *server) SayHello(ctx context.Context, req *pb.ReqBody, rsp *pb.RspBody) error {
    head := &oidb.OIDBHead{
        Uint64Uin:         proto.Uint64(10000),
        Uint32Command:     proto.Uint32(0x1100),
        Uint32ServiceType: proto.Uint32(1), 
    }
    
    // 后端 callee 为: trpc.oicq.oidb.Transporter
    if err := oidb.DoWithKnocknock(ctx, head, req, rsp); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }

	return
}

func (s *server) SayHelloBin(ctx context.Context, req oidb1.ReqBody, rsp oidb1.RspBody) error {
    head := &oidb.OIDBHead{
        Uint64Uin:         proto.Uint64(10000),
        Uint32Command:     proto.Uint32(0x1100),
        Uint32ServiceType: proto.Uint32(1), 
    }
    rawData, err := req.Marshal()
    if err != nil {
        return err
    }
    oidbReq := &codec.Body{
        Data: rawData,
    }
    oidbRsp := &codec.Body{}
    // 后端 callee 为: trpc.oicq.oidb.Transporter，适用于使用二进制协议的oidb后端
    if err := oidb.DoWithKnocknock(ctx, head, oidbReq, oidbRsp); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }
    return rsp.Unmarshal(oidbRsp.Data)
}
```

### oidb.DoWithKnocknockNew
```golang
import (
    "git.code.oa.com/trpc-go/trpc-codec/oidb"
    "github.com/golang/protobuf/proto"
    "git.code.oa.com/trpc-go/trpc-codec/oidb/entity"
) 

func (s *server) SayHello(ctx context.Context, req *pb.ReqBody, rsp *pb.RspBody) error {
    head := &oidb.OIDBHead{
        Uint64Uin:         proto.Uint64(10000),
        Uint32Command:     proto.Uint32(0x1100),
        Uint32ServiceType: proto.Uint32(1), 
    }
    
    // 后端 callee 为: trpc.oicq.oidb.Transporter
    if err := oidb.DoWithKnocknockNew(ctx, head, req, rsp, entity.Transporter); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }

    // 后端 callee 为: trpc.oicq.oidb.Forwarder
    if err := oidb.DoWithKnocknockNew(ctx, head, req, rsp, entity.Forwarder); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }

	return
}

func (s *server) SayHelloBin(ctx context.Context, req oidb1.ReqBody, rsp oidb1.RspBody) error {
    head := &oidb.OIDBHead{
        Uint64Uin:         proto.Uint64(10000),
        Uint32Command:     proto.Uint32(0x1100),
        Uint32ServiceType: proto.Uint32(1), 
    }
    rawData, err := req.Marshal()
    if err != nil {
        return err
    }
    oidbReq := &codec.Body{
        Data: rawData,
    }
    oidbRsp := &codec.Body{}
    // 后端 callee 为: trpc.oicq.oidb.Transporter
    if err := oidb.DoWithKnocknockNew(ctx, head, oidbReq, oidbRsp, entity.Transporter); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }
    // 后端 callee 为: trpc.oicq.oidb.Forwarder
    if err := oidb.DoWithKnocknock(ctx, head, oidbReq, oidbRsp, entity.Forwarder); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }
    return rsp.Unmarshal(oidbRsp.Data)
}
```

## 使用QQ后台鉴权能力 (oidb.DoWithQQBackendAuth)
OIDB平台提供了一种使用QQ后台鉴权能力的功能，具体可见MR：https://git.woa.com/trpc-go/trpc-codec/merge_requests/687

如果使用QQ后台鉴权能力，使用DoWithQQBackendAuth方法，入参和oidb.DoWithIPNew一致。

请在第一次调用DoWithQQBackendAuth方法前调用InitQQBackendAuthenticator方法，以初始化QQ后台鉴权器。

QQ后台鉴权器默认使用eth1网络接口的ip作为参与签名生成的参数，如果需要使用其他的ip，请在调用DoWithQQBackendAuth方法之前，调用`AddQQBackendAuthInfoByIP`方法自行为oidb头生成签名。

示例代码：
```go
import (
    "git.code.oa.com/trpc-go/trpc-codec/oidb"
    "git.code.oa.com/trpc-go/trpc-codec/oidb/entity"
)

var (
    cmdbID = "xxx" // CMDB模块id
    authKey = "xxx" // qqdata平台OIDB权限单提供
)
func main() {
    oidb.InitQQBackendAuthenticator(cmdbID, authKey)
    // 组req和rsp
    req := &pb.ReqBody{}
    rsp := &pb.RspBody{}
    head := &oidb.OIDBHead{
        Uint64Uin:         proto.Uint64(10000),
        Uint32Command:     proto.Uint32(0x1110),
        Uint32ServiceType: proto.Uint32(1), 
    }
    // 使用IpFwd，client的service name为trpc.oicq.oidb.IpFwd
    if err := oidb.DoWithQQBackendAuth(context.Background(), head, req, rsp, entity.Forwarder); err != nil {
		// handle err
	}
    // 使用IpTrans，client的service name为trpc.oicq.oidb.IpTrans
    if err := oidb.DoWithQQBackendAuth(context.Background(), head, req, rsp, entity.Transporter); err != nil {
		// handle err
	}    
}
```

## oidb提供服务返回的错误码
由于trpc框架错误码和oidb接入层错误码的区间是一样的，所以为了避免冲突，oidb服务返回错误时，框架错误码会 +100000 ，业务错误码跟用户设置一致。

