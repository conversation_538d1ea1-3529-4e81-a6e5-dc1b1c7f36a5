package qzh

import (
	"context"
	"reflect"

	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// HandlerFunc trpc 框架内部 filter 处理函数类型
type HandlerFunc func(svr interface{}, ctx context.Context, f server.FilterFunc) (rspbody interface{}, err error)

// service 初始化信息
var (
	// SerializeMap main 里面初始化，不判断并发读写
	SerializeMap = make(map[string]int)
	methodMap    = make(map[string]HandlerFunc)
)

// RegisterMethod 注册 pdu 服务需要处理的函数
func RegisterMethod(method string, serializeType int, reqType interface{}, rspType interface{},
	handler filter.HandleFunc) {
	SerializeMap[method] = serializeType
	methodMap[method] =
		func(svr interface{}, ctx context.Context, f server.FilterFunc) (rspbody interface{}, err error) {
			// 取对应请求包结构
			req := reflect.New(reflect.TypeOf(reqType).Elem()).Interface()
			filters, err := f(req)
			if err != nil {
				log.Tracef("process ==> filter func failed:%v", err)
				return nil, err
			}
			rsp := reflect.New(reflect.TypeOf(rspType).Elem()).Interface()
			err = filters.Handle(ctx, req, rsp, handler)
			if err != nil {
				log.Tracef("process ==> handle failed:%v", err)
				return nil, err
			}
			return rsp, nil
		}
}

// RegisterTrpcService 注册 trpc 的服务处理
func RegisterTrpcService(svr *server.Server, serviceName string) error {
	var methods []server.Method
	for name, handler := range methodMap {
		methods = append(methods, server.Method{
			Name: name,
			Func: handler,
		})
	}
	err := svr.Service(serviceName).Register(&server.ServiceDesc{
		ServiceName: serviceName,
		HandlerType: nil,
		Methods:     methods,
	}, nil)
	if err != nil {
		return err
	}
	return nil
}
