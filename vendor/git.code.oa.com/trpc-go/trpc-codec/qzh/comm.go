package qzh

import (
	"fmt"
	"time"
)

// 登录类型
const (
	EnumAuthTypeNull     = 0x00
	EnumAuthTypeWeb      = 0x01 // 强登陆，skey
	EnumAuthTypeSvr      = 0x02 // svr 访问，暂时无需 key 校验
	EnumAuthTypeLskey    = 0x04 // 弱登陆态，lskey
	EnumAuthType3g       = 0x08 // 老的 3G key, 要废掉，3G 的 A8key 请使用 ENUM_AUTH_TYPE_A8
	EnumAuthTypePskey    = 0x0C // pskey 后端优先使用这个判断
	EnumAuthTypeHostkey  = 0x10 // 主人态 key
	EnumAuthTypeA8       = 0x20 // 新的 3G A8 key.
	EnumAuthTypeA2       = 0x40 // 新的 3G A2 key.
	EnumAuthTypePhoneA2  = 0x60 // 手机号码登录 A2 key.
	EnumAuthTypeQqOpenid = 0x80 // 开平 QQ 登录的 openid openkey
	EnumAuthTypeWxOpenid = 0xc0 // 微信登录的 openid openkey
)

// 公共返回码定义
const (
	QzbdErrAPIQzoneini      = -50 //接口初始化失败
	QzbdErrL5Getroute       = -40 //L5 获取路由失败
	QzbdErrClientRecv       = -30 //网络接包失败，后端服务响应超时
	QzbdErrClientSend       = -29 //网络发包失败
	QzbdErrClientConnect    = -28 //网络连接失败
	QzbdErrClientBuflack    = -27 //网络缓冲区溢出
	QzbdErrClientSettimeout = -26 //设置超时失败
	QzbdErrPackFlow         = -20 //收发包的序列号不一致
	QzbdErrPackUnpack       = -19 //解包失败，注意具体的 errmsg
	QzbdErrPackPack         = -18 //打包失败，注意具体的 errmsg
	QzbdErrPackCmd          = -17 //命令字不能识别
	QzbdErrPackUin          = -16 //Uin 错误
	QzbdErrPackProto        = -15 //不能识别协议类型
	QzbdErrSystem           = -1  //系统 api 错误
	QzbdSucc                = 0   //成功
)

// QzoneHead qzone 协议通用接口
type QzoneHead interface {
	Marshal() ([]byte, error)
	Unmarshal([]byte) error
	AppProtocol() string
	GetCmdPattern() string // format: /xxx/xxx
	SetRspCode(int16)
	GetRspCode() int16
	GetPackFlow() uint32
	GetRspMsg() string
}

// CallDesc 调用接口描述
type CallDesc struct {
	// 主命令字
	CmdID int32
	// 子命令字
	SubCmdID int32
	// 应用层协议类型
	AppProtocol string
	// callee 名字，用于配置和上报
	CalleeName string
	// callee method，用于上报
	CalleeMethod string
}

// AuthInfo 登陆态详情
type AuthInfo struct {
	Uin uint32
	// 鉴权类型
	EnumAuthType uint32
	// 鉴权 Key，包括 skey 和 svrkey
	Key string
	// 扩展鉴权 key 内容 除了_key 所支持的之外的所有 key 类型，包括 3gkey(A8), 弱登陆态 key(lskey) 等
	Exkey string
	// 用户 ip，以主机字节序传入
	ClientIP uint32
	// 发起请求的最前端业务机器 ip，以主机字节序传入
	ServerIP  uint32
	PtloginID uint32
	// 应用类型
	TypeApp uint32
	// 具体来源类型
	TypeSource uint32
	// 平台类型
	TypePlatform uint8
}

// QzError 带错误码的简单错误结构体，后续需要再拓展
type QzError struct {
	RspCode int32
	Msg     string
}

// Error 返回自定义错误信息
func (e QzError) Error() string {
	return fmt.Sprintf("(code:%v, msg:%v)", e.RspCode, e.Msg)
}

// CreateFlow 随机生成 flow
func CreateFlow() uint32 {
	return uint32(time.Now().Nanosecond())
}

// GetQzErrorFromError 从错误码和现有 error 生成 QzError
func GetQzErrorFromError(ret int32, err error) QzError {
	if val, ok := err.(QzError); ok {
		return val
	}
	return QzError{RspCode: ret, Msg: err.Error()}
}

// GetQzErrorFromMsg 从错误信息和错误码生成 QzError
func GetQzErrorFromMsg(ret int32, msg string) QzError {
	return QzError{RspCode: ret, Msg: msg}
}
