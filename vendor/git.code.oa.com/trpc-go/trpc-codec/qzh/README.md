# tRPC-Go Middleware 第三方协议实现之 qza/pdu

[![BK Pipelines Status](https://api.bkdevops.qq.com/process/api/external/pipelines/projects/pcgtrpcproject/p-c5c2618bf511440abac2ba53f081675e/badge?X-DEVOPS-PROJECT-ID=pcgtrpcproject)](http://api.devops.oa.com/process/api-html/user/builds/projects/pcgtrpcproject/pipelines/p-c5c2618bf511440abac2ba53f081675e/latestFinished?X-DEVOPS-PROJECT-ID=pcgtrpcproject) [![Coverage](https://tcoverage.woa.com/api/getCoverage/getTotalImg/?pipeline_id=p-c5c2618bf511440abac2ba53f081675e)](http://macaron.oa.com/api/coverage/getTotalLink/?pipeline_id=p-c5c2618bf511440abac2ba53f081675e) [![GoDoc](https://img.shields.io/badge/API%20Docs-GoDoc-green)](http://godoc.oa.com/git.code.oa.com/trpc-go/trpc-codec/qzh)
## 实现步骤：
- 1. 实现 tRPC-Go [ReadFrame 拆包接口](https://git.woa.com/trpc-go/trpc-go/blob/master/transport/transport.go), 拆出一个完整的消息包。
- 2. 实现 tRPC-Go [Codec 打解包接口](https://git.woa.com/trpc-go/trpc-go/blob/master/codec/codec.go)，需要注意以下几点：
- 2.1 qza/pdu server codec decode 解出包以后，通过 msg.WithServerRpcName 将 qza 的主子命令字 (pdu 的 version+ 命令字) 转化成 rpcname。
- 2.2 qza/pdu server codec encode 回包前，通过 msg.ServerRspErr 将 handler 返回的错误 error 转化成包头错误码。
- 2.3 qza/pdu client codec encode 发包前，通过 msg.ClientRpcName 取出 rpcname 转化成 qza/pdu 的命令字。
- 2.4 qza/pdu client codec decode 收包后，通过 msg.WithClientRspErr 将回包包头错误码转化成 error 返回给调用方。
- 3. init 函数将具体实现注册到 trpc 框架中。

## rpc 说明：
- trpc 服务默认 rpc 名字是 /packagename.Service/Method，比如示例协议中的SayHello函数，默认rpcname是/helloworld.Greeter/SayHello。
- 对于 qza/pdu 这种数字型命令字来说需要进行转换，比如主命令字 (version)0x10，子命令字 21，则 rpcName 格式默认为/0x10/21。
- 以上 rpcName 的格式，可以由 proto 文件中的注释指定，参考示例协议代码，helloworld.proto；也可以参考下文 qza/pdu 处理函数注册，手动指定。
- 使用 trpc-go-cmdline 指定 rpcName 时，记得使用使用额外的选项-alias 才可。

## trpc 框架调用 pdu 协议
```golang
import (
    "git.code.oa.com/trpc-go/trpc-codec/qzh/pdu"
    "github.com/golang/protobuf/proto"
) 
```
### 方法 1，通过 proxy 调用
```golang
// SayHelloByProxy 通过proxy调用，推荐这种方式，提供mock client
func (s *server) SayHelloByProxy(ctx context.Context, req *pb.ReqBody, rsp *pb.RspBody) (err error) {
    head := pdu.Head(ctx)
    // 1. 通过proxy方式，推荐这种，提供mock client
    // 可指定更多option
    proxy := pdu.NewClientProxy()
    param := &pdu.PduDesc{
        Version: 1,
        Cmd:     1,
        CalleeName: "trpc.app.server.test",
        CalleeMethod: "/0x1/1",
    }
    if err := proxy.Do(ctx, param, reqBody, rspBody); err != nil {
        return err
    }
    return
}
```
### 方法 2，通过 pdu.DoV2 调用
```golang
// SayHello 通过pdu.DoV2调用，老的使用方式
func (s *server) SayHello(ctx context.Context, req *pb.ReqBody, rsp *pb.RspBody) (err error) {
    head := pdu.Head(ctx)
    
    // 通过pdu.DoV2方式
    // 如果使用DoV2函数，后端为非pb协议，可以用option来灵活指定序列化方式
    // 指定调用service名字和调用的rpcName
    if err := pdu.DoV2(ctx, "trpc.app.server.test", "/0x1/1", 1, 2, req, rsp, client.WithReqHead(head)); err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }
    return
}
```

## trpc 框架调用 qza 协议
```golang
import (
    "git.code.oa.com/trpc-go/trpc-codec/qzh"
    "git.code.oa.com/trpc-go/trpc-codec/qzh/qza"
    "github.com/golang/protobuf/proto"
) 
```

### 方法 1，通过 qza.Proxy 调用

```golang
// SayHelloByProxy 通过proxy调用，推荐这种方式，提供mock client
func (s *server) SayHelloByProxy(ctx context.Context, req *pb.ReqBody, rsp *pb.RspBody) (err error) {
    head := qza.Head(ctx)
    desc := qzh.CallDesc{
        AppProtocol: "qza",
        CmdId:    1,
        SubCmdId: 2,
        CalleeName: "trpc.app.server.test",
        CalleeMethod: "/0x1/1",
    }
    auth := qzh.AuthInfo{
        Uin: 123456,
    }
    // 通过proxy调用，可指定更多option
    proxy := qza.NewClientProxy()
    if err := proxy.Do(ctx, desc, auth, reqBody, rspBody); err != nil {
       return err
    }
}
```

### 方法 2，通过 qza.DoV2 调用

```golang
// SayHello 通过qza.DoV2进行调用，老的使用方式
func (s *server) SayHello(ctx context.Context, req *pb.ReqBody, rsp *pb.RspBody) (err error) {
    head := qza.Head(ctx)
    desc := qzh.CallDesc{
        AppProtocol: "qza",
        CmdId:    1,
        SubCmdId: 2,
    }
    auth := qzh.AuthInfo{
        Uin: 123456,
    }
    // 如果想要指定reqHead和rspHead，则使用client的option，然后使用指针传入对应head即可
    // 指定调用service名字和调用的rpcName
    err := qza.DoV2(ctx, "trpc.app.server.test", "/0x1/1", desc, auth, req, rsp, client.WithReqHead(head))
    if err != nil {
        log.Errorf("get fail:%v", err)
        return errs.New(1000, "inner fail")
    }
    return
}
```

## trpc 框架启动 qza/pdu 服务，支持无协议自定义服务路由

```golang
// Package main is the main package.
package main

import (
    "git.code.oa.com/TianShu/rule/echo"
    
    "git.code.oa.com/trpc-go/trpc-codec/qzh"
    "git.code.oa.com/trpc-go/trpc-go"
    "git.code.oa.com/trpc-go/trpc-go/codec"
    
    pb "git.code.oa.com/trpcprotocol/flowx/dataserver_recall"
    
    _ "git.code.oa.com/trpc-go/trpc-codec/qzh/pdu"
    _ "git.code.oa.com/trpc-go/trpc-codec/qzh/qza"
    _ "git.code.oa.com/trpc-go/trpc-config-tconf"
    _ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
    _ "git.code.oa.com/trpc-go/trpc-filter/recovery"
    _ "git.code.oa.com/trpc-go/trpc-log-atta"
    _ "git.code.oa.com/trpc-go/trpc-metrics-m007"
    _ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
    _ "git.code.oa.com/trpc-go/trpc-naming-polaris"
    _ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
    _ "git.code.oa.com/trpc-go/trpc-selector-cl5"
    _ "go.uber.org/automaxprocs"
)

type flowxRecallServiceImpl struct{}

func main() {
    s := trpc.NewServer() 
    // 开一个原生的trpc+pb服务 
    pb.RegisterFlowxRecallService(s, &flowxRecallServiceImpl{}) 
    //再开启一个自定义的无协议服务 
    //注册函数，增加命令字处理0x1:1 -- 对应codec rpcName格式 
    qzh.RegisterMethod("/0x1/1", codec.SerializationTypeJCE, (*echo.EchoReq)(nil), (*echo.EchoRsp)(nil), Echo)
    // 增加新的service，通过配置指定包头协议，参考example中的trpc_go.yaml.qzh配置 
    err := qzh.RegisterTrpcService(s, "trpc.flowx.dataserver.echo")
    if err != nil {
    	panic(err)
    } 
    // 开启服务 
    _ = s.Serve()
}

// Echo 注册的函数，类型需要是func(ctx context.Context, reqbody interface{}, rspbody interface{}) error
func Echo(ctx context.Context, reqbody interface{}, rspbody interface{}) error {
    req := reqbody.(*echo.EchoReq)
    rsp := rspbody.(*echo.EchoRsp)
    log.Debugf("Echo ==> req[%v]", req)
    rsp.ErrCode = 0
    rsp.ErrMsg = "success"
    return nil
}
```
