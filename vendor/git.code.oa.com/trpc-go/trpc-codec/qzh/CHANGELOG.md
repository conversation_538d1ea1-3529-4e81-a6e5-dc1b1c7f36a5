# Change Log

## [0.1.5](https://git.woa.com/trpc-go/trpc-codec/tree/qzh/v0.1.5) (2023-09-13)
### Features

- codec: implement safe framer (!671)

## [0.1.4](https://git.woa.com/trpc-go/trpc-codec/tree/qzh/v0.1.4) (2020-09-21)
### Features
- 移除非必要的依赖仓库 
- 增加对包长度的检查，避免非法包导致内存异常

## [0.1.3](https://git.woa.com/trpc-go/trpc-codec/tree/qzh/v0.1.3) (2020-07-10)
### Features
- 实现 qza/pdu 后端调用 DoV2，完整支持指定后端 service+rpcName
- 支持注册函数到指定 qza/pdu service 上，支持指定任意 trpc 支持的序列化类型
- 增加 trace 日志，有问题时便于定位

## [0.1.2](https://git.woa.com/trpc-go/trpc-codec/tree/qzh/v0.1.2) (2020-05-21)
### Features
- 支持 qza/pdu 协议传入 interface 请求体和回包，可用 msg 指定序列化方式
- 支持调用的后端主子命令字相同时，通过被调 (CalleeServiceName) 进行区分
- 修复 client 填充的包头，未赋值到 msg 中的问题

## [0.1.1](https://git.woa.com/trpc-go/trpc-codec/tree/qzh/v0.1.1) (2020-03-12)
### Bug Fixes
- 修复 qza 协议包头强校验 0x8 的问题

## [0.1.0](https://git.woa.com/trpc-go/trpc-codec/tree/qzh/v0.1.0) (2020-01-13)
### Features
- 支持提供 qza/pdu 协议的服务
- 支持调用 qza/pdu 协议的后端
- 支持将 qza/pdu 协议的数值命令字改造成 rpc 模式
