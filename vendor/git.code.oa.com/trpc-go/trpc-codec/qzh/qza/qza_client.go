package qza

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-codec/qzh"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
)

// Client 接口
type Client interface {
	// Do 发起请求
	Do(ctx context.Context, desc qzh.CallDesc, auth qzh.AuthInfo,
		reqBody, rspBody interface{}, opts ...client.Option) error
}

// qzaCli qza Client
type qzaCli struct {
	Client        client.Client
	opts          []client.Option
	verCompatible bool // 这里是为了兼容逻辑，Do 和 DoV2 两个版本的 CalleeServiceName 不同
}

// withVerCompatible 设置接口版本
func (c *qzaCli) withVerCompatible(v bool) {
	c.verCompatible = v
}

// NewClientProxy 新建一个 qzaCli 代理
func NewClientProxy(opts ...client.Option) Client {
	c := &qzaCli{
		Client: client.DefaultClient,
		opts: []client.Option{
			client.WithProtocol("qza"),
		},
	}
	c.opts = append(c.opts, opts...)
	return c
}

// Do 发起 qza 请求，desc 请传入必要参数
func (c *qzaCli) Do(ctx context.Context, desc qzh.CallDesc, auth qzh.AuthInfo,
	reqBody, rspBody interface{}, opts ...client.Option) error {
	ctx, msg := codec.WithCloneMessage(ctx)
	head := Clone(Head(ctx))
	err := head.FillFromCallAndAuth(&desc, &auth)
	if err != nil {
		return err
	}

	msg.WithClientReqHead(head)
	msg.WithClientRPCName(head.GetRPCName())
	msg.WithCalleeApp("qza")
	msg.WithCalleeService("qza")

	defaultName := fmt.Sprintf("qza0x%x_%d", desc.CmdID, desc.SubCmdID)
	// qza.DoV1 里面的设置逻辑，如果是 DoV1 方法，则按照老的规则设置 calleeServiceName，否则按照 DoV2 的规则设置
	if c.verCompatible && len(msg.CallerServiceName()) > 0 {
		fmtName := fmt.Sprintf("%v_qza0x%x_%d", msg.CallerServiceName(), desc.CmdID, desc.SubCmdID)
		msg.WithCalleeServer(fmtName)
		msg.WithCalleeServiceName(fmtName)
	} else if len(desc.CalleeName) > 0 {
		msg.WithCalleeServer(desc.CalleeName)
		msg.WithCalleeServiceName(desc.CalleeName)
	} else {
		msg.WithCalleeServer(defaultName)
		msg.WithCalleeServiceName(defaultName)
	}

	// 设置 calleeMethod，用于上报
	msg.WithCalleeMethod(defaultName)
	if len(desc.CalleeMethod) > 0 {
		msg.WithCalleeMethod(desc.CalleeMethod)
	}

	// 这里申请新切片而不使用 options := append(c.opts, opts...)
	// 是因为当同一个 client 的多个请求同时调用 Do() 时，并发环境下会导致 opts 被覆盖
	options := make([]client.Option, 0, len(c.opts)+len(opts))
	options = append(options, c.opts...)
	options = append(options, opts...)
	return client.DefaultClient.Invoke(ctx, reqBody, rspBody, options...)
}

// Do 当 version 和 cmd 相同，而服务不同时，可以先生成 msg，指定 CalleeServiceName 的名字
func Do(ctx context.Context, desc qzh.CallDesc, auth qzh.AuthInfo,
	reqBody, rspBody interface{}, opts ...client.Option) error {
	proxy := NewClientProxy(opts...)
	// 设置老版本 Do 接口兼容，内部做 ServiceName 兼容逻辑
	c, ok := proxy.(*qzaCli)
	if ok {
		c.withVerCompatible(true)
	}
	return proxy.Do(ctx, desc, auth, reqBody, rspBody)
}

// DoV2 支持 service name 和 method 发起请求，相比 Do 接口，使用这个更好
func DoV2(ctx context.Context, name, method string, desc qzh.CallDesc, auth qzh.AuthInfo,
	reqBody, rspBody interface{}, opts ...client.Option) error {
	if len(desc.CalleeName) == 0 {
		desc.CalleeName = name
	}
	if len(desc.CalleeMethod) == 0 {
		desc.CalleeMethod = method
	}
	proxy := NewClientProxy(opts...)
	return proxy.Do(ctx, desc, auth, reqBody, rspBody)
}
