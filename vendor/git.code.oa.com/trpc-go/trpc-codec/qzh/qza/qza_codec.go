package qza

import (
	"bytes"
	"context"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-codec/qzh"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/transport"
)

func init() {
	codec.Register("qza", DefaultServerCodec, DefaultClientCodec)
	transport.RegisterFramerBuilder("qza", DefaultFrameBuilder)
}

// qza default codec
var (
	DefaultServerCodec  = &ServerCodec{}
	DefaultClientCodec  = &ClientCodec{}
	DefaultFrameBuilder = &FrameBuilder{}
	// DefaultMaxFrameSize 默认帧最大 10M，用户可修改
	DefaultMaxFrameSize = 10 * 1024 * 1024
)

// Head 从 ctx 中获取 HEAD
func Head(ctx context.Context) *HEAD {
	msg := codec.Message(ctx)
	head, ok := msg.ServerReqHead().(*HEAD)
	if !ok {
		return NewHEAD()
	}
	return head
}

// FrameBuilder 数据帧构造器
type FrameBuilder struct {
}

// New 生成一个数据帧
func (fb *FrameBuilder) New(reader io.Reader) transport.Framer {
	return &framer{
		reader: reader,
	}
}

// framer 非变长 qza 头固定长度 106 字节
type framer struct {
	reader io.Reader
	head   [QzaHeadLen]byte
}

// ReadFrame 解析完整的一帧 qza 请求
func (f *framer) ReadFrame() ([]byte, error) {
	num, err := io.ReadFull(f.reader, f.head[:])
	if err != nil {
		return nil, err
	}
	if num != QzaHeadLen {
		return nil, errors.New("read frame msg head num invalid")
	}
	var packLen, packLenHigh uint16
	buffer := new(bytes.Buffer)
	buffer.Write(f.head[5:7])
	if err = binary.Read(buffer, binary.BigEndian, &packLen); err != nil {
		return nil, err
	}
	buffer.Reset()
	buffer.Write(f.head[103:105])
	if err = binary.Read(buffer, binary.BigEndian, &packLenHigh); err != nil {
		return nil, err
	}
	pkgLen := int64(packLen) + int64(65536)*int64(packLenHigh)
	// 包长度非法
	if pkgLen < int64(QzaHeadLen) {
		return nil, fmt.Errorf("read msg pkg len invalid, packLen:%d, pakckLenHigh:%d, pkgLen:%d",
			packLen, packLenHigh, pkgLen)
	}
	// 包长度超过限制，用户可自定义 DefaultMaxFrameSize
	if pkgLen > int64(DefaultMaxFrameSize) {
		return nil, fmt.Errorf("read frame total len %d > %d, too large",
			pkgLen, int64(DefaultMaxFrameSize))
	}

	msg := make([]byte, pkgLen)
	copy(msg, f.head[:])
	num, err = io.ReadFull(f.reader, msg[QzaHeadLen:pkgLen])
	if err != nil {
		return nil, err
	}
	if int64(num+QzaHeadLen) != pkgLen {
		return nil, errors.New("read frame msg body num invalid")
	}

	return msg, nil
}

// IsSafe implements codec.SafeFramer.
func (f *framer) IsSafe() bool {
	return true
}

// ServerCodec 服务端编解码
type ServerCodec struct {
}

// Encode 服务端打包 rspbody 结构体到二进制 回给客户端
func (s *ServerCodec) Encode(msg codec.Msg, rspBody []byte) ([]byte, error) {
	head, ok := msg.ServerRspHead().(*HEAD)
	if !ok {
		head = NewHEAD()
	}

	if e := msg.ServerRspErr(); e != nil {
		if e.Type == errs.ErrorTypeBusiness {
			head.SetRspCode(int16(e.Code))
		} else {
			// int16Max = 32767
			head.SetRspCode(1000 + int16(e.Code))
		}
	}

	return head.Marshal(rspBody)
}

// Decode 服务端收到客户端二进制请求数据解包到 reqbody 结构体
func (s *ServerCodec) Decode(msg codec.Msg, reqBuf []byte) ([]byte, error) {
	if len(reqBuf) < QzaHeadLen {
		return nil, errors.New("server decode req len invalid")
	}

	head := NewHEAD()
	reqBody, err := head.Unmarshal(reqBuf)
	if err != nil {
		return nil, err
	}
	msg.WithServerReqHead(head)
	msg.WithServerRspHead(head)
	msg.WithServerRPCName(head.GetRPCName())
	if serializeType, ok := qzh.SerializeMap[msg.ServerRPCName()]; ok {
		msg.WithSerializationType(serializeType)
	}
	log.Tracef("qza ==> server decode rpcName[%v], serializeType[%v], reqBodyLen[%v]",
		msg.ServerRPCName(), msg.SerializationType(), len(reqBody))

	return reqBody, nil
}

// ClientCodec 客户端编解码
type ClientCodec struct {
	Seq uint32
}

// Encode 客户端打包 reqbody 结构体到二进制数据 发到服务端
func (c *ClientCodec) Encode(msg codec.Msg, reqBody []byte) ([]byte, error) {
	var head *HEAD
	if msg.ClientReqHead() != nil {
		request, ok := msg.ClientReqHead().(*HEAD)
		if !ok {
			return nil, errors.New("client encode req head not HEAD")
		}
		head = request
	} else {
		request, ok := msg.ServerReqHead().(*HEAD)
		if ok {
			head = Clone(request)
		} else {
			head = NewHEAD()
		}
		msg.WithClientReqHead(head)
	}

	if head.packFlow == 0 {
		head.packFlow = atomic.AddUint32(&c.Seq, 1)
	}

	rpcName := msg.ClientRPCName()
	err := head.SetRPCName(rpcName)
	if err != nil {
		return nil, err
	}

	return head.Marshal(reqBody)
}

// Decode 客户端收到服务端二进制回包数据解包到 rspbody 结构体
func (c *ClientCodec) Decode(msg codec.Msg, rspBuf []byte) ([]byte, error) {
	if len(rspBuf) < QzaHeadLen {
		return nil, errors.New("client decode rsp len invalid")
	}

	var head *HEAD
	if msg.ClientRspHead() != nil {
		response, ok := msg.ClientRspHead().(*HEAD)
		if !ok {
			return nil, errors.New("client decode rsp head not HEAD")
		}
		head = response
	} else {
		head = NewHEAD()
		msg.WithClientRspHead(head)
	}

	rspBody, err := head.Unmarshal(rspBuf)
	if err != nil {
		return nil, err
	}
	if head.rspCode != 0 {
		msg.WithClientRspErr(errs.New(int(head.rspCode), head.GetRPCName()))
	}

	return rspBody, nil
}
