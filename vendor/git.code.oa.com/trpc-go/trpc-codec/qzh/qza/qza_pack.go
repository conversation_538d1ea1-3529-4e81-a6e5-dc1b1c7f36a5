// Package qza qzone 协议后端 head 类型，qza 协议
package qza

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"

	"git.code.oa.com/trpc-go/trpc-codec/qzh"
)

const (
	// QzaHeadLen 非变长 qza 头固定长度
	QzaHeadLen int = 106
)

// DETAILINFO qza 协议详情结构体，To save the memory, swap the order of the struct fields.
type DETAILINFO struct {
	// 版本号
	version int8
	// 平台类型	平台会针对于此字段统计
	typePlatform uint8
	// 业务请求的子命令字	-- 4 byte
	reqCmdItem int16
	// 应用类型	平台会针对于此字段统计
	typeApp uint32
	// 这个填各个业务 pskey 的 domain id  ---如空间的 domain id 为 5
	typeSource uint32
	// 保留字段
	reserveBuf [23]byte
	// 保留字段 2 -- 32 byte
	reserveBuf2 [9]byte
	// 业务请求的 IP
	reqIP uint32
	// 业务验证登陆态的 id
	ptloginID uint32
	// 访问者的客户端 IP
	clientIP uint32
	// 被访问者的 UIN
	hostUin uint32
	// 访问者的 UIN
	clientUin uint32
	// 扩展数据	通过 skey 校验登陆态的地方会使用这里的前 11 个字节存储 skey 相关信息
	extend [25]byte
	// 变长附加信息长度
	extendLen uint8
	// 业务登陆态 key 校验的类型
	authType uint8
	// 登陆状态 0xffff-未验证，0-成功 1-失败
	ptloginState uint16
	// 包长高位 整个包长度==_pack_len + 65536 * _detail_info._pack_len_high;
	packLenHigh uint16
}

func (detailInfo *DETAILINFO) write(mProtoBytesBuffer io.Writer) error {
	// 需要依赖写入 buf 的字段
	bufParam := []interface{}{
		detailInfo.version,
		detailInfo.typePlatform,
		detailInfo.typeApp,
		detailInfo.typeSource,
		detailInfo.reserveBuf,
		detailInfo.reqIP,
		detailInfo.ptloginID,
		detailInfo.authType,
		detailInfo.reserveBuf2,
		detailInfo.ptloginState,
		detailInfo.reqCmdItem,
		detailInfo.clientIP,
		detailInfo.hostUin,
		detailInfo.clientUin,
		detailInfo.extend,
		detailInfo.packLenHigh,
		detailInfo.extendLen,
	}
	if err := bufWrite(mProtoBytesBuffer, bufParam); err != nil {
		return err
	}
	return nil
}

func isSetInkey(authType uint8) bool {
	return (authType == qzh.EnumAuthTypeSvr) || (authType == qzh.EnumAuthTypeWeb)
}
func isSetExkey(authType uint8) bool {
	return (authType != qzh.EnumAuthTypeWeb) && (authType != qzh.EnumAuthTypeSvr)
}

// IsSetExkey 判断 qza 包头内登录态是否使用 exkey
func IsSetExkey(authType uint8) bool {
	return isSetExkey(authType)
}

// HEAD qza 完整包头结构体
type HEAD struct {
	// 请求详细
	detailInfo DETAILINFO
	// 版本号
	version int8
	// 请求的命令字
	reqCmd int16
	// 流水号
	packFlow uint32
	// 整个包长度的低位 整个包长度==_pack_len + 65536 * _detail_info._pack_len_high;
	packLen uint16
	// 返回码
	rspCode int16
	// 变长包头部分
	extend []byte
}

// NewHEAD 新建 qza 包，并初始化
func NewHEAD() *HEAD {
	var pkg = new(HEAD)
	pkg.Clean()
	return pkg
}

// Clone 深度拷贝 qza 包头内容
func Clone(rawHead *HEAD) *HEAD {
	var newHead = new(HEAD)
	newHead.Clean()
	newHead.version = rawHead.version
	newHead.packLen = rawHead.packLen
	newHead.rspCode = rawHead.rspCode
	newHead.detailInfo = rawHead.detailInfo
	newHead.packFlow = rawHead.packFlow
	newHead.reqCmd = rawHead.reqCmd
	newHead.extend = append(newHead.extend, rawHead.extend...)
	return newHead
}

// Check 根据切片数组校验是否是 qza 包头
func Check(buf []byte) (int, error) {
	bufLen := uint32(len(buf))
	if bufLen < 106 {
		return 0, nil
	}
	var packLen, packLenHigh uint16
	mProtoBytesBuffer := new(bytes.Buffer)
	mProtoBytesBuffer.Write(buf[5:7])
	if e := binary.Read(mProtoBytesBuffer, binary.BigEndian, &packLen); e != nil {
		return 0, qzh.GetQzErrorFromError(qzh.QzbdErrPackUnpack, e)
	}
	mProtoBytesBuffer.Reset()
	mProtoBytesBuffer.Write(buf[103:105])
	if e := binary.Read(mProtoBytesBuffer, binary.BigEndian, &packLenHigh); e != nil {
		return 0, qzh.GetQzErrorFromError(qzh.QzbdErrPackUnpack, e)
	}
	pkgLen := uint32(packLen) + uint32(65536)*uint32(packLenHigh)
	if bufLen < pkgLen {
		return 0, nil
	}
	return int(pkgLen), nil
}

// AppProtocol 返回 qza 协议名称
func (pkg *HEAD) AppProtocol() string {
	return "qza"
}

// Marshal qza 协议编码打包
func (pkg *HEAD) Marshal(body []byte) ([]byte, error) {
	// 先设置总包长
	pkg.SetPackLen(uint32(pkg.GetHeadLen()) + uint32(len(body)))
	// buffer 自动拓展大小
	mProtoBytesBuffer := new(bytes.Buffer)
	bufParam := []interface{}{
		pkg.version,
		pkg.packFlow,
		pkg.packLen,
		pkg.reqCmd,
		pkg.rspCode,
	}
	// qza 头部字段
	if err := bufWrite(mProtoBytesBuffer, bufParam); err != nil {
		return nil, err
	}
	// detail info struct
	if err := pkg.detailInfo.write(mProtoBytesBuffer); err != nil {
		return nil, err
	}
	// qza 最小长度
	if mProtoBytesBuffer.Len() < QzaHeadLen {
		return nil, qzh.GetQzErrorFromMsg(qzh.QzbdErrPackPack,
			fmt.Sprintf("Marshal qza bufLen[%v] < 106", mProtoBytesBuffer.Len()))
	}
	// qza 变长包头
	if pkg.extend != nil {
		if err := binary.Write(mProtoBytesBuffer, binary.BigEndian, pkg.extend[:pkg.GetExtendLen()]); err != nil {
			return nil, err
		}
	}
	// 业务 body
	if err := binary.Write(mProtoBytesBuffer, binary.BigEndian, body); err != nil {
		return nil, err
	}
	return mProtoBytesBuffer.Bytes(), nil
}

// FillFromCallAndAuth 从 call_desc 和 auth_info 中填充 qza 包头的基础信息
func (pkg *HEAD) FillFromCallAndAuth(desc *qzh.CallDesc, info *qzh.AuthInfo) error {
	pkg.SetPackFlow(qzh.CreateFlow())
	pkg.SetCmd(int16(desc.CmdID), int16(desc.SubCmdID))
	pkg.SetReqSource(info.TypePlatform, info.TypeApp, info.TypeSource)
	pkg.SetReqIP(info.ServerIP)
	pkg.SetPtloginID(info.PtloginID)
	pkg.SetAuthType(uint8(info.EnumAuthType))
	pkg.SetHostUin(info.Uin)
	pkg.SetClient(info.ClientIP, info.Uin)
	if isSetInkey(pkg.GetAuthType()) {
		if pkg.SetInternalKey(info.Key) != 0 {
			return qzh.QzError{RspCode: qzh.QzbdErrPackPack, Msg: "pack -- set internal key error"}
		}
	}
	if isSetExkey(pkg.GetAuthType()) {
		if pkg.SetExternalKey(info.Exkey) != 0 {
			return qzh.QzError{RspCode: qzh.QzbdErrPackPack, Msg: "pack -- set external key error"}
		}
	}
	return nil
}

// Unmarshal 严格按照字段顺序解包
func (pkg *HEAD) Unmarshal(buf []byte) ([]byte, error) {
	if len(buf) < 106 {
		return nil, qzh.GetQzErrorFromMsg(qzh.QzbdErrPackUnpack, "unpack error, the packet len is too small")
	}
	pkg.version = int8(buf[0])
	pkg.packFlow = binary.BigEndian.Uint32(buf[1:5])
	pkg.packLen = binary.BigEndian.Uint16(buf[5:7])
	pkg.reqCmd = int16(binary.BigEndian.Uint16(buf[7:9]))
	pkg.rspCode = int16(binary.BigEndian.Uint16(buf[9:11]))

	pkg.detailInfo.version = int8(buf[11])
	pkg.detailInfo.typePlatform = uint8(buf[12])
	pkg.detailInfo.typeApp = binary.BigEndian.Uint32(buf[13:17])
	pkg.detailInfo.typeSource = binary.BigEndian.Uint32(buf[17:21])
	mProtoBytesBuffer := bytes.NewBuffer(buf[21:44])
	if err := binary.Read(mProtoBytesBuffer, binary.BigEndian, pkg.detailInfo.reserveBuf[:]); err != nil {
		return nil, qzh.GetQzErrorFromError(qzh.QzbdErrPackUnpack, err)
	}
	pkg.detailInfo.reqIP = binary.BigEndian.Uint32(buf[44:48])
	pkg.detailInfo.ptloginID = binary.BigEndian.Uint32(buf[48:52])
	pkg.detailInfo.authType = uint8(buf[52])
	mProtoBytesBuffer = bytes.NewBuffer(buf[53:62])
	if err := binary.Read(mProtoBytesBuffer, binary.BigEndian, pkg.detailInfo.reserveBuf2[:]); err != nil {
		return nil, qzh.GetQzErrorFromError(qzh.QzbdErrPackUnpack, err)
	}
	pkg.detailInfo.ptloginState = binary.BigEndian.Uint16(buf[62:64])
	pkg.detailInfo.reqCmdItem = int16(binary.BigEndian.Uint16(buf[64:66]))
	pkg.detailInfo.clientIP = binary.BigEndian.Uint32(buf[66:70])
	pkg.detailInfo.hostUin = binary.BigEndian.Uint32(buf[70:74])
	pkg.detailInfo.clientUin = binary.BigEndian.Uint32(buf[74:78])
	mProtoBytesBuffer = bytes.NewBuffer(buf[78:103])
	if err := binary.Read(mProtoBytesBuffer, binary.BigEndian, pkg.detailInfo.extend[:]); err != nil {
		return nil, qzh.GetQzErrorFromError(qzh.QzbdErrPackUnpack, err)
	}
	pkg.detailInfo.packLenHigh = binary.BigEndian.Uint16(buf[103:105])
	pkg.detailInfo.extendLen = uint8(buf[105])
	mProtoBytesBuffer = bytes.NewBuffer(buf[106:pkg.GetBodyStartIndex()])
	pkg.extend = make([]byte, mProtoBytesBuffer.Len())
	copy(pkg.extend, mProtoBytesBuffer.Bytes())
	return buf[pkg.GetBodyStartIndex():], nil
}

// Clean qza 包头初始化
func (pkg *HEAD) Clean() {
	pkg.version = 0x8
	pkg.detailInfo.Clean()
}

// Clean detailInfo 初始化
func (detailInfo *DETAILINFO) Clean() {
	detailInfo.ptloginState = 0xFFFF
}

// SetVersion 设置 qza 协议版本
func (pkg *HEAD) SetVersion(v int8) {
	pkg.version = v
}

// GetVersion 获取 qza 协议版本
func (pkg *HEAD) GetVersion() int8 {
	return pkg.version
}

// SetPackFlow 设置 qza 包序列号
func (pkg *HEAD) SetPackFlow(pf uint32) {
	pkg.packFlow = pf
}

// GetPackFlow 获取 qza 包序列号
func (pkg *HEAD) GetPackFlow() uint32 {
	return pkg.packFlow
}

// SetPackLen 设置 qza 包总长度
func (pkg *HEAD) SetPackLen(packLen uint32) {
	pkg.packLen = uint16(packLen % 65536)
	pkg.detailInfo.packLenHigh = uint16(packLen / 65536)
}

// GetPackLen 获取 qza 包总长度
func (pkg *HEAD) GetPackLen() uint32 {
	return uint32(pkg.packLen) + uint32(65536)*uint32(pkg.detailInfo.packLenHigh)
}

// SetCmd 设置 qza 主子命令字
func (pkg *HEAD) SetCmd(cmd, cmdItem int16) {
	pkg.reqCmd = cmd
	pkg.detailInfo.reqCmdItem = cmdItem
}

// GetCmd 获取 qza 命令字
func (pkg *HEAD) GetCmd() (int16, int16) {
	return pkg.reqCmd, pkg.detailInfo.reqCmdItem
}

// SetRspCode 设置 qza 包内返回码
func (pkg *HEAD) SetRspCode(rspCode int16) {
	pkg.rspCode = rspCode
}

// GetRspCode 获取 qza 包内返回码
func (pkg *HEAD) GetRspCode() int16 {
	return pkg.rspCode
}

// SetReqSource 设置 qza 登录请求来源
func (pkg *HEAD) SetReqSource(platform uint8, app, source uint32) {
	pkg.detailInfo.typePlatform = platform
	pkg.detailInfo.typeApp = app
	pkg.detailInfo.typeSource = source
}

// GetReqSource 获取 qza 登录请求来源
func (pkg *HEAD) GetReqSource() (uint8, uint32, uint32) {
	return pkg.detailInfo.typePlatform, pkg.detailInfo.typeApp, pkg.detailInfo.typeSource
}

// GetExtend 获取拓展字段内容
func (pkg *HEAD) GetExtend() [25]byte {
	return pkg.detailInfo.extend
}

// GetExtendSize 获取拓展字段实际内容大小
func (pkg *HEAD) GetExtendSize() int {
	return len(pkg.detailInfo.extend)
}

// SetExtendLen 设置变长的拓展长度
func (pkg *HEAD) SetExtendLen(eLen uint8) {
	pkg.detailInfo.extendLen = eLen
}

// GetExtendLen 获取变长的拓展长度
func (pkg *HEAD) GetExtendLen() uint8 {
	return pkg.detailInfo.extendLen
}

// SetPtloginState 设置Ptlogin的状态
func (pkg *HEAD) SetPtloginState(pState uint16) {
	pkg.detailInfo.ptloginState = pState
}

// GetPtloginState 获取Ptlogin的状态
func (pkg *HEAD) GetPtloginState() uint16 {
	return pkg.detailInfo.ptloginState
}

// GetHeadLen 写死 106 字节，因为 golang 会做结构体对齐，106 不是对齐值
func (pkg *HEAD) GetHeadLen() int16 {
	return 106 + int16(pkg.detailInfo.extendLen)
}

// GetBodyStartIndex 使用网络传输的数据流，通过该函数返回的 index，来获取 jce 数据在 []byte 中的位置
func (pkg *HEAD) GetBodyStartIndex() int16 {
	return pkg.GetHeadLen()
}

// SetClient 设置客户端请求信息
func (pkg *HEAD) SetClient(clientIP, clientUin uint32) {
	pkg.detailInfo.clientIP = clientIP
	pkg.detailInfo.clientUin = clientUin
}

// GetClient 获取客户端请求信息
func (pkg *HEAD) GetClient() (uint32, uint32) {
	return pkg.detailInfo.clientIP, pkg.detailInfo.clientUin
}

// SetHostUin 设置被访问者 uin
func (pkg *HEAD) SetHostUin(hostUin uint32) {
	pkg.detailInfo.hostUin = hostUin
}

// GetHostUin 获取被访问者 uin
func (pkg *HEAD) GetHostUin() uint32 {
	return pkg.detailInfo.hostUin
}

// SetReqIP 设置来源请求 IP
func (pkg *HEAD) SetReqIP(reqIP uint32) {
	pkg.detailInfo.reqIP = reqIP
}

// GetReqIP 获取来源请求 IP
func (pkg *HEAD) GetReqIP() uint32 {
	return pkg.detailInfo.reqIP
}

// SetPtloginID 设置登录域 id
func (pkg *HEAD) SetPtloginID(ptloginID uint32) {
	pkg.detailInfo.ptloginID = ptloginID
}

// GetPtloginID 获取登录域 id
func (pkg *HEAD) GetPtloginID() uint32 {
	return pkg.detailInfo.ptloginID
}

// SetAuthType 设置登录态校验类型
func (pkg *HEAD) SetAuthType(authType uint8) {
	pkg.detailInfo.authType = authType
}

// GetAuthType 获取登录态校验类型
func (pkg *HEAD) GetAuthType() uint8 {
	return pkg.detailInfo.authType
}

// SetInternalKey 设置头部信息中的 key，key 最长不能超过 ExtendSize()
func (pkg *HEAD) SetInternalKey(key string) int32 {
	var _len = len(key)
	if _len+1 > pkg.GetExtendSize() {
		return -1
	}
	pkg.detailInfo.extend[0] = byte(_len)
	copy(pkg.detailInfo.extend[1:], key[:])
	return 0
}

// GetInternalKey 获取头部信息中的 key
func (pkg *HEAD) GetInternalKey() string {
	_len := pkg.detailInfo.extend[0]
	return string(pkg.detailInfo.extend[1 : 1+_len])
}

// SetExternalKey 设置变长包头中的 key，key 最长不能超过 254
func (pkg *HEAD) SetExternalKey(key string) int32 {
	var mLen = len(key)
	if mLen == 0 {
		return 0
	} else if mLen > 254 {
		return -1
	}
	pkg.SetExtendLen(0)
	pkg.extend = make([]byte, 255)
	pkg.extend[0] = byte(mLen)
	copy(pkg.extend[1:mLen+1], key[:])
	pkg.SetExtendLen(uint8(mLen + 1))
	return 0
}

// GetExternalKey 从 ctx 的拓展头部字段转换成切片类型来获取
func (pkg *HEAD) GetExternalKey() string {
	if len(pkg.extend) < 1 || len(pkg.extend) > 255 {
		return ""
	}
	var mLen = pkg.extend[0]
	var lenExtend = pkg.GetExtendLen()
	if mLen+1 > lenExtend {
		return ""
	}
	return string(pkg.extend[1:lenExtend])
}

// SetSKey skey 一般为 WEB 层程序调用，如 cgi
func (pkg *HEAD) SetSKey(skey string) {
	pkg.SetInternalKey(skey)
}

// GetSKey 获取 SKey
func (pkg *HEAD) GetSKey() string {
	return pkg.GetInternalKey()
}

// SetSvrKey svr key 一般为拿不到任何其它 key 的程序调用
func (pkg *HEAD) SetSvrKey(skey string) {
	pkg.SetInternalKey(skey)
}

// GetSvrKey 获取 svr key
func (pkg *HEAD) GetSvrKey() string {
	return pkg.GetInternalKey()
}

// Set3GKey 3g key 给手机无线 mqzone 使用的 key
func (pkg *HEAD) Set3GKey(a8 string) {
	pkg.SetExternalKey(a8)
}

// Get3GKey 获取 3g key
func (pkg *HEAD) Get3GKey() string {
	return pkg.GetExternalKey()
}

// SetLSKey 设置弱登陆态的 key
func (pkg *HEAD) SetLSKey(lskey string) {
	pkg.SetExternalKey(lskey)
}

// GetLSKey 获取 LS key
func (pkg *HEAD) GetLSKey() string {
	return pkg.GetExternalKey()
}

// SetA8Key A8 key 给手机无线 mqzone 使用的 key
func (pkg *HEAD) SetA8Key(a8 string) {
	pkg.SetExternalKey(a8)
}

// GetA8Key 获取 A8 key
func (pkg *HEAD) GetA8Key() string {
	return pkg.GetExternalKey()
}

// SetA2Key A2 key 给手机无线 mqzone 使用的 key
func (pkg *HEAD) SetA2Key(a2 string) {
	pkg.SetExternalKey(a2)
}

// GetA2Key 获取 A2 key
func (pkg *HEAD) GetA2Key() string {
	return pkg.GetExternalKey()
}

// SetSTKey ST key
func (pkg *HEAD) SetSTKey(st string) {
	pkg.SetExternalKey(st)
}

// GetSTKey 获取 STkey
func (pkg *HEAD) GetSTKey() string {
	return pkg.GetExternalKey()
}

// SetPSKey 设置强校验的 PSKEY
func (pkg *HEAD) SetPSKey(ps string) {
	pkg.SetExternalKey(ps)
}

// GetPSKey 获取强校验的 PSKEY
func (pkg *HEAD) GetPSKey() string {
	return pkg.GetExternalKey()
}

/**
 * 设置/获取客户端 IPv6 地址
 */
func (pkg *HEAD) isClientIPv6() bool {
	var zero [16]byte
	return !bytes.Equal(pkg.detailInfo.reserveBuf[16:], zero[:7]) ||
		!bytes.Equal(pkg.detailInfo.reserveBuf2[:], zero[7:])
}

// SetClientIPv6 设置 ipv6 地址
func (pkg *HEAD) SetClientIPv6(ip []byte) int32 {
	if ip == nil {
		return -1
	}
	copy(pkg.detailInfo.reserveBuf[16:], ip[:7])
	copy(pkg.detailInfo.reserveBuf2[:], ip[7:])
	return 0
}

// GetClientIPv6 获取 ipv6 地址，大小端转换在解包中完成
func (pkg *HEAD) GetClientIPv6(ip *[]byte) int32 {
	if ip == nil || !pkg.isClientIPv6() {
		return -1
	}
	copy((*ip)[:7], pkg.detailInfo.reserveBuf[16:])
	copy((*ip)[7:], pkg.detailInfo.reserveBuf2[:])
	return 0
}

// SetRPCName 从 rpcName 设置包头中的命令字
func (pkg *HEAD) SetRPCName(rpcName string) error {
	var cmd int16
	var subCmd int16
	num, err := fmt.Sscanf(rpcName, "/0x%x/%d", &cmd, &subCmd)
	if num != 2 || err != nil {
		return fmt.Errorf("invalid qza rpc name, err[%v]", err)
	}
	pkg.SetCmd(cmd, subCmd)
	return nil
}

// GetRPCName 拿到符合框架格式的 rpcName
func (pkg *HEAD) GetRPCName() string {
	return fmt.Sprintf("/0x%x/%d", pkg.reqCmd, pkg.detailInfo.reqCmdItem)
}

func bufWrite(buf io.Writer, params []interface{}) error {
	for _, v := range params {
		if err := binary.Write(buf, binary.BigEndian, v); err != nil {
			return err
		}
	}
	return nil
}
