// Package matcher primarily abstracts the RuleItem from debuglog into transformation matching rules.
// Users can configure RuleItem, and the system generates the corresponding matcher for matching.
package matcher

// MatchType define the routine for matcher to match the method.
type MatchType = string

var (
	// MatchTypeWhole means matcher matches the entire string.
	MatchTypeWhole MatchType = "whole"
	// MatchTypeRegex means matcher matches the string using a regular expression.
	MatchTypeRegex MatchType = "regex"
	// MatchTypePrefix means matcher matches the string prefix.
	MatchTypePrefix MatchType = "prefix"
	// MatchTypeSuffix means matcher matches the string suffix.
	MatchTypeSuffix MatchType = "suffix"
)

// StrPtr return the pointer to string s.
// RuleItem and matcher are often checked for configuration by determining if their pointers are nil.
// Therefore, the StrPtr() function is provided to simplify this operation.
func StrPtr(s string) *string {
	return &s
}
