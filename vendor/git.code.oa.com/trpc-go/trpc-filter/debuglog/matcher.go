package debuglog

import (
	"fmt"
	"regexp"
	"strings"

	imatcher "git.code.oa.com/trpc-go/trpc-filter/debuglog/internal/matcher"
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

type methodMatcherFunc = func(method string) bool
type errMatcherFunc = func(err error) bool

func noopMethodMatcher(_ string) bool {
	return true
}

func noopErrMatcher(_ error) bool {
	return true
}

// matcher uses MethodMatcher and ErrMatcher to match the rpc invoke.
type matcher struct {
	methodMatcher methodMatcherFunc
	errMatcher    errMatcherFunc
}

// build uses RuleItem to build the matcher.
func build(e RuleItem) *matcher {
	m := &matcher{}
	m.methodMatcher = buildMethodMatcher(e.Method, e.MatchType)
	m.errMatcher = buildErrMatcher(e.ErrIsNil, e.Retcode)
	return m
}

func initNoopMatcher() *matcher {
	return &matcher{
		methodMatcher: noopMethodMatcher,
		errMatcher:    noopErrMatcher,
	}
}

func buildMethodMatcher(method *string, matchType *imatcher.MatchType) methodMatcherFunc {
	// noop.
	// Notice: When method == nil and matchType != nil, it will enter this case.
	// This might lead to subtle situations.
	if method == nil && matchType == nil ||
		method == nil && matchType != nil {
		return noopMethodMatcher
	}

	// default: whole.
	if method != nil && matchType == nil {
		return func(meth string) bool {
			return meth == *method
		}
	}

	switch *(matchType) {
	case imatcher.MatchTypeWhole:
		return func(meth string) bool {
			return meth == *method
		}

	case imatcher.MatchTypeRegex:
		re, err := regexp.Compile(*method)
		if err != nil {
			panic(fmt.Sprintf("method and matchTypeRegex are set incorrectly: %v", err))
		}
		return func(method string) bool {
			return re.MatchString(method)
		}

	case imatcher.MatchTypePrefix:
		prefix := *method
		return func(method string) bool {
			return strings.HasPrefix(method, prefix)
		}

	case imatcher.MatchTypeSuffix:
		suffix := *method
		return func(method string) bool {
			return strings.HasSuffix(method, suffix)
		}

	default:
		panic("matchType is not set correctly")
	}
}

func buildErrMatcher(errIsNil *bool, retcode *int) errMatcherFunc {
	if errIsNil != nil && retcode != nil {
		panic("errIsNil and retcode cannot be set simultaneously")
	}

	if errIsNil != nil {
		return func(err error) bool {
			return (err == nil) == *errIsNil
		}
	}

	if retcode != nil {
		return func(err error) bool {
			return errs.Code(err) == *retcode
		}
	}

	return noopErrMatcher
}
