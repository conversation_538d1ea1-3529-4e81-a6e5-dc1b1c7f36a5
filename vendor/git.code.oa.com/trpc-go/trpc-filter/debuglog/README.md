# debuglog

[![BK Pipelines Status](https://api.bkdevops.qq.com/process/api/external/pipelines/projects/pcgtrpcproject/p-9219a8e533ac4248bcf98670069ec250/badge?X-DEVOPS-PROJECT-ID=pcgtrpcproject)](http://devops.oa.com:/ms/process/api-html/user/builds/projects/pcgtrpcproject/pipelines/p-9219a8e533ac4248bcf98670069ec250/latestFinished?X-DEVOPS-PROJECT-ID=pcgtrpcproject)[![Coverage](https://tcoverage.woa.com/api/getCoverage/getTotalImg/?pipeline_id=p-9219a8e533ac4248bcf98670069ec250)](http://macaron.oa.com/api/coverage/getTotalLink/?pipeline_id=p-9219a8e533ac4248bcf98670069ec250)[![GoDoc](https://img.shields.io/badge/API%20Docs-GoDoc-green)](http://godoc.oa.com/git.code.oa.com/trpc-go/trpc-filter/debuglog)
所有接口请求自动打印 debug 日志

## 使用说明

- 增加 import

```go
import (
   _ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
)
```

- TRPC 框架配置文件。其中 debuglog 可以替换
  - debuglog：默认打印
  - simpledebuglog：不打印包体
  - pjsondebuglog：格式化 json 打印包体
  - jsondebuglog：压缩型 json 打印包体
  
```yaml
server:
 ...
 filter:
  ...
  - debuglog

client:
 ...
 filter:
  ...
  - debuglog
```

流式服务只支持 debuglog，如果需要其他的输出格式请参考详细配置，流式服务的配置如下

```yaml
server:
  ...
  stream_filter:
    ...
    - debuglog

client:
  ...
  stream_filter:
    ...
    - debuglog
```

## 详细配置，可选

```yaml
plugins:
  tracing:
    debuglog:
      log_type: simple # 默认日志打印方式
      err_log_level: error # 错误日志打印级别
      nil_log_level: info # 非错误日志打印级别
      server_log_type: prettyjson # server 日志打印方式，会覆盖 log_type 的设定
      client_log_type: json # client 日志打印方式，会覆盖 log_type 的设定
      enable_color: true # 开启日志显示不同颜色   默认 false
      include: # 包含的匹配规则，当配置不为空时，exclude 选项将无效。
          # match_type 不设置时，默认为全字匹配，v0.1.13 后生效
          # method 和 retcode 选项同时指定时，都命中才包含。
        - method: /trpc.app.server.service/methodA 
          retcode: 51
          # method 和 err_is_nil 同时指定时，都命中才包含。
        - method: /trpc.app.server.service/methodB
          err_is_nil: true # 此选项一般与 method 进行搭配使用
                           # err_is_nil 主要用于解决 errcode 与 err 多对一的映射问题
                           # 详情可见 https://git.woa.com/trpc-go/trpc-filter/issues/82
                           # 详情可见 https://git.woa.com/trpc-go/trpc-filter/-/merge_requests/370
          # 只按方法名包含
        - method: /trpc.app.server.service/methodC
          # 只按正则表达式包含
        - method: ^/trpc\.app\.server\.service/methodD.*
          match_type: regex # v0.1.13 后生效。
          # 只按前缀包含
        - method: /trpc.app.server.service/methodE
          match_type: prefix # v0.1.13 后生效。
          # 只按后缀包含
        - method: methodF
          match_type: suffix # v0.1.13 后生效。
          # 只按错误码包含
        - retcode: 52
          # 同时设置 retcode 和 err_is_nil 来对 ErrServerNoResponse / ErrClientNoResponse 进行处理
          # 请注意，不要设置 retcode != 0 && err_is_nil == true，没有错误但是错误码不为 0 的情况并不存在
        - err_is_nil: false
          retcode: 0
      exclude: # 忽略打印的匹配规则，当 include 选项不为空时，该项配置不生效。
          # method 和 retcode 选项同时指定时，都命中才排除。
        - method: /trpc.app.server.service/methodD
          retcode: 53
          # method 和 err_is_nil 同时指定时，都命中才排除。
        - method: /trpc.app.server.service/methodH
          err_is_nil: false # 此选项一般与 method 进行搭配使用
                            # err_is_nil 主要用于解决 errcode 与 err 多对一的映射问题
                            # 详情可见 https://git.woa.com/trpc-go/trpc-filter/issues/82
                            # 详情可见 https://git.woa.com/trpc-go/trpc-filter/-/merge_requests/370
          # 只按方法名排除
        - method: /trpc.app.server.service/methodI
          # 只按正则表达式排除 
        - method: ^/trpc\.app\.server\.service/methodJ.*
          match_type: regex # v0.1.13 后生效。
          # 只按前缀排除
        - method: /trpc.app.server.service/methodK
          match_type: prefix # v0.1.13 后生效。
          # 只按后缀排除
        - method: methodL
          match_type: suffix # v0.1.13 后生效。
          # 只按错误码排除
        - retcode: 54
          # 同时设置 retcode 和 err_is_nil 来对 ErrServerNoResponse / ErrClientNoResponse 进行处理
          # 请注意，不要设置 retcode != 0 && err_is_nil == true，没有错误但是错误码不为 0 的情况并不存在
        - err_is_nil: false
          retcode: 0
```

- 注意，使用插件配置时，filter 必须为`debuglog`，否则插件的配置不会生效
- log_type/server_log_type/client_log_type的可填项为：
  - default：对应 debuglog，默认打印
  - simple：对应 simpledebuglog，不打印包体
  - prettyjson：对应 pjsondebuglog，格式化 json 打印包体
  - json：对应 jsondebuglog，压缩型 json 打印包体
- err_log_level 的可填项为：
  - error，默认打印
  - debug
  - info
  - warning
  - fatal
- nil_log_level 的可填项为：
  - debug，默认打印
  - info
  - warning
  - error
  - fatal
- include.method 和 exclude.method: 是根据 msg.ClientRPCName 或 msg.ServerRPCName 进行过滤的，
通常是生成的桩代码 xxx._ServiceDesc 里面的对应的 method 字段。

## 自定义打印方法，可选

- 有时候业务需要自定义请求回包的打印方法，可以通过自己注册自定义的打印方法来实现
- 注意，使用自定义打印方式时，filter 必须为`debuglog`，并会覆盖插件配置中的配置

```go
import (
    "context"
    "fmt"
    "git.code.oa.com/trpc-go/trpc-go/filter"
    "git.code.oa.com/trpc-go/trpc-filter/debuglog"
)

func main() {
    // 自定义 Server 打印函数
    debugServerLogFunc := func(ctx context.Context, req, rsp interface{}) string {
        return fmt.Sprintf(", req:%+v, rsp:%+v, this is server log test", req, rsp)
    }
    // 自定义 Client 打印函数
    debugClientLogFunc := func(ctx context.Context, req, rsp interface{}) string {
        return fmt.Sprintf(", req:%+v, rsp:%+v, this is client log test", req, rsp)
    }
    // 注册 filter
    filter.Register("debuglog",
        debuglog.ServerFilter(debuglog.WithLogFunc(debugServerLogFunc)),
        debuglog.ClientFilter(debuglog.WithLogFunc(debugClientLogFunc)))

    s := trpc.NewServer()

    pb.RegisterHttp_helloworldService(s, &Http_helloworldServerImpl{})
    if err := s.Serve(); err != nil {
        log.Fatal(err)
    }
}
```
