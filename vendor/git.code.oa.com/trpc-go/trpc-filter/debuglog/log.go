// Package debuglog is a logger trpc-filter to printing server/client RPC calls.
package debuglog

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

func init() {
	filter.Register(pluginName, ServerFilter(), ClientFilter())
	filter.Register(
		"simpledebuglog", ServerFilter(WithLogFunc(SimpleLogFunc)),
		ClientFilter(WithLogFunc(SimpleLogFunc)),
	)
	filter.Register(
		"pjsondebuglog", ServerFilter(WithLogFunc(PrettyJSONLogFunc)),
		ClientFilter(WithLogFunc(PrettyJSONLogFunc)),
	)
	filter.Register(
		"jsondebuglog", ServerFilter(WithLogFunc(JSONLogFunc)),
		ClientFilter(WithLogFunc(JSONLogFunc)),
	)
}

// options are the configuration options.
type options struct {
	logFunc         LogFunc
	errLogLevelFunc LogLevelFunc
	nilLogLevelFunc LogLevelFunc
	enableColor     bool
	include         []*RuleItem
	exclude         []*RuleItem
	includeMatchers []*matcher
	excludeMatchers []*matcher
}

// passed is the filtering result.
// When it is true, will go to the logging process.
func (o *options) passed(method string, err error) bool {
	// Calculation of the include rule.
	for _, in := range o.includeMatchers {
		if in.methodMatcher(method) && in.errMatcher(err) {
			return true
		}
	}

	// If the include rule is configured, the exclude rule will not be matched.
	if len(o.includeMatchers) > 0 {
		return false
	}

	// Calculation of the exclude rule.
	for _, ex := range o.excludeMatchers {
		if ex.methodMatcher(method) && ex.errMatcher(err) {
			return false
		}
	}

	return true
}

// Option sets the options.
type Option func(*options)

// LogFunc is the struct print method function.
type LogFunc func(ctx context.Context, req, rsp interface{}) string

// LogLevelFunc specifies the log level.
type LogLevelFunc func(ctx context.Context, format string, args ...interface{})

// WithLogFunc sets the print body method.
func WithLogFunc(f LogFunc) Option {
	return func(opts *options) {
		opts.logFunc = f
	}
}

// WithErrLogLevelFunc sets the log level print method.
func WithErrLogLevelFunc(f LogLevelFunc) Option {
	return func(opts *options) {
		opts.errLogLevelFunc = f
	}
}

// WithNilLogLevelFunc sets the non-error log level print method.
func WithNilLogLevelFunc(f LogLevelFunc) Option {
	return func(opts *options) {
		opts.nilLogLevelFunc = f
	}
}

// WithInclude sets the include options.
func WithInclude(in *RuleItem) Option {
	return func(opts *options) {
		opts.include = append(opts.include, in)
	}
}

// WithExclude sets the exclude options.
func WithExclude(ex *RuleItem) Option {
	return func(opts *options) {
		opts.exclude = append(opts.exclude, ex)
	}
}

// WithEnableColor enable multiple color log output.
func WithEnableColor(enable bool) Option {
	return func(opts *options) {
		opts.enableColor = enable
	}
}

// logLevel is log level.
type logLevel = string

var (
	traceLevel   logLevel = "trace"
	debugLevel   logLevel = "debug"
	warningLevel logLevel = "warning"
	infoLevel    logLevel = "info"
	errorLevel   logLevel = "error"
	fatalLevel   logLevel = "fatal"
)

// LogContextfFuncs is a map of methods for logging at different levels.
var LogContextfFuncs = map[string]func(ctx context.Context, format string, args ...interface{}){
	traceLevel:   log.TraceContextf,
	debugLevel:   log.DebugContextf,
	warningLevel: log.WarnContextf,
	infoLevel:    log.InfoContextf,
	errorLevel:   log.ErrorContextf,
	fatalLevel:   log.FatalContextf,
}

// DefaultLogFunc is the default struct print method.
var DefaultLogFunc = func(ctx context.Context, req, rsp interface{}) string {
	return fmt.Sprintf(", req:%+v, rsp:%+v", req, rsp)
}

// SimpleLogFunc does not print the struct.
var SimpleLogFunc = func(ctx context.Context, req, rsp interface{}) string {
	return ""
}

// PrettyJSONLogFunc is the method for printing formatted JSON， which outputs the request
// and response as JSON with a newline between "req" and "rsp".
var PrettyJSONLogFunc = func(ctx context.Context, req, rsp interface{}) string {
	reqJSON, _ := json.MarshalIndent(req, "", "  ")
	rspJSON, _ := json.MarshalIndent(rsp, "", "  ")
	return fmt.Sprintf("\nreq:%s\nrsp:%s", string(reqJSON), string(rspJSON))
}

// JSONLogFunc is the method for printing JSON, which outputs the request and response as JSON
// without a newline between "req" and "rsp". If you prefer to print the request and response
// as JSON with a newline, use PrettyJSONLogFunc instead.
var JSONLogFunc = func(ctx context.Context, req, rsp interface{}) string {
	reqJSON, _ := json.Marshal(req)
	rspJSON, _ := json.Marshal(rsp)
	return fmt.Sprintf(", req:%s, rsp:%s", string(reqJSON), string(rspJSON))
}

// ServerFilter is the server-side filter.
func ServerFilter(opts ...Option) filter.ServerFilter {
	o := getFilterOptions(opts...)
	nilLogFormat := getLogFormat(debugLevel, o.enableColor, "server request:%s, cost:%s, from:%s%s")
	errLogFormat := getLogFormat(errorLevel, o.enableColor, "server request:%s, cost:%s, from:%s, err:%s%s")
	deadlineLogFormat := getLogFormat(errorLevel, o.enableColor,
		"server request:%s, cost:%s, from:%s, err:%s, total timeout:%s%s")
	return func(ctx context.Context, req interface{}, handler filter.ServerHandleFunc) (rsp interface{}, err error) {
		begin := time.Now()
		rsp, err = handler(ctx, req)
		msg := trpc.Message(ctx)
		if !o.passed(msg.ServerRPCName(), err) {
			return rsp, err
		}

		end := time.Now()
		var addr string
		if msg.RemoteAddr() != nil {
			addr = msg.RemoteAddr().String()
		}
		if err == nil {
			o.nilLogLevelFunc(
				ctx, nilLogFormat,
				msg.ServerRPCName(), end.Sub(begin), addr, o.logFunc(ctx, req, rsp),
			)
		} else {
			deadline, ok := ctx.Deadline()
			if ok {
				o.errLogLevelFunc(
					ctx, deadlineLogFormat, msg.ServerRPCName(), end.Sub(begin), addr, err.Error(),
					deadline.Sub(begin), o.logFunc(ctx, req, rsp),
				)
			} else {
				o.errLogLevelFunc(
					ctx, errLogFormat, msg.ServerRPCName(), end.Sub(begin), addr, err.Error(), o.logFunc(ctx, req, rsp),
				)
			}
		}
		return rsp, err
	}
}

// ClientFilter is the client-side filter.
func ClientFilter(opts ...Option) filter.Filter {
	o := getFilterOptions(opts...)
	nilLogFormat := getLogFormat(debugLevel, o.enableColor, "client request:%s, cost:%s, to:%s%s")
	errLogFormat := getLogFormat(errorLevel, o.enableColor, "client request:%s, cost:%s, to:%s, err:%s%s")
	return func(ctx context.Context, req, rsp interface{}, handler filter.HandleFunc) (err error) {
		msg := trpc.Message(ctx)
		begin := time.Now()
		err = handler(ctx, req, rsp)
		if !o.passed(msg.ClientRPCName(), err) {
			return err
		}

		end := time.Now()
		var addr string
		if msg.RemoteAddr() != nil {
			addr = msg.RemoteAddr().String()
		}
		if err == nil {
			o.nilLogLevelFunc(
				ctx, nilLogFormat, msg.ClientRPCName(), end.Sub(begin), addr, o.logFunc(ctx, req, rsp),
			)
		} else {
			o.errLogLevelFunc(
				ctx, errLogFormat, msg.ClientRPCName(), end.Sub(begin), addr, err.Error(), o.logFunc(ctx, req, rsp),
			)
		}
		return err
	}
}

// getFilterOptions gets the interceptor condition options.
func getFilterOptions(opts ...Option) *options {
	o := &options{
		logFunc:         DefaultLogFunc,
		errLogLevelFunc: LogContextfFuncs[errorLevel],
		nilLogLevelFunc: LogContextfFuncs[debugLevel],
	}
	for _, opt := range opts {
		opt(o)
	}

	for _, in := range o.include {
		o.includeMatchers = append(o.includeMatchers, build(*in))
	}

	for _, ex := range o.exclude {
		o.excludeMatchers = append(o.excludeMatchers, build(*ex))
	}

	return o
}

// getLogLevelFunc gets the log print method for the corresponding log level.
func getLogLevelFunc(level string, defaultLevel string) LogLevelFunc {
	logFunc, ok := LogContextfFuncs[level]
	if !ok {
		logFunc = LogContextfFuncs[defaultLevel]
	}
	return logFunc
}

// color is the color of logs.
type color uint8

// fontColor returns the color of logs' word.
func (c color) fontColor() uint8 {
	return uint8(c)
}

const (
	colorRed     color = 31
	colorMagenta color = 35
)

// levelColorMap records the color corresponding to the log level.
var levelColorMap = map[logLevel]color{
	debugLevel: colorMagenta,
	errorLevel: colorRed,
}

// getLogFormat makes the log output to display different colors based on user settings.
func getLogFormat(level logLevel, enableColor bool, format string) string {
	if !enableColor {
		// 默认方案
		return format
	}
	preColor := "\033[1;%dm" // preColor controls the display of colored characters.
	sufColor := "\033[0m"    // sufColor controls the display of color termination characters.
	if v, ok := levelColorMap[level]; ok {
		return fmt.Sprintf(preColor, v.fontColor()) + format + sufColor
	}
	return format
}
