package debuglog

import imatcher "git.code.oa.com/trpc-go/trpc-filter/debuglog/internal/matcher"

// RuleItem is the basic configuration for a single rule.
type RuleItem struct {
	Method *string
	// MatchType default is "whole",
	// can be set to "regex", "prefix", "suffix", "whole".
	// If MatchType is not set correctly, RuleItem will always return false.
	MatchType *imatcher.MatchType `yaml:"match_type"`
	Retcode   *int
	// When ErrIsNil is set, the Retcode matching rules will be disabled.
	ErrIsNil *bool `yaml:"err_is_nil"`
}

// Matched is the result of rule matching.
func (e RuleItem) Matched(destMethod string, destRetCode int) bool {
	return e.isMethodMatched(destMethod) && e.isRetCodeMatched(destRetCode)
}

func (e RuleItem) matchAll(destMethod string, destRetCode int, destErrIsNil bool) bool {
	return e.isMethodMatched(destMethod) && e.isErrIsNilMatched(destErrIsNil) && e.isRetCodeMatched(destRetCode)
}

func (e RuleItem) isMethodMatched(method string) bool {
	return (e.Method == nil || *e.Method == method)
}

func (e RuleItem) isRetCodeMatched(code int) bool {
	return (e.Retcode == nil || *e.Retcode == code)
}

func (e RuleItem) isErrIsNilMatched(errIsNil bool) bool {
	return (e.ErrIsNil == nil || *e.ErrIsNil == errIsNil)
}
