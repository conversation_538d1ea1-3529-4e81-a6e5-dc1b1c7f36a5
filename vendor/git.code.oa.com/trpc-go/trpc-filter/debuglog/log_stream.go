package debuglog

import (
	"context"
	"fmt"
	"io"
	"sync/atomic"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

func init() {
	server.RegisterStreamFilter(pluginName, ServerStreamFilter())
	client.RegisterStreamFilter(pluginName, ClientStreamFilter())
}

type logServerStream struct {
	server.Stream
	opts *options
}

// RecvMsg implements server.Stream to intercept receiving message process.
func (s *logServerStream) RecvMsg(m interface{}) error {
	err := s.Stream.RecvMsg(m)
	if !s.opts.needToLogByErrMatcher(err) {
		return err
	}
	ctx := s.Stream.Context()
	msg := trpc.Message(ctx)
	logFunc := s.opts.nilLogLevelFunc
	logLevel := debugLevel
	if err != nil && err != io.EOF {
		logFunc = s.opts.errLogLevelFunc
		logLevel = errorLevel
	}
	logFunc(
		ctx,
		getLogFormat(logLevel, s.opts.enableColor, "server stream %s, ID: [%d], received message, from:%v, err:%v%s"),
		msg.ServerRPCName(), msg.StreamID(), msg.RemoteAddr(), err, s.opts.logFunc(ctx, m, nil),
	)
	return err
}

// SendMsg implements server.Stream to intercept sending message process.
func (s *logServerStream) SendMsg(m interface{}) error {
	err := s.Stream.SendMsg(m)
	if !s.opts.needToLogByErrMatcher(err) {
		return err
	}
	ctx := s.Stream.Context()
	msg := trpc.Message(ctx)
	logFunc := s.opts.nilLogLevelFunc
	logLevel := debugLevel
	if err != nil {
		logFunc = s.opts.errLogLevelFunc
		logLevel = errorLevel
	}
	logFunc(
		ctx,
		getLogFormat(logLevel, s.opts.enableColor, "server stream %s, ID: [%d], sent message, to:%v, err:%v%s"),
		msg.ServerRPCName(), msg.StreamID(), msg.RemoteAddr(), err, s.opts.logFunc(ctx, nil, m),
	)
	return err
}

// ServerStreamFilter creates the server-side stream filter.
func ServerStreamFilter(opts ...Option) server.StreamFilter {
	o := getFilterOptions(opts...)
	return func(ss server.Stream, info *server.StreamServerInfo, handler server.StreamHandler) error {
		ctx := ss.Context()
		msg := trpc.Message(ctx)
		rpcName := msg.ServerRPCName()
		if !o.needToLogByMethodMatcher(rpcName) {
			return handler(ss)
		}
		begin := time.Now()
		err := handler(&logServerStream{Stream: ss, opts: o})
		if !o.needToLogByErrMatcher(err) {
			return err
		}
		end := time.Now()
		var addr string
		if msg.RemoteAddr() != nil {
			addr = msg.RemoteAddr().String()
		}
		logFunc := o.nilLogLevelFunc
		logLevel := debugLevel
		var deadlineMsg string
		if err != nil {
			logFunc = o.errLogLevelFunc
			logLevel = errorLevel
			deadline, ok := ctx.Deadline()
			if ok {
				deadlineMsg = fmt.Sprintf(" ,total timeout:%s", deadline.Sub(begin))
			}
		}
		logFunc(
			ctx,
			getLogFormat(logLevel, o.enableColor, "server stream %s, ID: [%d], end, cost %s, from:%v, err:%v%s"),
			rpcName, msg.StreamID(), end.Sub(begin), addr, err, deadlineMsg,
		)
		return err
	}
}

type logClientStream struct {
	client.ClientStream
	begin   time.Time
	opts    *options
	rpcName string
	desc    *client.ClientStreamDesc

	isClosed uint32
}

// SendMsg implements client.ClientStream to intercept sending message process.
func (cs *logClientStream) SendMsg(m interface{}) error {
	err := cs.ClientStream.SendMsg(m)
	if err != nil {
		cs.finish(err)
		return err
	}
	logFunc := cs.opts.nilLogLevelFunc
	logLevel := debugLevel
	if err != nil {
		logFunc = cs.opts.errLogLevelFunc
		logLevel = errorLevel
	}
	ctx := cs.ClientStream.Context()
	msg := codec.Message(ctx)
	logFunc(
		ctx,
		getLogFormat(logLevel, cs.opts.enableColor, "client stream %s, ID: [%d], sent message, to:%v, err:%v%s"),
		cs.rpcName, msg.StreamID(), msg.RemoteAddr(), err, cs.opts.logFunc(cs.ClientStream.Context(), m, nil),
	)
	return nil
}

// RecvMsg implements client.ClientStream to intercept receiving message process.
func (cs *logClientStream) RecvMsg(m interface{}) error {
	err := cs.ClientStream.RecvMsg(m)
	if err != nil {
		if err == io.EOF {
			cs.finish(nil)
		} else {
			cs.finish(err)
		}
		return err
	}

	logFunc := cs.opts.nilLogLevelFunc
	logLevel := debugLevel
	if err != nil {
		logFunc = cs.opts.errLogLevelFunc
		logLevel = errorLevel
	}
	ctx := cs.ClientStream.Context()
	msg := codec.Message(ctx)
	logFunc(
		ctx,
		getLogFormat(logLevel, cs.opts.enableColor, "client stream %s, ID: [%d], received message, from:%v, err:%v%s"),
		cs.rpcName, msg.StreamID(), msg.RemoteAddr(), err, cs.opts.logFunc(cs.ClientStream.Context(), nil, m),
	)

	if !cs.desc.ServerStreams {
		cs.finish(nil)
	}
	return nil
}

// CloseSend implements client.ClientStream to intercept closing send process.
func (cs *logClientStream) CloseSend() error {
	if err := cs.ClientStream.CloseSend(); err != nil {
		cs.finish(err)
		return err
	}
	return nil
}

func (cs *logClientStream) finish(err error) {
	if !atomic.CompareAndSwapUint32(&cs.isClosed, 0, 1) {
		return
	}
	if !cs.opts.needToLogByErrMatcher(err) {
		return
	}
	logFunc := cs.opts.nilLogLevelFunc
	logLevel := debugLevel
	if err != nil {
		logFunc = cs.opts.errLogLevelFunc
		logLevel = errorLevel
	}
	ctx := cs.ClientStream.Context()
	msg := codec.Message(ctx)
	logFunc(
		cs.ClientStream.Context(),
		getLogFormat(logLevel, cs.opts.enableColor, "client stream %s, ID: [%d], end, cost %s, to:%v, err:%v"),
		cs.rpcName, msg.StreamID(), time.Since(cs.begin), msg.RemoteAddr(), err,
	)
}

// ClientStreamFilter creates the client-side stream filter.
func ClientStreamFilter(opts ...Option) client.StreamFilter {
	o := getFilterOptions(opts...)
	return func(ctx context.Context, desc *client.ClientStreamDesc, streamer client.Streamer) (client.ClientStream,
		error) {
		msg := codec.Message(ctx)
		begin := time.Now()
		s, err := streamer(ctx, desc)
		if !o.needToLogByMethodMatcher(msg.ClientRPCName()) {
			return s, err
		}
		return &logClientStream{
			ClientStream: s,
			begin:        begin,
			desc:         desc,
			opts:         o,
			rpcName:      msg.ClientRPCName(),
		}, err
	}
}

func (o *options) needToLogByMethodMatcher(method string) bool {
	for _, in := range o.includeMatchers {
		if in.methodMatcher(method) {
			return true
		}
	}
	if len(o.include) > 0 {
		return false
	}
	for _, ex := range o.excludeMatchers {
		if ex.methodMatcher(method) {
			return false
		}
	}
	return true
}

func (o *options) needToLogByErrMatcher(err error) bool {
	for _, in := range o.includeMatchers {
		if in.errMatcher(err) {
			return true
		}
	}
	if len(o.include) > 0 {
		return false
	}
	for _, ex := range o.excludeMatchers {
		if ex.errMatcher(err) {
			return false
		}
	}
	return true
}
