# Change Log

## [0.1.13](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.13) (2024-08-05)

### Features

- debuglog: add Regex to RuleItem for regular expression (!402)

## [0.1.12](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.12) (2024-07-26)

### Fixes

- debuglog: Fixed the logic for handling cases where errcode is equal to 0. (!409)

## [0.1.11](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.11) (2024-07-19)

### Features

- debuglog: add ErrIsNil to RuleItem for finer matching customization (!393)
- {slime, singleflight, debuglog}: fix typo (!401)
- debuglog: add description for method field (!391)

## [0.1.10](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.10) (2024-06-03)

### Features

- debuglog: JSONLogFun prints req and rsp without a newline (!387)

## [0.1.9](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.9) (2023-04-15)

### Fixes

- 配置 exclude 规则时，支持配置 errCode 等于 0 的情况

## [0.1.7](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.7) (2023-04-14)

### Fixes

- 支持不同级别日志显示不同颜色 (!317)

## [0.1.6](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.6) (2022-09-20)

### Fixes

- 服务端配置 exclude 规则时，请求 rsp 始终为 nil

## [0.1.5](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.5) (2022-07-18)

### Features

- 支持 trace 级别的日志，可以配合 TRPC_LOG_TRACE 环境变量实现动态开关日志

## [0.1.4](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.4) (2022-04-18)

### Features

- 支持调整日志打印级别
- server filter 重构兼容框架 v0.9.0

## [0.1.3](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.3) (2020-11-24)

### Features

- 支持 json 或自定义打印方式

## [0.1.2](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.2) (2020-08-03)

### Features

- 增加 debuglog 插件配置

## [0.1.1](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.1) (2020-01-17)

### Features

- 增加 total timeout 整体链路剩余超时时间

## [0.1.0](https://git.woa.com/trpc-go/trpc-filter/tree/debuglog/v0.1.0) (2020-01-12)

### Features

- 支持拦截器自动打印 debug 日志
