package debuglog

import (
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

const (
	pluginName = "debuglog"
	pluginType = "tracing"
)

func init() {
	plugin.Register(pluginName, &Plugin{})
}

// Plugin is the implement of the debuglog trpc plugin.
type Plugin struct {
}

// Type is the type of debuglog trpc plugin.
func (p *Plugin) Type() string {
	return pluginType
}

// Config is the configuration for the debuglog trpc plugin.
type Config struct {
	LogType       string `yaml:"log_type"`
	ErrLogLevel   string `yaml:"err_log_level"`
	NilLogLevel   string `yaml:"nil_log_level"`
	ServerLogType string `yaml:"server_log_type"`
	ClientLogType string `yaml:"client_log_type"`
	EnableColor   *bool  `yaml:"enable_color"`
	Include       []*RuleItem
	Exclude       []*RuleItem
}

func getLogFunc(t string) LogFunc {
	switch t {
	case "simple":
		return SimpleLogFunc
	case "prettyjson":
		return PrettyJSONLogFunc
	case "json":
		return JSONLogFunc
	default:
		return DefaultLogFunc
	}
}

// Setup initializes the debuglog instance.
func (p *Plugin) Setup(name string, configDec plugin.Decoder) error {
	var conf Config
	err := configDec.Decode(&conf)
	if err != nil {
		return err
	}

	var serverOpt []Option
	var clientOpt []Option

	serverLogType := conf.LogType
	if conf.ServerLogType != "" {
		serverLogType = conf.ServerLogType
	}
	serverOpt = append(serverOpt, WithLogFunc(getLogFunc(serverLogType)))

	clientLogType := conf.LogType
	if conf.ClientLogType != "" {
		clientLogType = conf.ClientLogType
	}
	clientOpt = append(clientOpt, WithLogFunc(getLogFunc(clientLogType)))

	for _, in := range conf.Include {
		serverOpt = append(serverOpt, WithInclude(in))
		clientOpt = append(clientOpt, WithInclude(in))
	}
	for _, ex := range conf.Exclude {
		serverOpt = append(serverOpt, WithExclude(ex))
		clientOpt = append(clientOpt, WithExclude(ex))
	}

	clientOpt = append(clientOpt,
		WithNilLogLevelFunc(getLogLevelFunc(conf.NilLogLevel, "debug")),
		WithErrLogLevelFunc(getLogLevelFunc(conf.ErrLogLevel, "error")),
	)
	serverOpt = append(serverOpt,
		WithNilLogLevelFunc(getLogLevelFunc(conf.NilLogLevel, "debug")),
		WithErrLogLevelFunc(getLogLevelFunc(conf.ErrLogLevel, "error")),
	)
	if conf.EnableColor != nil {
		serverOpt = append(serverOpt, WithEnableColor(*conf.EnableColor))
		clientOpt = append(clientOpt, WithEnableColor(*conf.EnableColor))
	}

	filter.Register(pluginName, ServerFilter(serverOpt...), ClientFilter(clientOpt...))
	server.RegisterStreamFilter(pluginName, ServerStreamFilter(serverOpt...))
	client.RegisterStreamFilter(pluginName, ClientStreamFilter(clientOpt...))

	return nil
}
