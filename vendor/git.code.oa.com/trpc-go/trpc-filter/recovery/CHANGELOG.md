# Change Log

## [0.1.5](https://git.woa.com/trpc-go/trpc-filter/tree/recovery/v0.1.5) (2024-08-06)

### Features

- recovery: add client filter !416
- recovery: support stream filter !340

### Refactors

- recovery: refactor !415

## [0.1.4](https://git.woa.com/trpc-go/trpc-filter/tree/recovery/v0.1.4) (2022-08-08)

### Features

- recovery插件 panic 堆栈 buffer 调整为 4096 bytes

## [0.1.3](https://git.woa.com/trpc-go/trpc-filter/tree/recovery/v0.1.3) (2022-06-14)

### Features

- 升级 trpc-go 到 v0.9.3，适配新版本 filter 签名

## [0.1.2](https://git.woa.com/trpc-go/trpc-filter/tree/recovery/v0.1.2) (2020-07-31)

### Features

- 修改recover调用栈buffer为可配置参数
- panic时增加metrics上报

## [0.1.1](https://git.woa.com/trpc-go/trpc-filter/tree/recovery/v0.1.1) (2020-03-06)

### Features

- 修改recover调用栈buffer大小到1024

## [0.1.0](https://git.woa.com/trpc-go/trpc-filter/tree/recovery/v0.1.0) (2020-01-12)

### Features

- 支持server端拦截器捕获panic并返回错误码给前端
