// Package naming is a naming configuration.
package naming

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/polaris/polaris-go/pkg/config"
	plog "git.code.oa.com/polaris/polaris-go/pkg/log"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/circuitbreaker"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/discovery"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/loadbalance"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris/registry" // 初始化注册模块
	"git.code.oa.com/trpc-go/trpc-naming-polaris/selector"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/servicerouter"
	"gopkg.in/yaml.v3"
)

func init() {
	plugin.Register("polaris", &SelectorFactory{})
}

// Config framework configuration.
type Config struct {
	Name                              string                  `yaml:"-"` // Name is the current name of plugin.
	Debug                             bool                    `yaml:"debug"`
	Default                           *bool                   `yaml:"default"`
	Protocol                          string                  `yaml:"protocol"`
	ReportTimeout                     *time.Duration          `yaml:"report_timeout"`
	EnableServiceRouter               *bool                   `yaml:"enable_servicerouter"`
	EnableCanary                      *bool                   `yaml:"enable_canary"`
	PersistDir                        *string                 `yaml:"persistDir"`
	ServiceExpireTime                 *time.Duration          `yaml:"service_expire_time"`
	LogDir                            *string                 `yaml:"log_dir"`
	Logs                              *Logs                   `yaml:"logs"`
	Timeout                           int                     `yaml:"timeout"`
	ConnectTimeout                    int                     `yaml:"connect_timeout"`
	MessageTimeout                    *time.Duration          `yaml:"message_timeout"`
	JoinPoint                         *string                 `yaml:"join_point"`
	AddressList                       string                  `yaml:"address_list"`
	Discovery                         DiscoveryConfig         `yaml:"discovery"`
	Loadbalance                       LoadbalanceConfig       `yaml:"loadbalance"`
	CircuitBreaker                    CircuitBreakerConfig    `yaml:"circuitbreaker"`
	ServiceRouter                     ServiceRouterConfig     `yaml:"service_router"`
	ClusterService                    ClusterService          `yaml:"cluster_service"`
	OutlierDetection                  OutlierDetectionConfig  `yaml:"outlierDetection"`
	EnableTransMeta                   bool                    `yaml:"enable_trans_meta"`
	MetaFailOverMode                  int                     `yaml:"meta_fail_over_mode"`
	BindIP                            string                  `yaml:"bind_ip"`
	InstanceLocation                  *model.InstanceLocation `yaml:"instance_location"`
	DisableSourceServiceNameRouteRule bool                    `yaml:"disable_source_service_name_route_rule"`
	PolarisConfig                     config.Configuration
}

// UnmarshalYAML is the customized unmarshal function to ensure the default value of the config.
func (c *Config) UnmarshalYAML(value *yaml.Node) error {
	type plain Config
	if err := value.Decode((*plain)(c)); err != nil {
		return err
	}
	setDefaultConfig(c)
	return nil
}

func setDefaultConfig(c *Config) {
	if c == nil {
		return
	}
	if c.Logs == nil {
		c.Logs = &Logs{
			Level:      "default",
			MaxBackups: plog.DefaultRotationMaxBackups,
			MaxSize:    plog.DefaultRotationMaxSize,
		}
	}
	if c.LogDir != nil && c.Logs.DirPath == "" {
		c.Logs.DirPath = *c.LogDir
	}
	if c.Logs.MaxBackups == 0 {
		c.Logs.MaxBackups = plog.DefaultRotationMaxBackups
	}
	if c.Logs.MaxSize == 0 {
		c.Logs.MaxSize = plog.DefaultRotationMaxSize
	}
	if c.Logs.DirPath == "" {
		c.Logs.DirPath = plog.DefaultLogRotationRootDir
	}
}

// Logs log configuration.
type Logs struct {
	DirPath    string `yaml:"dir_path"`
	Level      string `yaml:"level"`
	MaxBackups int    `yaml:"max_backups"`
	MaxSize    int    `yaml:"max_size"`
}

// ServiceRouterConfig service routing configuration.
type ServiceRouterConfig struct {
	// NearbyMatchLevel is the minimum matching level of the nearest route,
	// including region (big area),
	// zone (area), campus (campus), and the default is zone.
	NearbyMatchLevel string `yaml:"nearby_matchlevel"`
	// StrictNearby set whether to check nearby rule strictly, default false.
	StrictNearby *bool `yaml:"strict_nearby"`
	// EnableRecoverAll set whether to enable all dead and all alive, default true.
	EnableRecoverAll *bool `yaml:"enable_recover_all"`
	// PercentOfMinInstances # is the minimum healthy instance judgment threshold of all dead and all alive,
	// the value range is between [0,1], and the default is 0.
	// which means, only when all instances are unhealthy, all dead and all alive will be enabled.
	PercentOfMinInstances float64 `yaml:"percent_of_min_instances"`
	// NeedReturnAllNodes indicates whether to expand all nodes into registry.Node return.
	NeedReturnAllNodes bool `yaml:"need_return_all_nodes"`
}

// DiscoveryConfig configuration.
type DiscoveryConfig struct {
	RefreshInterval int `yaml:"refresh_interval"`
}

// LoadbalanceConfig loads balancing configuration.
type LoadbalanceConfig struct {
	Name []string `yaml:"name"` // load balancing type.
	// Detailed configuration of each load balancing strategy.
	Details map[string]yaml.Node `yaml:"details"`
}

// CircuitBreakerConfig circuit breaker configuration.
type CircuitBreakerConfig struct {
	Enable                    *bool          `yaml:"enable"`
	CheckPeriod               *time.Duration `yaml:"checkPeriod"`
	RequestCountAfterHalfOpen *int           `yaml:"requestCountAfterHalfOpen"`
	SleepWindow               *time.Duration `yaml:"sleepWindow"`
	SuccessCountAfterHalfOpen *int           `yaml:"successCountAfterHalfOpen"`
	Chain                     []string       `yaml:"chain"`
	ErrorCount                *struct {
		ContinuousErrorThreshold *int           `yaml:"continuousErrorThreshold"`
		MetricNumBuckets         *int           `yaml:"metricNumBuckets"`
		MetricStatTimeWindow     *time.Duration `yaml:"metricStatTimeWindow"`
	} `yaml:"errorCount"`
	ErrorRate *struct {
		ErrorRateThreshold     *float64       `yaml:"errorRateThreshold"`
		MetricNumBuckets       *int           `yaml:"metricNumBuckets"`
		MetricStatTimeWindow   *time.Duration `yaml:"metricStatTimeWindow"`
		RequestVolumeThreshold *int           `yaml:"requestVolumeThreshold"`
	} `yaml:"errorRate"`
}

// OutlierDetectionConfig circuit breaker detection configuration.
// Only use 'Enable' & 'CheckPeriod': 'Enable'=true equals 'When'=on-recover,
// 'Enable'=false equals 'When'=never, 'CheckPeriod' equals Interval.
// 'When', 'Interval', 'Concurrency' have higher priority than 'Enable' & 'CheckPeriod'.
// If you both set 'When' and 'Enable','When' will overwrite 'Enable',
// also if you both set 'Interval' AND 'CheckPeriod', 'Interval' will overwrite 'CheckPeriod'.
// Polaris SDK will verify this config, if verify failed, you will receive an SDK err.
type OutlierDetectionConfig struct {
	// Deprecated: Use When instead
	Enable *bool `yaml:"enable"` // Whether to start the circuit breaker.
	// Deprecated: Use Interval instead
	CheckPeriod *time.Duration `yaml:"checkPeriod"` // Timing detection cycle.
	// Detection strategy.
	// The following three values are supported:
	// always: Always do a detection;
	// on-recover: Detection only performed when the instance is break;
	// never: Never detection.
	// if not set this value, is equals never;
	// else if this value set incorrectly, err will be returned when verify config;
	// else if this value equals always or on-recover, the 'Concurrency' must larger than 0.
	When string `yaml:"when"`
	// Timing detection cycle.
	Interval *time.Duration `yaml:"interval"`
	// Number of concurrent goroutines for health detection.
	Concurrency *int `yaml:"concurrency"`
}

// ClusterService cluster service.
type ClusterService struct {
	Discover    string `yaml:"discover"`
	HealthCheck string `yaml:"health_check"`
	Monitor     string `yaml:"monitor"`
}

// SelectorFactory implements the name service plugin for trpc.
type SelectorFactory struct {
	sdkCtx         api.SDKContext
	decorateConfig func(*Config) *Config
}

// Type plugin type.
func (f *SelectorFactory) Type() string {
	return "selector"
}

func (c *Config) getSetDefault() bool {
	setDefault := true
	if c.Default != nil {
		setDefault = *c.Default
	}
	return setDefault
}

func (c *Config) getEnableCanary() bool {
	var isEnable bool
	if c.EnableCanary != nil {
		isEnable = *c.EnableCanary
	}
	return isEnable
}

func (c *Config) getEnableServiceRouter() bool {
	isEnable := true
	if c.EnableServiceRouter != nil {
		isEnable = *c.EnableServiceRouter
	}
	return isEnable
}

func (c *Config) setLog() {
	log.Infof("naming-polaris starts to set polaris-go logs config: %+v", c.Logs)
	if l := c.Logs; l != nil {
		if err := api.SetLoggersDetailed(l.DirPath, getLogLevel(l.Level), l.MaxBackups, l.MaxSize); err != nil {
			log.Errorf("naming-polaris set polaris-go logs error: %+v", err)
		}
	}
	log.Infof("naming-polaris starts to set polaris-go logs level: %+v", c.Debug)
	if c.Debug {
		if err := plog.GetBaseLogger().SetLogLevel(plog.DebugLog); err != nil {
			log.Errorf("naming-polaris set polaris-go log level error: %+v", err)
		}
	}
}

// Setup initialization.
func (f *SelectorFactory) Setup(name string, dec plugin.Decoder) error {
	if dec == nil {
		return errors.New("selector config decoder empty")
	}
	conf := &Config{Name: name}
	if err := dec.Decode(conf); err != nil {
		return err
	}
	if f.decorateConfig != nil {
		conf = f.decorateConfig(conf)
	}
	sdkCtx, err := setupWithConfig(conf)
	f.sdkCtx = sdkCtx
	return err
}

// FlexDependsOn makes sure that register is initialized after selector,
// which may set some global status of SDK, such as log directories.
func (f *SelectorFactory) FlexDependsOn() []string {
	return []string{"log-default"}
}

// ConfigDecorator is a decorator to modify the configuration right after
// unmarshaling and before the actual setting up.
//
// Typical usage:
//
//	import (
//	    naming "git.code.oa.com/trpc-go/trpc-naming-polaris"
//	    "git.code.oa.com/trpc-go/trpc-go/plugin"
//	)
//
//	func main() {
//	    pluginType, pluginName := "selector", "polaris"
//	    s := plugin.Get(pluginType, pluginName)
//	    cd, ok := s.(naming.ConfigDecorator)
//	    if !ok {
//	        log.Fatal("naming polaris selector factory should implement ConfigDecorator interface")
//	    }
//	    cd.WithDecorateConfig(func(c *Config) *Config {
//	        pc := api.NewConfiguration() // Or use: config.LoadConfigurationByFile("polaris.yaml")
//	        addresses := []string{"127.0.0.1:8081"}
//	        pc.GetGlobal().GetServerConnector().SetAddresses(addresses)
//	        pc.GetGlobal().GetAPI().SetTimeout(time.Second)
//	        pc.GetProvider().GetRateLimit().GetRateLimitCluster().SetService("polaris.metric.v2.test")
//	        // ...
//	        c.PolarisConfig = pc
//	        return c
//	    })
//	    trpc.NewServer()
//	    // ...
//	}
type ConfigDecorator interface {
	WithDecorateConfig(func(*Config) *Config)
}

// WithDecorateConfig sets the configuration decorator for naming polaris.
func (f *SelectorFactory) WithDecorateConfig(decorate func(*Config) *Config) {
	f.decorateConfig = decorate
}

// GetSDKCtx returns the stored sdk context.
func (f *SelectorFactory) GetSDKCtx() api.SDKContext {
	return f.sdkCtx
}

// SetupWithConfig executes setup using the given config.
// Remember to give a proper config name to conf.Name.
func SetupWithConfig(conf *Config) error {
	_, err := setupWithConfig(conf)
	return err
}

func setupWithConfig(conf *Config) (api.SDKContext, error) {
	// 如果没设置协议默认使用 grpc 协议
	if len(conf.Protocol) == 0 {
		conf.Protocol = "grpc"
	}

	// Initialization log.
	conf.setLog()
	sdkCtx, err := newSDKContext(conf)
	if err != nil {
		return nil, fmt.Errorf("new sdk ctx err: %w", err)
	}
	return sdkCtx, setupComponents(sdkCtx, conf)
}

func setupComponents(sdkCtx api.SDKContext, conf *Config) error {
	setDefault := conf.getSetDefault()
	enableServiceRouter := conf.getEnableServiceRouter()
	enableCanary := conf.getEnableCanary()
	if err := discovery.Setup(sdkCtx, &discovery.Config{Name: conf.Name}, setDefault); err != nil {
		return err
	}

	// Initialize service routing.
	if err := servicerouter.Setup(
		sdkCtx,
		&servicerouter.Config{
			Name:               conf.Name,
			Enable:             enableServiceRouter,
			EnableCanary:       enableCanary,
			NeedReturnAllNodes: conf.ServiceRouter.NeedReturnAllNodes,
		},
		setDefault,
	); err != nil {
		return err
	}
	if err := setupLoadbalance(sdkCtx, conf, setDefault); err != nil {
		return err
	}
	if err := circuitbreaker.Setup(
		sdkCtx,
		&circuitbreaker.Config{
			Name:          conf.Name,
			ReportTimeout: conf.ReportTimeout,
		},
		setDefault,
	); err != nil {
		return err
	}
	if err := selector.Setup(sdkCtx,
		&selector.Config{
			Name:                              conf.Name,
			Enable:                            enableServiceRouter,
			EnableCanary:                      enableCanary,
			ReportTimeout:                     conf.ReportTimeout,
			EnableTransMeta:                   conf.EnableTransMeta,
			MetaFailOverMode:                  conf.MetaFailOverMode,
			DisableSourceServiceNameRouteRule: conf.DisableSourceServiceNameRouteRule,
		}); err != nil {
		return err
	}
	return nil
}

func setupLoadbalance(sdkCtx api.SDKContext, conf *Config, setDefault bool) error {
	if len(conf.Loadbalance.Name) == 0 {
		conf.Loadbalance.Name = append(
			conf.Loadbalance.Name,
			loadbalance.LoadBalancerWR,
			loadbalance.LoadBalancerHash,
			loadbalance.LoadBalancerRingHash,
			loadbalance.LoadBalancerL5CST,
			loadbalance.LoadBalancerMaglev,
			loadbalance.LoadBalancerDWR,
			loadbalance.LoadBalancerBrpcMurmur,
		)
	}
	for index, balanceType := range conf.Loadbalance.Name {
		// Under the premise that Polaris is set as the addressing method by default,
		// the first load balancing method is set as the default load balancing method.
		isDefault := setDefault && index == 0
		if err := loadbalance.Setup(sdkCtx, balanceType, isDefault); err != nil {
			return err
		}
	}
	return nil
}

func setSdkCircuitBreaker(c config.Configuration, cfg *Config) {
	if len(cfg.CircuitBreaker.Chain) > 0 {
		c.GetConsumer().GetCircuitBreaker().SetChain(cfg.CircuitBreaker.Chain)
	}
	if cfg.CircuitBreaker.Enable != nil {
		c.GetConsumer().GetCircuitBreaker().SetEnable(*cfg.CircuitBreaker.Enable)
	}
	if cfg.CircuitBreaker.CheckPeriod != nil {
		c.GetConsumer().GetCircuitBreaker().SetCheckPeriod(*cfg.CircuitBreaker.CheckPeriod)
	}
	if cfg.CircuitBreaker.RequestCountAfterHalfOpen != nil {
		c.GetConsumer().GetCircuitBreaker().SetRequestCountAfterHalfOpen(*cfg.CircuitBreaker.RequestCountAfterHalfOpen)
	}
	if cfg.CircuitBreaker.SleepWindow != nil {
		c.GetConsumer().GetCircuitBreaker().SetSleepWindow(*cfg.CircuitBreaker.SleepWindow)
	}
	if cfg.CircuitBreaker.SuccessCountAfterHalfOpen != nil {
		c.GetConsumer().GetCircuitBreaker().SetSuccessCountAfterHalfOpen(*cfg.CircuitBreaker.SuccessCountAfterHalfOpen)
	}
	setErrorCount(c, cfg)
	setErrorRate(c, cfg)
}

func setErrorCount(c config.Configuration, cfg *Config) {
	if cfg.CircuitBreaker.ErrorCount == nil {
		return
	}
	errorCount := cfg.CircuitBreaker.ErrorCount
	if errorCount.ContinuousErrorThreshold != nil {
		c.GetConsumer().GetCircuitBreaker().GetErrorCountConfig().
			SetContinuousErrorThreshold(*errorCount.ContinuousErrorThreshold)
	}
	if errorCount.MetricNumBuckets != nil {
		c.GetConsumer().GetCircuitBreaker().GetErrorCountConfig().SetMetricNumBuckets(*errorCount.MetricNumBuckets)
	}
	if errorCount.MetricStatTimeWindow != nil {
		c.GetConsumer().GetCircuitBreaker().GetErrorCountConfig().SetMetricStatTimeWindow(*errorCount.MetricStatTimeWindow)
	}
}

func setErrorRate(c config.Configuration, cfg *Config) {
	if cfg.CircuitBreaker.ErrorRate == nil {
		return
	}
	errorRate := cfg.CircuitBreaker.ErrorRate
	if errorRate.ErrorRateThreshold != nil {
		c.GetConsumer().GetCircuitBreaker().GetErrorRateConfig().SetErrorRatePercent(int(*errorRate.ErrorRateThreshold * 100))
	}
	if errorRate.MetricNumBuckets != nil {
		c.GetConsumer().GetCircuitBreaker().GetErrorRateConfig().SetMetricNumBuckets(*errorRate.MetricNumBuckets)
	}
	if errorRate.RequestVolumeThreshold != nil {
		c.GetConsumer().GetCircuitBreaker().GetErrorRateConfig().SetRequestVolumeThreshold(*errorRate.RequestVolumeThreshold)
	}
	if errorRate.MetricStatTimeWindow != nil {
		c.GetConsumer().GetCircuitBreaker().GetErrorRateConfig().SetMetricStatTimeWindow(*errorRate.MetricStatTimeWindow)
	}
}

func setSdkOutlierDetection(c config.Configuration, cfg *Config) {
	if cfg.OutlierDetection.Enable != nil {
		c.GetConsumer().GetOutlierDetectionConfig().SetEnable(*cfg.OutlierDetection.Enable)
	}
	if cfg.OutlierDetection.CheckPeriod != nil {
		c.GetConsumer().GetOutlierDetectionConfig().SetCheckPeriod(*cfg.OutlierDetection.CheckPeriod)
	}
	if cfg.OutlierDetection.When != "" {
		c.GetConsumer().GetOutlierDetectionConfig().SetWhen(config.When(cfg.OutlierDetection.When))
	}
	if cfg.OutlierDetection.Interval != nil {
		c.GetConsumer().GetOutlierDetectionConfig().SetInterval(*cfg.OutlierDetection.Interval)
	}
	if cfg.OutlierDetection.Concurrency != nil {
		c.GetConsumer().GetOutlierDetectionConfig().SetConcurrency(*cfg.OutlierDetection.Concurrency)
	}
	// Only enable tcp connection detection.
	c.GetConsumer().GetOutlierDetectionConfig().SetChain([]string{"tcp"})
}

func setSdkProperty(c config.Configuration, cfg *Config) {
	if cfg.Timeout != 0 {
		timeout := time.Duration(cfg.Timeout) * time.Millisecond
		c.GetGlobal().GetAPI().SetTimeout(timeout)
		// If a timeout is set, the maximum number of retries needs to be set to 0.
		c.GetGlobal().GetAPI().SetMaxRetryTimes(0)
	}
	if cfg.Discovery.RefreshInterval != 0 {
		refreshInterval := time.Duration(cfg.Discovery.RefreshInterval) * time.Millisecond
		c.GetConsumer().GetLocalCache().SetServiceRefreshInterval(refreshInterval)
	}
	//Set the service cache as a persistent directory.
	if cfg.PersistDir != nil {
		c.GetConsumer().GetLocalCache().SetPersistDir(*cfg.PersistDir)
	}
	// Set the sdk cache retention time.
	if cfg.ServiceExpireTime != nil {
		c.GetConsumer().GetLocalCache().SetServiceExpireTime(*cfg.ServiceExpireTime)
	}
	if cfg.ClusterService.Discover != "" {
		c.GetGlobal().GetSystem().GetDiscoverCluster().SetService(cfg.ClusterService.Discover)
	}
	if cfg.ClusterService.HealthCheck != "" {
		c.GetGlobal().GetSystem().GetHealthCheckCluster().SetService(cfg.ClusterService.HealthCheck)
	}
	if cfg.ClusterService.Monitor != "" {
		c.GetGlobal().GetSystem().GetMonitorCluster().SetService(cfg.ClusterService.Monitor)
	}
	// Set service routing.
	if cfg.ServiceRouter.NearbyMatchLevel != "" {
		c.GetConsumer().GetServiceRouter().GetNearbyConfig().SetMatchLevel(cfg.ServiceRouter.NearbyMatchLevel)
	}
	if cfg.ServiceRouter.StrictNearby != nil {
		c.GetConsumer().GetServiceRouter().GetNearbyConfig().SetStrictNearby(*cfg.ServiceRouter.StrictNearby)
	}
	if cfg.ServiceRouter.EnableRecoverAll != nil {
		c.GetConsumer().GetServiceRouter().SetEnableRecoverAll(*cfg.ServiceRouter.EnableRecoverAll)
	}
	c.GetConsumer().GetServiceRouter().SetPercentOfMinInstances(cfg.ServiceRouter.PercentOfMinInstances)
}

func setLocation(c config.Configuration, cfg *Config) {
	location := cfg.InstanceLocation
	if location != nil && location.Region != "" && location.Zone != "" && location.Campus != "" {
		c.GetGlobal().GetAPI().GetLocation().SetRegion(location.Region)
		c.GetGlobal().GetAPI().GetLocation().SetZone(location.Zone)
		c.GetGlobal().GetAPI().GetLocation().SetCampus(location.Campus)
		c.GetGlobal().GetAPI().GetLocation().SetEnableUpdate(false)
	}
}

func newSDKContext(cfg *Config) (api.SDKContext, error) {
	var c config.Configuration
	if cfg.PolarisConfig != nil { // Specific polaris config
		c = cfg.PolarisConfig
	} else { // Default polairs config
		c = api.NewConfiguration()
	}

	cfg.AddressList = strings.TrimSpace(cfg.AddressList)
	if len(cfg.AddressList) > 0 {
		c.GetGlobal().GetServerConnector().SetAddresses(strings.Split(cfg.AddressList, ","))
	}

	// Setting address_list will override the configuration of joinPoint.
	if cfg.JoinPoint != nil {
		c.GetGlobal().GetServerConnector().SetJoinPoint(*cfg.JoinPoint)
	}
	// Set local IP.
	if cfg.BindIP != "" {
		c.GetGlobal().GetAPI().SetBindIP(cfg.BindIP)
	}

	c.GetGlobal().GetServerConnector().SetProtocol(cfg.Protocol)
	connectTimeout := selector.DefaultConnectTimeout
	if cfg.ConnectTimeout != 0 {
		connectTimeout = time.Millisecond * time.Duration(cfg.ConnectTimeout)
	}
	c.GetGlobal().GetServerConnector().SetConnectTimeout(connectTimeout)
	messageTimeout := selector.DefaultMessageTimeout
	if cfg.MessageTimeout != nil {
		messageTimeout = *cfg.MessageTimeout
	}

	var routers []string

	// Add a plugin to filter according to the called service env.
	routers = append(routers, config.DefaultServiceRouterDstMeta)
	routers = append(routers, c.GetConsumer().GetServiceRouter().GetChain()...)

	// Add canary routing chain.
	if cfg.EnableCanary != nil && *cfg.EnableCanary {
		routers = append(routers, config.DefaultServiceRouterCanary)
	}

	c.GetConsumer().GetServiceRouter().SetChain(routers)

	confs, err := loadbalance.AsPluginCfgs(cfg.Loadbalance.Details)
	if err != nil {
		return nil, fmt.Errorf("failed to parse yaml loadbalance details: %w", err)
	}
	loadBalanceConfig := c.GetConsumer().GetLoadbalancer()
	for name, conf := range confs {
		if err := loadBalanceConfig.SetPluginConfig(name, conf); err != nil {
			return nil, fmt.Errorf("failed to set load balancer %s's config: %w", name, err)
		}
	}

	c.GetGlobal().GetServerConnector().SetMessageTimeout(messageTimeout)
	// Configure circuit breaker policy.
	setSdkCircuitBreaker(c, cfg)
	// Configure circuit breaker detection policy.
	setSdkOutlierDetection(c, cfg)
	// Configure location properties.
	setLocation(c, cfg)
	// Configure other properties.
	setSdkProperty(c, cfg)
	sdkCtx, err := api.InitContextByConfig(c)
	if err != nil {
		return nil, err
	}
	return sdkCtx, nil
}

func getLogLevel(desc string) int {
	switch strings.ToLower(desc) {
	case "debug":
		return plog.DebugLog
	case "info":
		return plog.InfoLog
	case "warn":
		return plog.WarnLog
	case "error":
		return plog.ErrorLog
	case "fatal":
		return plog.FatalLog
	case "none":
		return plog.NoneLog
	case "default":
		fallthrough
	default:
		return plog.DefaultBaseLogLevel
	}
}
