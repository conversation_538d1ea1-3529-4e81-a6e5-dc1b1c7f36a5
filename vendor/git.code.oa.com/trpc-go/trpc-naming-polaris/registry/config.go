package registry

import (
	"time"

	"git.code.oa.com/polaris/polaris-go/pkg/model"
)

// Config is registry configuration.
type Config struct {
	// ServiceToken Service Access Token.
	ServiceToken string
	// Protocol Server access method, support http grpc, default grpc.
	Protocol string
	// HeartBeat Reporting heartbeat time interval, the default is recommended as TTL/2.
	HeartBeat int
	// EnableRegister By default, only report heartbeat, do not register service, if true, start registration.
	EnableRegister bool
	// Weight.
	Weight *int
	// TTL Unit s, the cycle for the server to check whether the periodic instance is healthy.
	TTL int
	// InstanceID instance name.
	InstanceID string
	// Namespace namespace.
	Namespace string
	// ServiceName Service Name.
	ServiceName string
	// BindAddress specifies reporting address.
	BindAddress string
	// Version version of instance
	Version *string
	// Metadata User-defined metadata information.
	Metadata map[string]string
	// DisableHealthCheck disables healthcheck.
	DisableHealthCheck bool
	// DisableDeregister diable deregister
	DisableDeregister bool
	// InstanceLocation is the geographic location of the instance.
	InstanceLocation *model.InstanceLocation
	// Timeout is the timeout duration for a single query.
	// By default, it directly uses the global timeout configuration.
	// The user's total maximum timeout is (1 + RetryCount) * Timeout.
	Timeout *time.Duration
	//  RetryCount is the number of retries. By default, it directly uses the global retry configuration.
	RetryCount *int
}
