package registry

import "fmt"

// DefaultDynamicWeightReporter is the default dynamic weight report instance.
// It should be used after the initialization of polaris-registry plugin or else
// a related error message will be induced.
//
// Example:
//
//	done := make(<-chan struct{})
//	go func(done <-chan struct{}) {
//		reportInterval := time.Second // Put this value into your own configuration file.
//		t := time.NewTicker(reportInterval)
//		defer t.Stop()
//		for {
//			select {
//			case <-t.C:
//			case <-done:
//				return
//			}
//			u, c := 23.3, 100.0 // Get these values some where.
//			precision, bitSize := 3, 64
//			used, capacity := strconv.FormatFloat(u, 'f', precision, bitSize), strconv.FormatFloat(c, 'f', precision, bitSize)
//			if err := registry.DefaultDynamicWeightReporter.Report(serviceName, used, capacity); err != nil {
//				log.Error("dynamic weight report error: %+v", err)
//			}
//		}
//	}(done)
var DefaultDynamicWeightReporter = &dynamicWeightReporter{services: make(map[string][]report)}

// dynamicWeightReporter provides utilities to report dynamic weight of a service.
// Reference: https://iwiki.woa.com/pages/viewpage.action?pageId=386636275
type dynamicWeightReporter struct {
	services map[string][]report
}

type report func(used, capacity string) error

// Report reports the current service metrics to enable dynamic weight load balancing.
// The service name should have already been registered beforehand.
// The current support metrics are used-capacity key-value pair.
// The given string `used` and `capacity` should be able to be parsed as float64:
//
//	u, err := strconv.ParseFloat(used, 64)
//	require.Nil(t, err)
//	c, err := strconv.ParseFloat(capacity, 64)
//	require.Nil(t, err)
//	require.LessOrEqual(t, u, c) // The underlying value of `used` should be less than or equal to `capacity`.
//
// Reference: https://iwiki.woa.com/pages/viewpage.action?pageId=386636275
func (r *dynamicWeightReporter) Report(serviceName, used, capacity string) error {
	reports, ok := r.services[serviceName]
	if !ok {
		return fmt.Errorf("dynamic weight reporter report failed: service %s hasn't been registered", serviceName)
	}
	for _, report := range reports {
		if err := report(used, capacity); err != nil {
			return err
		}
	}
	return nil
}
