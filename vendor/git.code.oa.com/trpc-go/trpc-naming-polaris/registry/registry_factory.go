package registry

import (
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"git.code.oa.com/polaris/polaris-go/api"
	plog "git.code.oa.com/polaris/polaris-go/pkg/log"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
)

const (
	defaultConnectTimeout = time.Second
	defaultMessageTimeout = time.Second
	defaultProtocol       = "grpc"
)

// FactoryConfig is factory configuration.
type FactoryConfig struct {
	EnableRegister     bool                    `yaml:"register_self"`
	Protocol           string                  `yaml:"protocol"`
	HeartbeatInterval  int                     `yaml:"heartbeat_interval"`
	Services           []Service               `yaml:"service"`
	Debug              bool                    `yaml:"debug"`
	AddressList        string                  `yaml:"address_list"`
	JoinPoint          *string                 `yaml:"join_point"`
	ClusterService     ClusterService          `yaml:"cluster_service"`
	ConnectTimeout     int                     `yaml:"connect_timeout"`
	MessageTimeout     *time.Duration          `yaml:"message_timeout"`
	DisableHealthCheck bool                    `yaml:"disable_health_check"`
	DisableDeregister  bool                    `yaml:"disable_deregister"`
	InstanceLocation   *model.InstanceLocation `yaml:"instance_location"`
}

// ClusterService is cluster service.
type ClusterService struct {
	Discover    string `yaml:"discover"`
	HealthCheck string `yaml:"health_check"`
	Monitor     string `yaml:"monitor"`
}

// Service is service configuration.
type Service struct {
	Namespace   string            `yaml:"namespace"`
	ServiceName string            `yaml:"name"`
	Token       string            `yaml:"token"`
	InstanceID  string            `yaml:"instance_id"`
	Weight      *int              `yaml:"weight"`
	BindAddress string            `yaml:"bind_address"`
	Version     *string           `yaml:"version"`
	MetaData    map[string]string `yaml:"metadata"`
	TTL         time.Duration     `yaml:"ttl"`
	Timeout     *time.Duration    `yaml:"timeout"`
	RetryCount  *int              `yaml:"retry_count"`
}

func init() {
	plugin.Register("polaris", &RegistryFactory{})
}

// RegistryFactory is registered factory.
type RegistryFactory struct {
	sdkCtx api.SDKContext
}

// Type returns registration type.
func (f *RegistryFactory) Type() string {
	return "registry"
}

// Setup starts loading configuration and registers log.
func (f *RegistryFactory) Setup(name string, configDec plugin.Decoder) error {
	if configDec == nil {
		return errors.New("registry config decoder empty")
	}
	conf := &FactoryConfig{}
	if err := configDec.Decode(conf); err != nil {
		return err
	}
	if conf.Debug {
		log.Debug("set polaris log level debug")
		plog.GetBaseLogger().SetLogLevel(plog.DebugLog)
	}
	sdkCtx, err := newSDKCtx(conf)
	if err != nil {
		return fmt.Errorf("create new provider failed: err %w", err)
	}
	f.sdkCtx = sdkCtx
	return register(api.NewProviderAPIByContext(sdkCtx), conf)
}

// FlexDependsOn makes sure that register is initialized after selector,
// which may set some global status of SDK, such as log directories.
func (f *RegistryFactory) FlexDependsOn() []string {
	return []string{"selector-polaris"}
}

// GetSDKCtx returns the stored sdk context.
func (f *RegistryFactory) GetSDKCtx() api.SDKContext {
	return f.sdkCtx
}

func newSDKCtx(cfg *FactoryConfig) (api.SDKContext, error) {
	c := api.NewConfiguration()
	if len(cfg.AddressList) > 0 {
		addressList := strings.Split(cfg.AddressList, ",")
		c.GetGlobal().GetServerConnector().SetAddresses(addressList)
	}
	// Config cluster
	if cfg.ClusterService.Discover != "" {
		c.GetGlobal().GetSystem().GetDiscoverCluster().SetService(cfg.ClusterService.Discover)
	}
	if cfg.ClusterService.HealthCheck != "" {
		c.GetGlobal().GetSystem().GetHealthCheckCluster().SetService(cfg.ClusterService.HealthCheck)
	}
	if cfg.ClusterService.Monitor != "" {
		c.GetGlobal().GetSystem().GetMonitorCluster().SetService(cfg.ClusterService.Monitor)
	}
	// Setting joinPoint will override address_list and cluster configuration.
	if cfg.JoinPoint != nil {
		c.GetGlobal().GetServerConnector().SetJoinPoint(*cfg.JoinPoint)
	}
	if cfg.Protocol == "" {
		cfg.Protocol = defaultProtocol
	}
	c.GetGlobal().GetServerConnector().SetProtocol(cfg.Protocol)
	if cfg.ConnectTimeout != 0 {
		c.GetGlobal().GetServerConnector().SetConnectTimeout(time.Duration(cfg.ConnectTimeout) * time.Millisecond)
	} else {
		c.GetGlobal().GetServerConnector().SetConnectTimeout(defaultConnectTimeout)
	}
	// Set message timeout.
	messageTimeout := defaultMessageTimeout
	if cfg.MessageTimeout != nil {
		messageTimeout = *cfg.MessageTimeout
	}
	c.GetGlobal().GetServerConnector().SetMessageTimeout(messageTimeout)
	return api.InitContextByConfig(c)
}

func register(provider api.ProviderAPI, conf *FactoryConfig) error {
	for _, service := range conf.Services {
		cfg := &Config{
			Protocol:           conf.Protocol,
			EnableRegister:     conf.EnableRegister,
			HeartBeat:          conf.HeartbeatInterval / 1000,
			Version:            service.Version,
			ServiceName:        service.ServiceName,
			Namespace:          service.Namespace,
			ServiceToken:       service.Token,
			InstanceID:         service.InstanceID,
			Metadata:           service.MetaData,
			BindAddress:        service.BindAddress,
			Weight:             service.Weight,
			TTL:                int(math.Ceil(service.TTL.Seconds())),
			DisableHealthCheck: conf.DisableHealthCheck,
			DisableDeregister:  conf.DisableDeregister,
			InstanceLocation:   conf.InstanceLocation,
			Timeout:            service.Timeout,
			RetryCount:         service.RetryCount,
		}

		reg, err := newRegistry(provider, cfg)
		if err != nil {
			return fmt.Errorf("create new registry for service %s failed: err %w", service.ServiceName, err)
		}

		// A new report func for the register.
		r := func(used, capacity string) error {
			const (
				usedKey     = "used"
				capacityKey = "capacity"
			)
			req := model.DynamicWeightReportRequest{
				InstanceID: cfg.InstanceID,
				Service:    service.ServiceName,
				Namespace:  cfg.Namespace,
				Host:       reg.host,
				Port:       uint32(reg.port),
				Token:      cfg.ServiceToken,
				Metrics: map[string]string{
					usedKey:     used,
					capacityKey: capacity,
				},
			}
			return provider.ReportDynamicWeight(&api.DynamicWeightReportRequest{
				DynamicWeightReportRequest: req,
			})
		}
		// Append the report func to the reporter.
		DefaultDynamicWeightReporter.services[service.ServiceName] = append(
			DefaultDynamicWeightReporter.services[service.ServiceName],
			r,
		)

		registry.Register(service.ServiceName, &recursiveRegistryPair{
			reg,
			registry.Get(service.ServiceName),
		})
		// Get the origin registry.Registry.
		// regs := registry.Get(service.ServiceName)
		// Remains consistent with the previous behavior.
		// if regs == nil {
		// 	registry.Register(service.ServiceName, reg)
		// 	continue
		// }

		// If regs != nil, just append the reg to the regs to
		// wrap the registry.Registry in a registryList,
		// then re-register the registryList.
	}
	return nil
}
