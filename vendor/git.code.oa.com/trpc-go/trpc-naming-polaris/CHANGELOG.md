# Change Log

## [0.5.23](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.23) (2025-02-27)

### features

- feat: add DisableSourceServiceNameRouteRule to disable source service name route rule (!244)

## [0.5.22](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.22) (2025-01-21)

### features

- feat: polaris-clusters to support multiple selector (merge request !238)  

### document

- circuitbreaker: add doc about ShouldCircuitBreak (merge request !235)
- docs: change config through code for naming-polaris (merge request !234)

## [0.5.21](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.21) (2024-10-24)

### features

- naming/registry: enable timeout and retryCount for registry (!233)

## [0.5.20](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.20) (2024-09-02)

### bug fixes

- naming-polaris: amend logic of log_dir and logs configuration (!228)

## [0.5.19](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.19) (2024-08-13)

### features

- naming: support init polaris-go sdk instance from polaris.yaml (!223)

## [0.5.18](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.18) (2024-08-12)

### features

- naming: support for configuring the enable property of the circuit breaker (!221)
- servicerouter: tidy servicerouter.go (!222)

## [0.5.17](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.17) (2024-07-09)

### features

- naming/registry: add recursiveRegistryPair for multiple Registries (!216)
- naming-polaris: add client yaml config notes (!217)
- naming-polaris: add links to iwiki and km (!215)
- feat: support EnableRecoverAll config (!214)

## [0.5.16](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.16) (2024-05-28)

### features

- selector: check client metadata for trans-meta (!212)

## [0.5.15](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.15) (2024-05-27)

### features

- Update polaris active health detection policy to support never, n-recover, and always policy configurations (!209)

## [0.5.14](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.14) (2024-05-09)

### features

- Upgrade polaris-go to v0.12.12 to fix dead lock issues (!205)

## [0.5.13](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.13) (2024-04-16)

### features

- Support config version of instance.

## [0.5.12](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.12) (2023-10-27)

### bug fixes

- Fix that nil routers result in nil cluster which would cause a failure in loadbalance. (!198)

## [0.5.11](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.11) (2023-10-24)

### features

- Provide `servicerouter.WithDisableNearbyRouter` to disable nearby router for a single request. (!196)

### bug fixes

- Update polaris-go to [v0.12.8](https://git.woa.com/polaris/polaris-go/blob/release-0.12/CHANGELOG.md) to fix some SDK bugs. (!197)

### enhancement

- Enrich readme details of private deployment for polaris. (!190)
- Format readme naming-polaris yaml config. (!191)
- Clarify the usage of instance id in readme. (!194)
- Use polaris Configuration to replace polaris ConfigurationImpl. (!195)

## [0.5.10](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.10) (2023-08-31)

### features

- Support config service level heartbeat ttl in yaml. (!189)

## [0.5.9](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.9) (2023-08-29)

### bug fixes

- DisableHealthCheck also disables heartbeat. (!187)

## [0.5.8](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.8) (2023-08-24)

### bug fixes

- Upgrade polaris-go to 0.12.6 to fix that Singpore cluster fallback to Mainland. (!186)

## [0.5.7](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.7) (2023-08-17)

### features

- Upgrade polaris-go to v0.12.5. (!183)
  - Fix the issue of mixing rule-based routing and set-based routing.

## [0.5.6](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.6) (2023-08-11)

### features

- Upgrade polaris-go to v0.12.4. (!182)

## [0.5.5](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.5) (2023-08-08)

### features

- Update mock files (!179)

## [0.5.4](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.4) (2023-07-31)

### features

- Upgrade polaris-go to v0.12.2. (!177)

## [0.5.3](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.3) (2023-07-19)

### features

- Upgrade polaris-go to v0.12.1. (!176)

## [0.5.2](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.2) (2023-07-12)

### features

- Upgrade polaris-go to v0.12.0 (!172)
- Feat: add polaris config (!171)

## [0.5.1](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.1) (2023-05-11)

### features

- Export internal polaris SDK ctx by public factory method to avoid unnecessary polaris SDK init.(!166)  
  Other plugin such as polaris limiter may use this feature.  
  Note, the selector and registry still use different polaris SDK instances. This is a long-standing historical issue,
  and we don't fix it at this version.
- Improve readme:  
  add client service config yaml example (!162)  
  add multi-selector version notice (!163)  
  add doc for target, name addressing and service router (!164)  
  specify log dir and persiste dir for multi selector (!165)  

## [0.5.0](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.5.0) (2023-04-26)

- Revert !152 (which upgrades polaris-go to v2) (!161)

From v0.5.0 on, the coexist problem of canary and set remains unfixed.

You can wait for polaris-go to fix the bug on v1. If you wish to use this feature, you can use the retracted version v0.4.2, or naming-polaris v2.

## [0.4.4](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.4.4) (2023-04-17)

### bug fixes

- fix selector fallback name bug caused by !156 (!158)

## [0.4.3](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.4.3) (2023-04-17)

### features

- report counter metrics when heartbeat failed(!155)
- support any polaris loadbalance(!156)

### bug fixes

- explicitly import latest polaris-go v1 to avoid surprising coexist v1/v2 panic when upgrade naming-polaris only.

## [0.4.2](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.4.2) (2023-04-11)

### features

- Support set location info(!142)
- Add new field `default` to selector config to support multiple polaris selector(!149)
- Support zero Weight in yaml config(!141)
- Support returning all registry.Node in service router(!137)

### bug fixes

- On missing selector name, "polaris" is used as default(!151)
- Update polaris SDK to v2 to fix the coexist problem of canary and set(!152)
- Translate to English(!150)
- More readable README.md(!140, !139)

## [0.4.1](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.4.1) (2023-02-16)

### features

- Support dynamic weight load balancing (configure key as polaris_dwr)(!135)

## [0.4.0](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.4.0) (2023-02-13)

### features

- Support address location information registered by Polaris (!128)
- Support setting the threshold of all dead and all alive (!129)
- Support configuring the number of virtual nodes for Polaris ring hash (!130)
- Support configuring multiple Polaris plugins, which will use different Polaris names (!131)

### bug fixes

- Replace broken code.oa in docs with woa (!132)

## [0.3.6](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.3.6) (2022-11-04)

### features

- Polaris heartbeat can work with tRPC-Go health check. When health checking is configured, the first heartbeat report will occur when the service status changes to serving.

## [0.3.5](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.3.5) (2022-10-24)

### features

- The register plug-in is weakly dependent on the selector plug-in, so the log configuration of the selector also takes effect on the register.
- Support detailed configuration of Polaris log.
- Remove the gomonkey dependency in unittest.
- Upgrade polaris/polaris-go(v0.10.7 => v0.11.2), upgrade trpc-go/trpc-go(v0.5.2 => v0.8.0).

### bug fixes

- Fix that the Should type is not exported so that the user cannot construct the circuit breaker error checking function.

# Change Log

## [0.3.4](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.3.4) (2022-07-21)

### features

- In the New method of the selector, it will judge according to the configuration. If there is a configuration, use the JoinPoint in the configuration, so as to achieve the purpose of dynamically adjusting the JoinPoint

## [0.3.3](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.3.3) (2022-06-21)

### bug fixes

- Upgrade the polaris-go version to 0.10.7, and fix the problem that the 0.3.2 version plugin uses the 0.10.3 version polaris-go to generate cache files that cannot be read by the old version plugin

## [0.3.2](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.3.2) (2022-02-28)

### features

- Upgrade polaris-go dependency to 0.10.3: Support alias routing, remove the pb file in the project and change it to rely on polaris-server-api, support loading cache files on demand

## [0.3.1](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.3.1) (2021-12-02)

### features

- Polaris plugin withservicename mode added plugin
- Support register custom selector & add LocalCachePersistDir option

## [0.3.0](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.3.0) (2021-09-02)

### features

- Multi-environment routing, adding the ability to transparently transmit trpc protocol fields to Polaris meta

## [0.2.13](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.13) (2021-08-24)

### bug fixes

- Fix the problem that the environment transparent transmission only transparently transmits the environment with the highest priority

## [0.2.12](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.12) (2021-07-21)

### features

- tRPC-Go full-link timeout distinguishes full-link and user-defined timeouts, reducing false service circuit breaker judgments

## [0.2.11](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.11) (2021-07-02)

### features

- Support configuration to enable canary function

## [0.2.10](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.10) (2021-06-21)

### features

- Added support for Polaris cache timeout and access point configuration
- Polaris fusing detection function
- Polaris plug-in increases the minimum matching level parameter of the nearest route

## [0.2.9](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.9) (2021-05-25)

### features

- Support Polaris connection timeout
- Provides an externally accessible new instance interface
- Fix test failure
- Optimize documentation

## [0.2.8](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.8) (2021-04-07)

### features

- selector exposes UseBuildin

## [0.2.7](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.7) (2021-03-26)

### features

- registry supports specifying regions

## [0.2.6](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.6) (2021-03-26)

### features

- Fix the problem that the specified env does not take effect

## [0.2.5](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.5) (2021-03-17)

### Features

- Upgrade sdk version to 0.8.1

## [0.2.4](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.4) (2021-03-12)

### Features

- Upgrade sdk version to 0.8.0
- Load balancing supports the number of virtual nodes
- Support canary function

## [0.2.2](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.2) (2020-11-27)

### Features

- Upgrade sdk version
- Added l5cst, maglv hash

## [0.2.1](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.1) (2020-08-05)

### Features

- Upgrade sdk version
- Does not rely on external incoming addresses

## [0.2.0](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.2.0) (2020-08-05)

### Features

- Upgrade framework version
- Selector is not registered by default

## [0.1.16](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.16) (2020-08-05)

### Features

- Upgrade framework version
- Routing rules support metadata

## [0.1.15](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.15) (2020-07-20)

### Features

- default init selector

## [0.1.14](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.14) (2020-07-13)

### Features

- Polaris sdk version upgraded to v0.5.2

## [0.1.13](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.13) (2020-06-22)

### Features

- Polaris sdk version upgraded to v0.5

## [0.1.12](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.12) (2020-05-12)

### Features

- Support log directory configuration

## [0.1.11](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.11) (2020-04-26)

### Features

- Registration support weight

## [0.1.10](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.10) (2020-04-10)

### Features

- Polaris sdk version upgraded to v0.3.11

## [0.1.9](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.9) (2020-04-01)

### Features

- Fix set logic panic
- Polaris sdk version upgraded to v0.3.9

## [0.1.8](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.8) (2020-03-26)

### Features

- Support cluster configuration
- Support set function

## [0.1.7](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.7) (2020-03-19)

### Features

- Fix the problem that loadbalance does not have instance nodes

## [0.1.6](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.6) (2020-03-13)

### Features

- Polaris sdk version upgraded to v0.3.6

## [0.1.5](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.5) (2020-03-12)

### Features

- Polaris sdk version upgraded to v0.3.5
- Support circuit breaker configuration
- Support persistent directory configuration
- Support consistent hash

## [0.1.4](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.4) (2020-03-02)

### Features

- Add plug-in statistical data reporting

## [0.1.3](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.3) (2020-01-16)

### Features

- Polaris sdk version upgraded to v0.3.3
- remove boot log

## [0.1.2](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.2) (2020-01-16)

### Features

- Polaris sdk version upgraded to v0.3.2

## [0.1.1](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.1) (2020-01-16)

### Features

- Optimize performance in multi-node situations

## [0.1.0](https://git.woa.com/trpc-go/trpc-naming-polaris/tree/v0.1.0) (2020-01-13)

### Features

- Support Polaris addressing mode
- Support multi-environment function
