# full version at https://golangci-lint.run/usage/configuration/
linters-settings:
  funlen:
    lines: 80
    statements: 80
  goconst:
    min-len: 2
    min-occurrences: 2
  gocyclo:
    min-complexity: 20
  goimports:
    local-prefixes: git.code.oa.com/trpc-go/trpc-naming-polaris
  golint:
    #min-confidence: 0
  govet:
    check-shadowing: true
  lll:
    line-length: 120
  errcheck:
    check-type-assertions: true
  gocritic:
    enabled-checks:
      - nestingReduce
    settings:
      nestingReduce:
        bodyWidth: 5

linters:
  disable-all: true
  enable:
    - deadcode
    - funlen
    - goconst
    - gocyclo
    - gofmt
    - ineffassign
    - staticcheck
    - structcheck # When a non-exported structure is embedded in another structure, it will not be detected when the previous structure is used. This requires each business to shield itself
    - typecheck
    - goimports
    - golint
    - gosimple
    - govet
    - lll
    - rowserrcheck
    - errcheck
    - unused
    - varcheck
    - sqlclosecheck
    # - bodyclose https://github.com/timakin/bodyclose/issues  don't use it.

run:
  timeout: 20m

issues:
  exclude-use-default: true

    # The list of ids of default excludes to include or disable. By default it's empty.
    # The following rules, golangci-lint thinks they should be shielded, but we choose not to. Therefore, `exclude-use-default: true` shields a part, and fishes out the following.
  # There are some in the ignore list maintained by golanglint-ci that we don't want to block, so take them out.
  #Let me say here that using a whitelist is better than a blacklist. List As golanglint-ci introduces more tools, we follow up to enjoy the benefits. When we engage in blacklisting, we have to maintain it ourselves. It is better to maintain it with golanglint-ci.
  include:
    - EXC0002 # golint (comment on exported (method|function|type|const)|should have( a package)? comment|comment should be of the form)
    - EXC0003 # golint func name will be used as test\.Test.* by other packages, and that stutters; consider calling this
    - EXC0004 # govet (possible misuse of unsafe.Pointer|should have signature)
    - EXC0005 # staticcheck ineffective break statement. Did you mean to break out of the outer loop
    - EXC0011 # stylecheck (comment on exported (method|function|type|const)|should have( a package)? comment|comment should be of the form)

  exclude-rules:
    - path: _test\.go
      linters:
        - funlen # The specification says that a single test function can have up to 160 lines, but the tool is not easy to distinguish, so here we don’t check the length of the single test function directly
    - linters:
        - staticcheck
      text: "SA1019: package github.com/golang/protobuf" # it reports 'SA1019: package github.com/golang/protobuf/proto is deprecated: Use the "google.golang.org/protobuf/proto" package instead.', but this library update is very stable
    - linters:
        - staticcheck
      text: "SA6002: argument should be pointer-like to avoid allocations" # sync.pool.Put(buf), slice `var buf []byte` will tiger this
    - linters:
        - structcheck
      text: "Store` is unused" # Unique to documentation projects, when a non-exported struct embeds another struct, the previous struct is used without detection
    - linters:
        - lll
      source: "^//go:generate " # Exclude lll issues for long lines with go:generate

  max-same-issues: 0
  new: false
  max-issues-per-linter: 0

output:
  sort-results: true

service:
  golangci-lint-version: 1.28.x
