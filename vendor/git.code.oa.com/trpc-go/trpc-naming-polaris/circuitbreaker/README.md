# 熔断器插件

在 trpc-go 框架的配置文件中增加以下配置：
```yaml
selector:  # Configuration for trpc framework service discovery.
  polaris:  # Configuration for Polaris Service Discovery.
    circuitbreaker:
      enable: true  # Whether to enable the circuit breaker feature, default value: true, this feature is supported in versions >=v0.5.18.
      checkPeriod: 30s  # Instance timing circuit breaker detection period, default value: 30s.
      requestCountAfterHalfOpen: 10  # The maximum number of requests allowed after the circuit breaker is half-opened, default value: 10.
      sleepWindow: 30s  # After the circuit breaker is opened, how long does it take to switch to the half-open state, default value: 30s.
      successCountAfterHalfOpen: 8  # The minimum number of successful requests necessary for the circuit breaker to be closed from half open, default value: 8.
      chain:  # Circuit breaking strategy, default value: [errorCount, errorRate].
        - errorCount  # Circuit breaker based on cycle error rate.
        - errorRate  # Circuit breaker based on cycle consecutive error count.
      errorCount:
        continuousErrorThreshold: 10  # Threshold to trigger continuous error circuit breaker, default value: 10.
        metricNumBuckets: 10  # The minimum number of statistical units for consecutive errors, default value: 10.
        metricStatTimeWindow: 1m0s  # Continuous failure statistics period, default value: 1m.
      errorRate:
        errorRateThreshold: 0.5  # Threshold to trigger error rate fusing, default value: 0.5.
        metricNumBuckets: 5  # The minimum number of statistical units for error rate fusing, default value: 5.
        metricStatTimeWindow: 1m0s  # Statistical period of error rate fusing, default value: 1m.
        requestVolumeThreshold: 10  # The minimum request threshold to trigger the error rate circuit breaker, default value: 10.
```

注意需要同时在代码中引入 trpc-naming-polaris：
```go
import (
    _ "git.code.oa.com/trpc-go/trpc-naming-polaris"
)

```

熔断器的逻辑是根据当前主调调用情况上报到北极星 SDK，当达到熔断标准时，SDK 将对应的节点熔断。

在执行上报时，有一些错误会被过滤掉，即熔断器不会将这些当成熔断错误上报。
插件中定义了四种类型用来确定当前错误的上报行为：
```go
// Should determines whether it should be counted as a circuit breaker error.
type Should int

// True/False indicates whether the current error should be counted as a circuit breaker error.
// Ignore means that the current error should be ignored by the circuit breaker,
// and no report will be made.
// Unknown means that we don't know whether it should be regarded as a circuit breaker error,
// and it will be handed over to the next function to judge.
const (
    Unknown Should = iota
    True
    False
    Ignore
)
```
插件中默认的过滤逻辑为：
```go
// newShouldCircuitBreak creates a default circuit breaker error checking strategy, which is also a bottom-up strategy.
var newShouldCircuitBreak = func(minClientTimeout time.Duration) func(error, time.Duration) Should {
    return func(err error, cost time.Duration) Should {
        if e, ok := err.(*errs.Error); ok &&
            e.Type == errs.ErrorTypeFramework &&
            (e.Code == errs.RetClientConnectFail ||
                e.Code == errs.RetClientNetErr ||
                e.Code == errs.RetClientTimeout && cost >= minClientTimeout) {
            return True
        }
        return False
    }
}
```
用户也可以自定义 shouldCircuitBreak 函数，并通过 ShouldCircuitBreaker 函数来注册。
注意 ShouldCircuitBreaker 的调用需要在 trpc-go 的配置加载之前，
这样才能让在注册 circuitbreaker 的时候使用到替换后的 shouldCircuitBreak。
示例：
```go
// Package main is the client main package.
package main

import (
    "strings"
	
    "git.code.oa.com/trpc-go/trpc-go"
    "git.code.oa.com/trpc-go/trpc-go/client"
    "git.code.oa.com/trpc-go/trpc-go/log"
    pb "git.code.oa.com/trpc-go/trpc-go/testdata"
    _ "git.code.oa.com/trpc-go/trpc-naming-polaris"
    pcircuitbreaker "git.code.oa.com/trpc-go/trpc-naming-polaris/circuitbreaker"
)

func main() {
    // 在加载配置之前，先调用 ShouldCircuitBreak 自定义用户方法。
    pcircuitbreaker.ShouldCircuitBreak(
        // 这个错误过滤方法由用户来定义
        func(err error) pcircuitbreaker.Should {
            errStr := err.Error()
            if strings.HasPrefix(errStr, "true") {
                return pcircuitbreaker.True
            } else if strings.HasPrefix(errStr, "false") {
                return pcircuitbreaker.False
            } else if strings.HasPrefix(errStr, "ignore") {
                return pcircuitbreaker.Ignore
            } else {
                return pcircuitbreaker.Unknown
            }
        },
    )
	
    // 加载配置
    _ = trpc.NewServer()
    ctx := trpc.BackgroundContext()
    hello, err := pb.NewGreeterClientProxy().SayHello(ctx, &pb.HelloRequest{
        Msg: "client",
    },
        client.WithServiceName("trpc.test.helloworld.Greeter"),
        client.WithNamespace("Development"),
    )
    if err != nil {
        log.Fatal(err)
    }
    log.Infof("recv rsp: %s", hello.Msg)
}
```

在用户自定义了错误过滤方法之后，插件中最终的执行逻辑是：
1. 先使用用户提供的方法进行过滤，根据返回值判断上报情况。
2. 如果用户方法返回值为 Unknown，则会再走一次插件默认的 shouldCircuitBreak 方法过滤，用来兜底。
3. 如果返回值还是 Unknown，则会跳过这次上报。
