package servicerouter

import "context"

// WithDisableNearbyRouter marks ctx as nearby router disabled.
// This option is different from client.WithXxx options. It should be used to wrap the original ctx:
//
//	clientProxy = pb.HewHelloClientProxy()
//	rsp, err := clientProxy.Hello(servicerouter.WithDisableNearbyRouter(ctx), req)
//
// Warning, this option has no effect if you use tRPC-Go target mod,
// i.e. client.WithTarget or yaml.client.service[i].target.
func WithDisableNearbyRouter(ctx context.Context) context.Context {
	return context.WithValue(ctx, nearbyRouterDisabled{}, struct{}{})
}

type nearbyRouterDisabled struct{}
