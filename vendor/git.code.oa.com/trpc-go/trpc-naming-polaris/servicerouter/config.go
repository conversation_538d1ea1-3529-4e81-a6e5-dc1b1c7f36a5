package servicerouter

// Config configuration.
type Config struct {
	// Name is the current name of plugin.
	Name string
	// Enable configures whether to enable the service routing function.
	Enable bool
	// EnableCanary configures whether to enable the canary function.
	EnableCanary bool
	// NeedReturnAllNodes expands all nodes into registry.Node and return.
	NeedReturnAllNodes bool
}

const (
	setEnableKey   string = "internal-enable-set"
	setNameKey     string = "internal-set-name"
	setEnableValue string = "Y"
)
