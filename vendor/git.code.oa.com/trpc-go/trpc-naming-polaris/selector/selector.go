// Package selector is a selector.
package selector

import (
	"fmt"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/polaris/polaris-go/pkg/config"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/circuitbreaker"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/servicerouter"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.code.oa.com/trpc-go/trpc-go/naming/selector"
)

const (
	// DefaultConnectTimeout Default connection timeout.
	DefaultConnectTimeout = time.Second
	// DefaultMessageTimeout Default message timeout.
	DefaultMessageTimeout = time.Second
)

var once = &sync.Once{}

// Setup is for setting up.
func Setup(sdkCtx api.SDKContext, cfg *Config) error {
	s := &Selector{
		consumer: api.NewConsumerAPIByContext(sdkCtx),
		cfg:      cfg,
	}
	const defaultName = "polaris"
	if cfg.Name == "" {
		cfg.Name = defaultName
	}
	selector.Register(cfg.Name, s)
	return nil
}

// RegisterDefault registers the default selector.
func RegisterDefault() {
	once.Do(func() {
		s, err := New(&Config{
			Protocol:   "grpc",
			Enable:     true,
			UseBuildin: true,
		})
		if err != nil {
			panic(err)
		}
		selector.Register("polaris", s)
	})
}

// Register registers selector according to parameters.
func Register(cfg *Config) {
	once.Do(func() {
		s, err := New(cfg)
		if err != nil {
			panic(err)
		}
		selector.Register("polaris", s)
	})
}

// New new instance.
func New(cfg *Config) (*Selector, error) {
	c := api.NewConfiguration()
	if !cfg.UseBuildin {
		c.GetGlobal().GetServerConnector().SetAddresses(cfg.ServerAddrs)
	}
	if cfg.JoinPoint != "" {
		c.GetGlobal().GetServerConnector().SetJoinPoint(cfg.JoinPoint)
	}
	if cfg.Protocol == "" {
		cfg.Protocol = "grpc"
	}
	c.GetGlobal().GetServerConnector().SetProtocol(cfg.Protocol)
	if cfg.RefreshInterval != 0 {
		refreshInterval := time.Duration(cfg.RefreshInterval) * time.Millisecond
		c.GetConsumer().GetLocalCache().SetServiceRefreshInterval(refreshInterval)
	}
	if cfg.Timeout != 0 {
		timeout := time.Duration(cfg.Timeout) * time.Millisecond
		c.GetGlobal().GetAPI().SetTimeout(timeout)
		// If timeout is set, the maximum number of retries needs to be set to 0.
		c.GetGlobal().GetAPI().SetMaxRetryTimes(0)
	}
	// Set local IP
	if cfg.BindIP != "" {
		c.GetGlobal().GetAPI().SetBindIP(cfg.BindIP)
	}

	connectTimeout := DefaultConnectTimeout
	if cfg.ConnectTimeout != 0 {
		connectTimeout = time.Millisecond * time.Duration(cfg.ConnectTimeout)
	}
	c.GetGlobal().GetServerConnector().SetConnectTimeout(connectTimeout)

	// Set chain of service router
	chain := c.GetConsumer().GetServiceRouter().GetChain()
	// Add a plugin to filter according to the called service env.
	chain = append([]string{config.DefaultServiceRouterDstMeta}, chain...)
	// Add canary routing chain.
	if cfg.EnableCanary {
		chain = append(chain, config.DefaultServiceRouterCanary)
	}
	c.GetConsumer().GetServiceRouter().SetChain(chain)

	// Configure the local cache storage address.
	if cfg.LocalCachePersistDir != "" {
		c.GetConsumer().GetLocalCache().SetPersistDir(cfg.LocalCachePersistDir)
	}
	sdkCtx, err := api.InitContextByConfig(c)
	if err != nil {
		return nil, err
	}
	return &Selector{
		consumer: api.NewConsumerAPIByContext(sdkCtx),
		cfg:      cfg,
	}, nil
}

// Selector is route selector.
type Selector struct {
	consumer api.ConsumerAPI
	cfg      *Config
}

func getMetadata(opts *selector.Options, enableTransMeta bool) map[string]string {
	metadata := make(map[string]string)
	if len(opts.SourceEnvName) > 0 {
		metadata["env"] = opts.SourceEnvName
	}
	// To solve the problem that the transparent transmission field of
	// the request cannot be passed to Polaris for meta matching,
	// agree on the transparent transmission field with the prefix 'selector-meta-',
	// remove the prefix and fill in meta, and use it for Polaris matching.
	if enableTransMeta {
		setTransSelectorMeta(opts, metadata)
	}
	for key, value := range opts.SourceMetadata {
		if len(key) > 0 && len(value) > 0 {
			metadata[key] = value
		}
	}
	return metadata
}

func extractSourceServiceRequestInfo(opts *selector.Options, enableTransMeta bool) *model.ServiceInfo {
	if opts.DisableServiceRouter {
		return nil
	}
	metadata := getMetadata(opts, enableTransMeta)

	if opts.SourceServiceName != "" || opts.SourceNamespace != "" || len(metadata) > 0 {
		// When the calling service is not empty, or metadata is not empty, return ServiceInfo.
		return &model.ServiceInfo{
			Service:   opts.SourceServiceName,
			Namespace: opts.SourceNamespace,
			Metadata:  metadata,
		}
	}
	return nil
}

func getDestMetadata(opts *selector.Options) map[string]string {
	destMeta := make(map[string]string)
	// Support environment selection when service routing is not enabled.
	if opts.DisableServiceRouter {
		if len(opts.DestinationEnvName) > 0 {
			destMeta["env"] = opts.DestinationEnvName
		}
	}
	// Support custom metadata tag key passed to polaris for addressing.
	for key, value := range opts.DestinationMetadata {
		if len(key) > 0 && len(value) > 0 {
			destMeta[key] = value
		}
	}
	return destMeta
}

// Select selects service node.
func (s *Selector) Select(serviceName string, opt ...selector.Option) (*registry.Node, error) {
	opts := &selector.Options{}
	for _, o := range opt {
		o(opts)
	}
	log.Tracef("[NAMING-POLARIS] select options: %+v", opts)

	namespace := opts.Namespace
	var sourceService *model.ServiceInfo

	if s.cfg.Enable {
		sourceService = extractSourceServiceRequestInfo(opts, s.cfg.EnableTransMeta)
	}
	if sourceService != nil && s.cfg.DisableSourceServiceNameRouteRule {
		// Polaris-go will use the source service name to find the route rule for caller/source service.
		// Setting the service name to an empty string will disable the caller/source route rule,
		// so that it will not require the source service be registered in polaris.
		sourceService.Service = ""
	}
	if opts.LoadBalanceType == "" {
		opts.LoadBalanceType = LoadBalanceWR
	}
	name, ok := loadBalanceMap[opts.LoadBalanceType]
	if !ok {
		// May fallback to the original name defined in polaris-go.
		name = opts.LoadBalanceType
	}
	destMeta := getDestMetadata(opts)
	var hashKey []byte
	if opts.Key != "" {
		hashKey = []byte(opts.Key)
	}
	req := &api.GetOneInstanceRequest{
		GetOneInstanceRequest: model.GetOneInstanceRequest{
			Service:        serviceName,
			Namespace:      namespace,
			SourceService:  sourceService,
			Metadata:       destMeta,
			LbPolicy:       name,
			ReplicateCount: opts.Replicas,
			Canary:         getCanaryValue(opts),
			HashKey:        hashKey,
		},
	}
	if s.cfg.MetaFailOverMode > 0 {
		req.EnableFailOverDefaultMeta = true
		req.FailOverDefaultMeta = model.FailOverDefaultMetaConfig{
			Type: model.FailOverHandler(s.cfg.MetaFailOverMode),
		}
	}
	resp, err := s.consumer.GetOneInstance(req)
	if err != nil {
		return nil, fmt.Errorf("get one instance err: %s", err.Error())
	}
	if len(resp.Instances) == 0 {
		return nil, fmt.Errorf("get one instance return empty")
	}
	inst := resp.Instances[0]
	var setName, containerName string
	if md := inst.GetMetadata(); md != nil {
		containerName = md[containerKey]
		if containerName == "" && md[instanceLabelPlatformKey] == tkexPlatform {
			containerName = md[instanceLabelPodNameKey]
		}
		if enable := md[setEnableKey]; enable == setEnableValue {
			setName = md[setNameKey]
		}
	}
	return &registry.Node{
		ContainerName: containerName,
		SetName:       setName,
		Address:       net.JoinHostPort(inst.GetHost(), strconv.Itoa(int(inst.GetPort()))),
		ServiceName:   serviceName,
		Weight:        inst.GetWeight(),
		Metadata: map[string]interface{}{
			"instance":  inst,
			"service":   serviceName,
			"namespace": namespace,
		},
	}, nil
}

// GetConsumer gets the consumerAPI instance of the selector.
func (s *Selector) GetConsumer() api.ConsumerAPI {
	return s.consumer
}

// GetCfg gets selector Config configuration.
func (s *Selector) GetCfg() *Config {
	return s.cfg
}

// Report reports the service status.
func (s *Selector) Report(node *registry.Node, cost time.Duration, err error) error {
	return circuitbreaker.Report(s.consumer, node, s.cfg.ReportTimeout, cost, err)
}

func getCanaryValue(opts *selector.Options) string {
	if opts.Ctx == nil {
		return ""
	}
	ctx := opts.Ctx
	msg := codec.Message(ctx)
	metaData := msg.ClientMetaData()
	if metaData == nil {
		return ""
	}
	return string(metaData[servicerouter.CanaryKey])
}

func setTransSelectorMeta(opts *selector.Options, selectorMeta map[string]string) {
	if opts.Ctx == nil {
		return
	}
	msg := codec.Message(opts.Ctx)
	for k, v := range msg.ServerMetaData() {
		if strings.HasPrefix(k, selectorMetaPrefix) {
			trimmedKey := strings.TrimPrefix(k, selectorMetaPrefix)
			selectorMeta[trimmedKey] = string(v)
		}
	}
	for k, v := range msg.ClientMetaData() {
		if strings.HasPrefix(k, selectorMetaPrefix) {
			trimmedKey := strings.TrimPrefix(k, selectorMetaPrefix)
			selectorMeta[trimmedKey] = string(v)
		}
	}
}
