# route selector plugin

An implementation of trpc-selector that provides trpc users with Polaris for routing and load balancing.

```go
package main

import (
    "context"
    "log"
    "time"

    "git.code.oa.com/trpc-go/trpc-go/client"
    "git.code.oa.com/trpc-go/trpc-naming-polaris/selector"

    pb "git.code.oa.com/trpcprotocol/test/helloworld"

    _ "git.code.oa.com/trpc-go/trpc-go"
)

func init() {
    selector.RegisterDefault()
}

func main() {
    ctx, cancel := context.WithTimeout(context.TODO(), time.Millisecond*2000)
    defer cancel()

    opts := []client.Option{
        client.WithNamespace("Development"),
        client.WithTarget("polaris://trpc.app.server.service"),
    }

    clientProxy := pb.NewGreeterClientProxy(opts...)

    req := &pb.HelloRequest{
        Msg: "client hello",
    }
    rsp, err := clientProxy.<PERSON><PERSON><PERSON>(ctx, req)
    log.Printf("req:%v, rsp:%v, err:%v", req, rsp, err)
}
```
