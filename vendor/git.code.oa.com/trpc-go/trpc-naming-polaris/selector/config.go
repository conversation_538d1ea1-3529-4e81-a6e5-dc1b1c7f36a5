package selector

import (
	"time"

	"git.code.oa.com/polaris/polaris-go/pkg/config"
)

// Config selector configuration structure
type Config struct {
	// Name is the current name of plugin.
	Name string
	// RefreshInterval is the time to refresh the list, in ms.
	RefreshInterval int
	// ServerAddrs is service address.
	ServerAddrs []string
	// Protocol is protocol type.
	Protocol string
	// Enable is the state of the ServiceRouter.
	Enable bool
	// Timeout obtains information Polaris background timeout, in ms.
	Timeout int
	// ConnectTimeout is the timeout of connecting to Polaris background, in ms.
	ConnectTimeout int
	// Enable  is the state of the canary.
	EnableCanary bool
	// UseBuildin indicates whether to use the sdk default buried address.
	UseBuildin bool
	// ReportTimeout If ReportTimeout is set, when the downstream times out and the time is less than the set value,
	// the error will be ignored and not reported.
	ReportTimeout *time.Duration
	// EnableTransMeta When the setting is enabled,
	// remove the prefix from the transparent transmission field prefixed with 'selector-meta-'
	// and fill in the MetaData of SourceService.
	EnableTransMeta bool
	// MetaFailOverMode When selecting with metadata and find no matching instance, this config
	// works as:
	//   1: get one of all available instances.
	//   2: get one of all available instances which doesn't match requests metadata keys.
	MetaFailOverMode int
	// Set the local cache storage address.
	LocalCachePersistDir string
	// Set the local IP address.
	BindIP string
	// JoinPoint Set the access point, which will override the value set by UseBuildin.
	JoinPoint string
	// DisableSourceServiceNameRouteRule Disable the source service name route rule.
	// The default value is false.
	DisableSourceServiceNameRouteRule bool
}

const (
	setEnableKey       string = "internal-enable-set"
	setNameKey         string = "internal-set-name"
	setEnableValue     string = "Y"
	containerKey       string = "container_name"
	selectorMetaPrefix string = "selector-meta-"

	instanceLabelPlatformKey = "platform"
	instanceLabelPodNameKey  = "podName"
	tkexPlatform             = "TKEx"
)

// Deprecated: you can always use the original string defined in polaris-go.
const (
	// LoadBalanceWR is the random weight lb. It's default.
	LoadBalanceWR = "polaris_wr"
	// LoadBalanceHash is the common hash lb.
	LoadBalanceHash = "polaris_hash"
	// LoadBalancerRingHash is the lb based on consistent hash ring.
	LoadBalancerRingHash = "polaris_ring_hash"
	// LoadBalanceMaglev is the lb based on maglev hash.
	LoadBalanceMaglev = "polaris_maglev"
	// LoadBalanceL5Cst is the lb which is compatible with l5 consistent hash.
	LoadBalanceL5Cst = "polaris_l5cst"
	// LoadBalanceDWR is the dynamic weight lb.
	LoadBalanceDWR = "polaris_dwr"
	// LoadBalancerBrpcMurmur is the brpc c_murmur hash.
	LoadBalancerBrpcMurmur string = "polaris_cmurmur"
)

// Deprecated: you can always use the original string defined in polaris-go.
var loadBalanceMap map[string]string = map[string]string{
	LoadBalanceWR:          config.DefaultLoadBalancerWR,
	LoadBalanceHash:        config.DefaultLoadBalancerHash,
	LoadBalancerRingHash:   config.DefaultLoadBalancerRingHash,
	LoadBalanceMaglev:      config.DefaultLoadBalancerMaglev,
	LoadBalanceL5Cst:       config.DefaultLoadBalancerL5CST,
	LoadBalanceDWR:         config.DefaultLoadBalancerDWR,
	LoadBalancerBrpcMurmur: config.DefaultLoadBalancerBrpcMurmur,
}
