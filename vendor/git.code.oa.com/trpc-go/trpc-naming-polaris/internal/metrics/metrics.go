// Package metrics 定义和上报 metrics
package metrics

import (
	"strconv"

	"git.code.oa.com/polaris/polaris-go/api"
	plog "git.code.oa.com/polaris/polaris-go/pkg/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

const (
	polarisMetricsKey          = "polaris_metrics"
	polarisServiceKey          = "polaris_service"
	polarisServiceNamespaceKey = "polaris_namespace"
	polarisServiceHostKey      = "polaris_host"
	polarisServicePortKey      = "polaris_port"
)

// ReportHeartBeatFail report service heartbeat fails
func ReportHeartBeatFail(req *api.InstanceHeartbeatRequest) {
	dims := []*metrics.Dimension{
		{
			Name:  polarisServiceKey,
			Value: req.Service,
		},
		{
			Name:  polarisServiceNamespaceKey,
			Value: req.Namespace,
		},
		{
			Name:  polarisServiceHostKey,
			Value: req.Host,
		},
		{
			Name:  polarisServicePortKey,
			Value: strconv.FormatInt(int64(req.Port), 10),
		},
	}
	indices := []*metrics.Metrics{
		metrics.NewMetrics("trpc.PolarisHeartBeatFail", float64(1), metrics.PolicySUM),
	}
	err := metrics.ReportMultiDimensionMetricsX(polarisMetricsKey, dims, indices)
	if err != nil {
		plog.GetBaseLogger().Errorf("heartbeat metrics report err: %v\n", err)
	}
}
