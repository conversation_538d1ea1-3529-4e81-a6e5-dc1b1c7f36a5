// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.15.5
// source: transporter.proto

package oidb_transporter

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg []byte `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *MsgRequest) Reset() {
	*x = MsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_transporter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgRequest) ProtoMessage() {}

func (x *MsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_transporter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgRequest.ProtoReflect.Descriptor instead.
func (*MsgRequest) Descriptor() ([]byte, []int) {
	return file_transporter_proto_rawDescGZIP(), []int{0}
}

func (x *MsgRequest) GetMsg() []byte {
	if x != nil {
		return x.Msg
	}
	return nil
}

type MsgReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg []byte `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *MsgReply) Reset() {
	*x = MsgReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_transporter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgReply) ProtoMessage() {}

func (x *MsgReply) ProtoReflect() protoreflect.Message {
	mi := &file_transporter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgReply.ProtoReflect.Descriptor instead.
func (*MsgReply) Descriptor() ([]byte, []int) {
	return file_transporter_proto_rawDescGZIP(), []int{1}
}

func (x *MsgReply) GetMsg() []byte {
	if x != nil {
		return x.Msg
	}
	return nil
}

var File_transporter_proto protoreflect.FileDescriptor

var file_transporter_proto_rawDesc = []byte{
	0x0a, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x6f, 0x69, 0x63, 0x71, 0x2e, 0x6f,
	0x69, 0x64, 0x62, 0x22, 0x1e, 0x0a, 0x0a, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0x1c, 0x0a, 0x08, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x32, 0x56, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72,
	0x12, 0x47, 0x0a, 0x0d, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x1a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x6f, 0x69, 0x63, 0x71, 0x2e, 0x6f, 0x69,
	0x64, 0x62, 0x2e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x6f, 0x69, 0x63, 0x71, 0x2e, 0x6f, 0x69, 0x64, 0x62, 0x2e, 0x4d,
	0x73, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x32, 0x52, 0x0a, 0x07, 0x49, 0x70, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x12, 0x47, 0x0a, 0x0d, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x6f, 0x69, 0x63,
	0x71, 0x2e, 0x6f, 0x69, 0x64, 0x62, 0x2e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x18, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x6f, 0x69, 0x63, 0x71, 0x2e, 0x6f, 0x69,
	0x64, 0x62, 0x2e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x66, 0x0a,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x6f, 0x69, 0x63, 0x71, 0x2e, 0x6f,
	0x69, 0x64, 0x62, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x50,
	0x01, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x72, 0x70, 0x63, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f,
	0x6f, 0x69, 0x63, 0x71, 0x2f, 0x6f, 0x69, 0x64, 0x62, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_transporter_proto_rawDescOnce sync.Once
	file_transporter_proto_rawDescData = file_transporter_proto_rawDesc
)

func file_transporter_proto_rawDescGZIP() []byte {
	file_transporter_proto_rawDescOnce.Do(func() {
		file_transporter_proto_rawDescData = protoimpl.X.CompressGZIP(file_transporter_proto_rawDescData)
	})
	return file_transporter_proto_rawDescData
}

var file_transporter_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_transporter_proto_goTypes = []interface{}{
	(*MsgRequest)(nil), // 0: trpc.oicq.oidb.MsgRequest
	(*MsgReply)(nil),   // 1: trpc.oicq.oidb.MsgReply
}
var file_transporter_proto_depIdxs = []int32{
	0, // 0: trpc.oicq.oidb.Transporter.HandleProcess:input_type -> trpc.oicq.oidb.MsgRequest
	0, // 1: trpc.oicq.oidb.IpTrans.HandleProcess:input_type -> trpc.oicq.oidb.MsgRequest
	1, // 2: trpc.oicq.oidb.Transporter.HandleProcess:output_type -> trpc.oicq.oidb.MsgReply
	1, // 3: trpc.oicq.oidb.IpTrans.HandleProcess:output_type -> trpc.oicq.oidb.MsgReply
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_transporter_proto_init() }
func file_transporter_proto_init() {
	if File_transporter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_transporter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_transporter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_transporter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_transporter_proto_goTypes,
		DependencyIndexes: file_transporter_proto_depIdxs,
		MessageInfos:      file_transporter_proto_msgTypes,
	}.Build()
	File_transporter_proto = out.File
	file_transporter_proto_rawDesc = nil
	file_transporter_proto_goTypes = nil
	file_transporter_proto_depIdxs = nil
}
