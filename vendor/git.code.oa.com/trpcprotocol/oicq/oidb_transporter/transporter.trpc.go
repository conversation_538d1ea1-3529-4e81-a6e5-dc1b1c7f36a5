// Code generated by trpc-go/trpc-go-cmdline v2.0.16. DO NOT EDIT.
// source: transporter.proto

package oidb_transporter

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// TransporterService defines service
type TransporterService interface {
	HandleProcess(ctx context.Context, req *MsgRequest, rsp *MsgReply) (err error)
}

func TransporterService_HandleProcess_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &MsgRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}, rspbody interface{}) error {
		return svr.(TransporterService).HandleProcess(ctx, reqbody.(*MsgRequest), rspbody.(*MsgReply))
	}

	rsp := &MsgReply{}
	err = filters.Handle(ctx, req, rsp, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// TransporterServer_ServiceDesc descriptor for server.RegisterService
var TransporterServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.oicq.oidb.Transporter",
	HandlerType: ((*TransporterService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.oicq.oidb.Transporter/HandleProcess",
			Func: TransporterService_HandleProcess_Handler,
		},
	},
}

// RegisterTransporterService register service
func RegisterTransporterService(s server.Service, svr TransporterService) {
	if err := s.Register(&TransporterServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("Transporter register error:%v", err))
	}
}

// IpTransService defines service
type IpTransService interface {
	HandleProcess(ctx context.Context, req *MsgRequest, rsp *MsgReply) (err error)
}

func IpTransService_HandleProcess_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &MsgRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}, rspbody interface{}) error {
		return svr.(IpTransService).HandleProcess(ctx, reqbody.(*MsgRequest), rspbody.(*MsgReply))
	}

	rsp := &MsgReply{}
	err = filters.Handle(ctx, req, rsp, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// IpTransServer_ServiceDesc descriptor for server.RegisterService
var IpTransServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.oicq.oidb.IpTrans",
	HandlerType: ((*IpTransService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.oicq.oidb.IpTrans/HandleProcess",
			Func: IpTransService_HandleProcess_Handler,
		},
	},
}

// RegisterIpTransService register service
func RegisterIpTransService(s server.Service, svr IpTransService) {
	if err := s.Register(&IpTransServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("IpTrans register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedTransporter struct{}

func (s *UnimplementedTransporter) HandleProcess(ctx context.Context, req *MsgRequest, rsp *MsgReply) error {
	return errors.New("rpc HandleProcess of service Transporter is not implemented")
}

type UnimplementedIpTrans struct{}

func (s *UnimplementedIpTrans) HandleProcess(ctx context.Context, req *MsgRequest, rsp *MsgReply) error {
	return errors.New("rpc HandleProcess of service IpTrans is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// TransporterClientProxy defines service client proxy
type TransporterClientProxy interface {
	HandleProcess(ctx context.Context, req *MsgRequest, opts ...client.Option) (rsp *MsgReply, err error)
}

type TransporterClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewTransporterClientProxy = func(opts ...client.Option) TransporterClientProxy {
	return &TransporterClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *TransporterClientProxyImpl) HandleProcess(ctx context.Context, req *MsgRequest, opts ...client.Option) (*MsgReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.oicq.oidb.Transporter/HandleProcess")
	msg.WithCalleeServiceName(TransporterServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("oicq")
	msg.WithCalleeServer("oidb")
	msg.WithCalleeService("Transporter")
	msg.WithCalleeMethod("HandleProcess")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &MsgReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// IpTransClientProxy defines service client proxy
type IpTransClientProxy interface {
	HandleProcess(ctx context.Context, req *MsgRequest, opts ...client.Option) (rsp *MsgReply, err error)
}

type IpTransClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewIpTransClientProxy = func(opts ...client.Option) IpTransClientProxy {
	return &IpTransClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *IpTransClientProxyImpl) HandleProcess(ctx context.Context, req *MsgRequest, opts ...client.Option) (*MsgReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.oicq.oidb.IpTrans/HandleProcess")
	msg.WithCalleeServiceName(IpTransServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("oicq")
	msg.WithCalleeServer("oidb")
	msg.WithCalleeService("IpTrans")
	msg.WithCalleeMethod("HandleProcess")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &MsgReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
