syntax = "proto3"; 
package   trpc.oicq.oidb;

//default java package name 
option  java_package="com.tencent.trpcprotocol.oicq.oidb.transporter";
option  java_multiple_files = true; 

//default go package name 
option  go_package ="git.code.oa.com/trpcprotocol/oicq/oidb_transporter";

service Transporter {  // for knocknock鉴权
  rpc HandleProcess (MsgRequest) returns (MsgReply) {}
}

service IpTrans {  // for模块ip鉴权
   rpc HandleProcess (MsgRequest) returns (MsgReply) {}
}

message MsgRequest {
  bytes msg = 1;
}

message MsgReply {
  bytes msg = 1;
} 