// Code generated by MockGen. DO NOT EDIT.
// Source: stub/git.code.oa.com/trpcprotocol/oicq/oidb_transporter/transporter.trpc.go

// Package oidb_transporter is a generated GoMock package.
package oidb_transporter

import (
	context "context"
	reflect "reflect"

	client "git.code.oa.com/trpc-go/trpc-go/client"
	gomock "github.com/golang/mock/gomock"
)

// MockTransporterService is a mock of TransporterService interface.
type MockTransporterService struct {
	ctrl     *gomock.Controller
	recorder *MockTransporterServiceMockRecorder
}

// MockTransporterServiceMockRecorder is the mock recorder for MockTransporterService.
type MockTransporterServiceMockRecorder struct {
	mock *MockTransporterService
}

// NewMockTransporterService creates a new mock instance.
func NewMockTransporterService(ctrl *gomock.Controller) *MockTransporterService {
	mock := &MockTransporterService{ctrl: ctrl}
	mock.recorder = &MockTransporterServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransporterService) EXPECT() *MockTransporterServiceMockRecorder {
	return m.recorder
}

// HandleProcess mocks base method.
func (m *MockTransporterService) HandleProcess(ctx context.Context, req *MsgRequest, rsp *MsgReply) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleProcess", ctx, req, rsp)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleProcess indicates an expected call of HandleProcess.
func (mr *MockTransporterServiceMockRecorder) HandleProcess(ctx, req, rsp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleProcess", reflect.TypeOf((*MockTransporterService)(nil).HandleProcess), ctx, req, rsp)
}

// MockIpTransService is a mock of IpTransService interface.
type MockIpTransService struct {
	ctrl     *gomock.Controller
	recorder *MockIpTransServiceMockRecorder
}

// MockIpTransServiceMockRecorder is the mock recorder for MockIpTransService.
type MockIpTransServiceMockRecorder struct {
	mock *MockIpTransService
}

// NewMockIpTransService creates a new mock instance.
func NewMockIpTransService(ctrl *gomock.Controller) *MockIpTransService {
	mock := &MockIpTransService{ctrl: ctrl}
	mock.recorder = &MockIpTransServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIpTransService) EXPECT() *MockIpTransServiceMockRecorder {
	return m.recorder
}

// HandleProcess mocks base method.
func (m *MockIpTransService) HandleProcess(ctx context.Context, req *MsgRequest, rsp *MsgReply) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleProcess", ctx, req, rsp)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleProcess indicates an expected call of HandleProcess.
func (mr *MockIpTransServiceMockRecorder) HandleProcess(ctx, req, rsp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleProcess", reflect.TypeOf((*MockIpTransService)(nil).HandleProcess), ctx, req, rsp)
}

// MockTransporterClientProxy is a mock of TransporterClientProxy interface.
type MockTransporterClientProxy struct {
	ctrl     *gomock.Controller
	recorder *MockTransporterClientProxyMockRecorder
}

// MockTransporterClientProxyMockRecorder is the mock recorder for MockTransporterClientProxy.
type MockTransporterClientProxyMockRecorder struct {
	mock *MockTransporterClientProxy
}

// NewMockTransporterClientProxy creates a new mock instance.
func NewMockTransporterClientProxy(ctrl *gomock.Controller) *MockTransporterClientProxy {
	mock := &MockTransporterClientProxy{ctrl: ctrl}
	mock.recorder = &MockTransporterClientProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransporterClientProxy) EXPECT() *MockTransporterClientProxyMockRecorder {
	return m.recorder
}

// HandleProcess mocks base method.
func (m *MockTransporterClientProxy) HandleProcess(ctx context.Context, req *MsgRequest, opts ...client.Option) (*MsgReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleProcess", varargs...)
	ret0, _ := ret[0].(*MsgReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleProcess indicates an expected call of HandleProcess.
func (mr *MockTransporterClientProxyMockRecorder) HandleProcess(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleProcess", reflect.TypeOf((*MockTransporterClientProxy)(nil).HandleProcess), varargs...)
}

// MockIpTransClientProxy is a mock of IpTransClientProxy interface.
type MockIpTransClientProxy struct {
	ctrl     *gomock.Controller
	recorder *MockIpTransClientProxyMockRecorder
}

// MockIpTransClientProxyMockRecorder is the mock recorder for MockIpTransClientProxy.
type MockIpTransClientProxyMockRecorder struct {
	mock *MockIpTransClientProxy
}

// NewMockIpTransClientProxy creates a new mock instance.
func NewMockIpTransClientProxy(ctrl *gomock.Controller) *MockIpTransClientProxy {
	mock := &MockIpTransClientProxy{ctrl: ctrl}
	mock.recorder = &MockIpTransClientProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIpTransClientProxy) EXPECT() *MockIpTransClientProxyMockRecorder {
	return m.recorder
}

// HandleProcess mocks base method.
func (m *MockIpTransClientProxy) HandleProcess(ctx context.Context, req *MsgRequest, opts ...client.Option) (*MsgReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleProcess", varargs...)
	ret0, _ := ret[0].(*MsgReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleProcess indicates an expected call of HandleProcess.
func (mr *MockIpTransClientProxyMockRecorder) HandleProcess(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleProcess", reflect.TypeOf((*MockIpTransClientProxy)(nil).HandleProcess), varargs...)
}
