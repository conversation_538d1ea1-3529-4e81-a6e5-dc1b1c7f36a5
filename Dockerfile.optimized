# 多阶段构建：构建阶段
FROM ubuntu:22.04 AS builder

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    wget \
    git \
    build-essential \
    cmake \
    pkg-config \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libgtk-3-dev \
    libatlas-base-dev \
    gfortran \
    && rm -rf /var/lib/apt/lists/*

# 安装 OpenCV 4.11.0
RUN wget -O opencv.tar.gz https://github.com/opencv/opencv/archive/4.11.0.tar.gz && \
    tar xf opencv.tar.gz && \
    cd opencv-4.11.0 && \
    mkdir build && cd build && \
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DCMAKE_INSTALL_PREFIX=/usr/local \
          -DOPENCV_GENERATE_PKGCONFIG=ON \
          -DBUILD_EXAMPLES=OFF \
          -DBUILD_TESTS=OFF \
          -DBUILD_PERF_TESTS=OFF \
          -DBUILD_DOCS=OFF \
          -DBUILD_opencv_apps=OFF \
          -DWITH_FFMPEG=ON \
          -DWITH_GSTREAMER=OFF \
          -DWITH_GTK=ON \
          -DWITH_V4L=ON \
          -DWITH_OPENGL=OFF \
          -DWITH_OPENCL=OFF \
          -DWITH_IPP=OFF \
          -DWITH_TBB=OFF \
          -DBUILD_opencv_python2=OFF \
          -DBUILD_opencv_python3=OFF \
          -DBUILD_SHARED_LIBS=ON \
          -DCMAKE_POSITION_INDEPENDENT_CODE=ON \
          .. && \
    make -j$(nproc) && \
    make install && \
    ldconfig && \
    cd / && rm -rf opencv-4.11.0 opencv.tar.gz

# 安装 Go 1.23.0 (匹配 go.mod)
RUN wget https://go.dev/dl/go1.23.0.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go1.23.0.linux-amd64.tar.gz && \
    rm go1.23.0.linux-amd64.tar.gz

ENV PATH="/usr/local/go/bin:${PATH}"
ENV PKG_CONFIG_PATH="/usr/local/lib/pkgconfig"
ENV LD_LIBRARY_PATH="/usr/local/lib"

WORKDIR /app

# 复制依赖文件
COPY go.mod go.sum ./
COPY vendor/ ./vendor/
COPY stub/ ./stub/

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -mod=vendor -a -installsuffix cgo -o output_error_qq_photos main.go

# 运行阶段：使用更小的基础镜像
FROM ubuntu:22.04

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装运行时依赖（不安装系统的 OpenCV，避免版本冲突）
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libjpeg8 \
    libpng16-16 \
    libtiff5 \
    libavcodec58 \
    libavformat58 \
    libswscale5 \
    libgtk-3-0 \
    libatlas3-base \
    libgfortran5 \
    pkg-config \
    curl \
    procps \
    && rm -rf /var/lib/apt/lists/*

# 创建必要的目录
RUN mkdir -p /usr/local/lib/pkgconfig /app/logs

# 从构建阶段复制 OpenCV 库和相关文件
COPY --from=builder /usr/local/lib/libopencv* /usr/local/lib/
COPY --from=builder /usr/local/lib/pkgconfig/opencv4.pc /usr/local/lib/pkgconfig/
COPY --from=builder /usr/local/include/opencv4/ /usr/local/include/opencv4/

# 从构建阶段复制应用程序
COPY --from=builder /app/output_error_qq_photos /app/output_error_qq_photos
COPY --from=builder /app/application.yaml /app/application.yaml

WORKDIR /app

# 设置环境变量
ENV LD_LIBRARY_PATH="/usr/local/lib"
ENV PKG_CONFIG_PATH="/usr/local/lib/pkgconfig"
ENV OPENCV_LOG_LEVEL=ERROR

# 更新动态链接器缓存
RUN ldconfig

# 创建非 root 用户（安全最佳实践）
RUN groupadd -r appuser && useradd -r -g appuser -d /app -s /bin/bash appuser && \
    chown -R appuser:appuser /app

# 切换到非 root 用户
USER appuser

# 暴露端口
EXPOSE 6610

# 健康检查（检查进程是否运行）
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD pgrep -f output_error_qq_photos > /dev/null || exit 1

# 运行应用
CMD ["./output_error_qq_photos"]
