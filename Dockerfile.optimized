# 多阶段构建：构建阶段
FROM ubuntu:22.04 AS builder

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    wget \
    git \
    build-essential \
    cmake \
    pkg-config \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libgtk-3-dev \
    libatlas-base-dev \
    gfortran \
    && rm -rf /var/lib/apt/lists/*

# 安装 OpenCV 4.11.0
RUN wget -O opencv.tar.gz https://github.com/opencv/opencv/archive/4.11.0.tar.gz && \
    tar xf opencv.tar.gz && \
    cd opencv-4.11.0 && \
    mkdir build && cd build && \
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DCMAKE_INSTALL_PREFIX=/usr/local \
          -DOPENCV_GENERATE_PKGCONFIG=ON \
          -DBUILD_EXAMPLES=OFF \
          -DBUILD_TESTS=OFF \
          -DBUILD_PERF_TESTS=OFF \
          -DBUILD_DOCS=OFF \
          -DWITH_FFMPEG=ON \
          .. && \
    make -j$(nproc) && \
    make install && \
    cd / && rm -rf opencv-4.11.0 opencv.tar.gz

# 安装 Go 1.23.0 (匹配 go.mod)
RUN wget https://go.dev/dl/go1.23.0.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go1.23.0.linux-amd64.tar.gz && \
    rm go1.23.0.linux-amd64.tar.gz

ENV PATH="/usr/local/go/bin:${PATH}"
ENV PKG_CONFIG_PATH="/usr/local/lib/pkgconfig"
ENV LD_LIBRARY_PATH="/usr/local/lib"

WORKDIR /app

# 复制依赖文件
COPY go.mod go.sum ./
COPY vendor/ ./vendor/
COPY stub/ ./stub/

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -mod=vendor -a -installsuffix cgo -o output_error_qq_photos main.go

# 运行阶段：使用更小的基础镜像
FROM ubuntu:22.04

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libopencv-core4.5d \
    libopencv-imgproc4.5d \
    libopencv-imgcodecs4.5d \
    libopencv-highgui4.5d \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制 OpenCV 库
COPY --from=builder /usr/local/lib/libopencv* /usr/local/lib/
COPY --from=builder /usr/local/lib/pkgconfig/opencv4.pc /usr/local/lib/pkgconfig/

# 从构建阶段复制应用程序
COPY --from=builder /app/output_error_qq_photos /app/output_error_qq_photos
COPY --from=builder /app/application.yaml /app/application.yaml

WORKDIR /app

# 设置环境变量
ENV LD_LIBRARY_PATH="/usr/local/lib"

# 暴露端口（如果需要）
EXPOSE 6610

# 运行应用
CMD ["./output_error_qq_photos"]
