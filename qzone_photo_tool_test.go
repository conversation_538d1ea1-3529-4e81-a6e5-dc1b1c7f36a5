package main

import (
	"context"
	"reflect"
	"testing"

	_ "git.code.oa.com/trpc-go/trpc-go/http"

	"github.com/golang/mock/gomock"

	"git.woa.com/fanniliu/output_error_qq_photos/internal/service"
	pb "git.woa.com/trpcprotocol/zy_socail/qq_photos"
)

//go:generate go mod tidy
//go:generate mockgen -destination=stub/git.woa.com/trpcprotocol/zy_socail/qq_photos/output_error_qq_photos_mock.go -package=qq_photos -self_package=git.woa.com/trpcprotocol/zy_socail/qq_photos --source=stub/git.woa.com/trpcprotocol/zy_socail/qq_photos/output_error_qq_photos.trpc.go

func Test_qzonePhotoToolImpl_RawFixTool(t *testing.T) {
	// 开始写mock逻辑
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	qzonePhotoToolService := pb.NewMockQzonePhotoToolService(ctrl)
	var inorderClient []*gomock.Call
	// 预期行为
	m := qzonePhotoToolService.EXPECT().RawFixTool(gomock.Any(), gomock.Any()).AnyTimes()
	m.DoAndReturn(func(ctx context.Context, req *pb.RawFixToolRequest) (*pb.RawFixToolReply, error) {
		s := service.NewQzonePhotoToolImpl()
		return s.RawFixTool(ctx, req)
	})
	gomock.InOrder(inorderClient...)

	// 开始写单元测试逻辑
	type args struct {
		ctx context.Context
		req *pb.RawFixToolRequest
		rsp *pb.RawFixToolReply
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var rsp *pb.RawFixToolReply
			var err error
			if rsp, err = qzonePhotoToolService.RawFixTool(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("qzonePhotoToolImpl.RawFixTool() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !reflect.DeepEqual(rsp, tt.args.rsp) {
				t.Errorf("qzonePhotoToolImpl.RawFixTool() rsp got = %v, want %v", rsp, tt.args.rsp)
			}
		})
	}
}
