// Code generated by trpc-go/trpc-go-cmdline v2.8.31. DO NOT EDIT.
// source: output_error_qq_photos.proto

package qq_photos

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// QzonePhotoToolService defines service.
type QzonePhotoToolService interface {
	RawFixTool(ctx context.Context, req *RawFixToolRequest) (*RawFixToolReply, error)
}

func QzonePhotoToolService_RawFixTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RawFixToolRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(QzonePhotoToolService).RawFixTool(ctx, reqbody.(*RawFixToolRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// QzonePhotoToolServer_ServiceDesc descriptor for server.RegisterService.
var QzonePhotoToolServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.zy_socail.qq_photos.QzonePhotoTool",
	HandlerType: ((*QzonePhotoToolService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool",
			Func: QzonePhotoToolService_RawFixTool_Handler,
		},
	},
}

// RegisterQzonePhotoToolService registers service.
func RegisterQzonePhotoToolService(s server.Service, svr QzonePhotoToolService) {
	if err := s.Register(&QzonePhotoToolServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("QzonePhotoTool register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedQzonePhotoTool struct{}

func (s *UnimplementedQzonePhotoTool) RawFixTool(ctx context.Context, req *RawFixToolRequest) (*RawFixToolReply, error) {
	return nil, errors.New("rpc RawFixTool of service QzonePhotoTool is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// QzonePhotoToolClientProxy defines service client proxy
type QzonePhotoToolClientProxy interface {
	RawFixTool(ctx context.Context, req *RawFixToolRequest, opts ...client.Option) (rsp *RawFixToolReply, err error)
}

type QzonePhotoToolClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewQzonePhotoToolClientProxy = func(opts ...client.Option) QzonePhotoToolClientProxy {
	return &QzonePhotoToolClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *QzonePhotoToolClientProxyImpl) RawFixTool(ctx context.Context, req *RawFixToolRequest, opts ...client.Option) (*RawFixToolReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.zy_socail.qq_photos.QzonePhotoTool/RawFixTool")
	msg.WithCalleeServiceName(QzonePhotoToolServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("zy_socail")
	msg.WithCalleeServer("qq_photos")
	msg.WithCalleeService("QzonePhotoTool")
	msg.WithCalleeMethod("RawFixTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RawFixToolReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
