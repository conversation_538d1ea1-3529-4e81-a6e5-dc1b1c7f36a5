// Code generated by MockGen. DO NOT EDIT.
// Source: stub/git.woa.com/trpcprotocol/zy_socail/qq_photos/output_error_qq_photos.trpc.go

// Package qq_photos is a generated GoMock package.
package qq_photos

import (
	context "context"
	reflect "reflect"

	client "git.code.oa.com/trpc-go/trpc-go/client"
	gomock "github.com/golang/mock/gomock"
)

// MockQzonePhotoToolService is a mock of QzonePhotoToolService interface.
type MockQzonePhotoToolService struct {
	ctrl     *gomock.Controller
	recorder *MockQzonePhotoToolServiceMockRecorder
}

// MockQzonePhotoToolServiceMockRecorder is the mock recorder for MockQzonePhotoToolService.
type MockQzonePhotoToolServiceMockRecorder struct {
	mock *MockQzonePhotoToolService
}

// NewMockQzonePhotoToolService creates a new mock instance.
func NewMockQzonePhotoToolService(ctrl *gomock.Controller) *MockQzonePhotoToolService {
	mock := &MockQzonePhotoToolService{ctrl: ctrl}
	mock.recorder = &MockQzonePhotoToolServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQzonePhotoToolService) EXPECT() *MockQzonePhotoToolServiceMockRecorder {
	return m.recorder
}

// RawFixTool mocks base method.
func (m *MockQzonePhotoToolService) RawFixTool(ctx context.Context, req *RawFixToolRequest) (*RawFixToolReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RawFixTool", ctx, req)
	ret0, _ := ret[0].(*RawFixToolReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RawFixTool indicates an expected call of RawFixTool.
func (mr *MockQzonePhotoToolServiceMockRecorder) RawFixTool(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawFixTool", reflect.TypeOf((*MockQzonePhotoToolService)(nil).RawFixTool), ctx, req)
}

// MockQzonePhotoToolClientProxy is a mock of QzonePhotoToolClientProxy interface.
type MockQzonePhotoToolClientProxy struct {
	ctrl     *gomock.Controller
	recorder *MockQzonePhotoToolClientProxyMockRecorder
}

// MockQzonePhotoToolClientProxyMockRecorder is the mock recorder for MockQzonePhotoToolClientProxy.
type MockQzonePhotoToolClientProxyMockRecorder struct {
	mock *MockQzonePhotoToolClientProxy
}

// NewMockQzonePhotoToolClientProxy creates a new mock instance.
func NewMockQzonePhotoToolClientProxy(ctrl *gomock.Controller) *MockQzonePhotoToolClientProxy {
	mock := &MockQzonePhotoToolClientProxy{ctrl: ctrl}
	mock.recorder = &MockQzonePhotoToolClientProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQzonePhotoToolClientProxy) EXPECT() *MockQzonePhotoToolClientProxyMockRecorder {
	return m.recorder
}

// RawFixTool mocks base method.
func (m *MockQzonePhotoToolClientProxy) RawFixTool(ctx context.Context, req *RawFixToolRequest, opts ...client.Option) (*RawFixToolReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RawFixTool", varargs...)
	ret0, _ := ret[0].(*RawFixToolReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RawFixTool indicates an expected call of RawFixTool.
func (mr *MockQzonePhotoToolClientProxyMockRecorder) RawFixTool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawFixTool", reflect.TypeOf((*MockQzonePhotoToolClientProxy)(nil).RawFixTool), varargs...)
}
