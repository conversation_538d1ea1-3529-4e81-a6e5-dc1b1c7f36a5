# 使用官方 Go 镜像作为构建环境
FROM golang:1.23-bullseye AS builder

# 安装 OpenCV 依赖
RUN apt-get update && apt-get install -y \
    libopencv-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制依赖文件
COPY go.mod go.sum ./
COPY vendor/ ./vendor/
COPY stub/ ./stub/

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 go build -mod=vendor -o output_error_qq_photos main.go

# 运行阶段
FROM debian:bullseye-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libopencv-core4.5 \
    libopencv-imgproc4.5 \
    libopencv-imgcodecs4.5 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制应用程序和配置
COPY --from=builder /app/output_error_qq_photos .
COPY --from=builder /app/application.yaml .

# 运行应用
CMD ["./output_error_qq_photos"]
