#!/bin/bash

# OpenCV 故障排查脚本

CONTAINER_NAME="output_error_qq_photos"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查容器是否运行
check_container() {
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_error "容器 $CONTAINER_NAME 未运行"
        log_info "请先启动容器: docker start $CONTAINER_NAME"
        exit 1
    fi
    log_success "容器 $CONTAINER_NAME 正在运行"
}

# 检查 OpenCV 库文件
check_opencv_libs() {
    echo ""
    log_info "=== 检查 OpenCV 库文件 ==="
    
    docker exec "$CONTAINER_NAME" /bin/bash -c "
        echo '1. 检查 /usr/local/lib 中的 OpenCV 库:'
        find /usr/local/lib -name '*opencv*' -type f | sort
        echo ''
        
        echo '2. 检查系统库路径中的 OpenCV:'
        find /usr/lib -name '*opencv*' -type f 2>/dev/null | head -10 || echo '系统库路径中未找到 OpenCV'
        echo ''
        
        echo '3. 检查库文件权限:'
        ls -la /usr/local/lib/libopencv* | head -5 2>/dev/null || echo '未找到 OpenCV 库文件'
    "
}

# 检查环境变量
check_environment() {
    echo ""
    log_info "=== 检查环境变量 ==="
    
    docker exec "$CONTAINER_NAME" /bin/bash -c "
        echo '1. LD_LIBRARY_PATH:'
        echo \$LD_LIBRARY_PATH
        echo ''
        
        echo '2. PKG_CONFIG_PATH:'
        echo \$PKG_CONFIG_PATH
        echo ''
        
        echo '3. PATH:'
        echo \$PATH
        echo ''
        
        echo '4. 所有环境变量 (过滤 OpenCV 相关):'
        env | grep -i opencv || echo '未找到 OpenCV 相关环境变量'
    "
}

# 检查程序依赖
check_program_dependencies() {
    echo ""
    log_info "=== 检查程序依赖 ==="
    
    docker exec "$CONTAINER_NAME" /bin/bash -c "
        echo '1. 程序文件信息:'
        ls -la /app/output_error_qq_photos
        echo ''
        
        echo '2. 程序依赖库:'
        ldd /app/output_error_qq_photos
        echo ''
        
        echo '3. 缺失的依赖 (not found):'
        ldd /app/output_error_qq_photos | grep 'not found' || echo '所有依赖都已找到'
        echo ''
        
        echo '4. OpenCV 相关依赖:'
        ldd /app/output_error_qq_photos | grep -i opencv || echo '未找到 OpenCV 依赖'
    "
}

# 测试 OpenCV 功能
test_opencv_functionality() {
    echo ""
    log_info "=== 测试 OpenCV 功能 ==="
    
    docker exec "$CONTAINER_NAME" /bin/bash -c "
        echo '1. 创建简单的 OpenCV 测试程序:'
        cat > /tmp/opencv_test.cpp << 'EOF'
#include <opencv2/opencv.hpp>
#include <iostream>

int main() {
    std::cout << \"OpenCV Version: \" << CV_VERSION << std::endl;
    cv::Mat img = cv::Mat::zeros(100, 100, CV_8UC3);
    if (!img.empty()) {
        std::cout << \"OpenCV 基本功能正常\" << std::endl;
        return 0;
    } else {
        std::cout << \"OpenCV 基本功能异常\" << std::endl;
        return 1;
    }
}
EOF
        
        echo '2. 编译测试程序:'
        if command -v g++ &> /dev/null; then
            g++ -o /tmp/opencv_test /tmp/opencv_test.cpp \$(pkg-config --cflags --libs opencv4) 2>&1 || echo '编译失败'
            
            echo '3. 运行测试程序:'
            /tmp/opencv_test 2>&1 || echo '运行失败'
        else
            echo 'g++ 编译器不可用，跳过编译测试'
        fi
        
        echo ''
        echo '4. 检查 pkg-config:'
        pkg-config --modversion opencv4 2>&1 || echo 'pkg-config 无法找到 opencv4'
        
        echo ''
        echo '5. 检查 OpenCV 配置:'
        pkg-config --cflags opencv4 2>&1 || echo 'pkg-config 无法获取 OpenCV 编译标志'
    "
}

# 检查系统信息
check_system_info() {
    echo ""
    log_info "=== 检查系统信息 ==="
    
    docker exec "$CONTAINER_NAME" /bin/bash -c "
        echo '1. 操作系统信息:'
        cat /etc/os-release | head -5
        echo ''
        
        echo '2. 内核版本:'
        uname -a
        echo ''
        
        echo '3. 内存使用:'
        free -h
        echo ''
        
        echo '4. 磁盘使用:'
        df -h /
        echo ''
        
        echo '5. 进程信息:'
        ps aux | grep output_error_qq_photos | grep -v grep || echo '未找到应用进程'
    "
}

# 尝试修复常见问题
fix_common_issues() {
    echo ""
    log_info "=== 尝试修复常见问题 ==="
    
    docker exec "$CONTAINER_NAME" /bin/bash -c "
        echo '1. 更新动态链接器缓存:'
        ldconfig
        echo '动态链接器缓存已更新'
        echo ''
        
        echo '2. 检查修复后的依赖:'
        ldd /app/output_error_qq_photos | grep 'not found' || echo '所有依赖都已找到'
        echo ''
        
        echo '3. 尝试重新设置环境变量:'
        export LD_LIBRARY_PATH=/usr/local/lib:\$LD_LIBRARY_PATH
        export PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:\$PKG_CONFIG_PATH
        echo 'LD_LIBRARY_PATH=' \$LD_LIBRARY_PATH
        echo 'PKG_CONFIG_PATH=' \$PKG_CONFIG_PATH
    "
}

# 生成诊断报告
generate_report() {
    echo ""
    log_info "=== 生成诊断报告 ==="
    
    REPORT_FILE="opencv_diagnostic_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "OpenCV 诊断报告"
        echo "生成时间: $(date)"
        echo "容器名称: $CONTAINER_NAME"
        echo "========================================"
        echo ""
        
        echo "1. 容器状态:"
        docker ps --filter "name=$CONTAINER_NAME"
        echo ""
        
        echo "2. 镜像信息:"
        docker images | grep output_error_qq_photos
        echo ""
        
        echo "3. 容器日志 (最后50行):"
        docker logs --tail 50 "$CONTAINER_NAME"
        echo ""
        
    } > "$REPORT_FILE"
    
    log_success "诊断报告已生成: $REPORT_FILE"
}

# 显示修复建议
show_fix_suggestions() {
    echo ""
    log_info "=== 修复建议 ==="
    echo ""
    echo "如果遇到 OpenCV 相关问题，请尝试以下解决方案："
    echo ""
    echo "1. 🔧 重新构建镜像:"
    echo "   ./docker-build.sh"
    echo ""
    echo "2. 🔄 重启容器:"
    echo "   docker restart $CONTAINER_NAME"
    echo ""
    echo "3. 🛠️  手动设置环境变量:"
    echo "   docker exec $CONTAINER_NAME bash -c 'export LD_LIBRARY_PATH=/usr/local/lib && /app/output_error_qq_photos'"
    echo ""
    echo "4. 📦 检查镜像构建日志:"
    echo "   查看构建过程中是否有 OpenCV 安装错误"
    echo ""
    echo "5. 🔍 进入容器手动调试:"
    echo "   docker exec -it $CONTAINER_NAME /bin/bash"
    echo "   然后手动运行程序并查看错误信息"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "  OpenCV 故障排查工具"
    echo "========================================"
    
    case "${1:-all}" in
        "libs")
            check_container
            check_opencv_libs
            ;;
        "env")
            check_container
            check_environment
            ;;
        "deps")
            check_container
            check_program_dependencies
            ;;
        "test")
            check_container
            test_opencv_functionality
            ;;
        "system")
            check_container
            check_system_info
            ;;
        "fix")
            check_container
            fix_common_issues
            ;;
        "report")
            check_container
            generate_report
            ;;
        "all")
            check_container
            check_opencv_libs
            check_environment
            check_program_dependencies
            test_opencv_functionality
            check_system_info
            fix_common_issues
            generate_report
            show_fix_suggestions
            ;;
        "help"|"-h"|"--help")
            echo "使用方法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  all      执行所有检查 (默认)"
            echo "  libs     检查 OpenCV 库文件"
            echo "  env      检查环境变量"
            echo "  deps     检查程序依赖"
            echo "  test     测试 OpenCV 功能"
            echo "  system   检查系统信息"
            echo "  fix      尝试修复常见问题"
            echo "  report   生成诊断报告"
            echo "  help     显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0           # 执行所有检查"
            echo "  $0 libs      # 只检查库文件"
            echo "  $0 fix       # 尝试修复问题"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            echo "使用 $0 help 查看帮助信息"
            exit 1
            ;;
    esac
}

# 检查 Docker 是否可用
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装"
    exit 1
fi

if ! docker info &> /dev/null; then
    log_error "Docker 服务未启动"
    exit 1
fi

# 执行主函数
main "$@"
